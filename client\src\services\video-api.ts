import api from './api';

/**
 * Video API service
 */
export const videoAPI = {
  /**
   * Upload a new video
   */
  uploadVideo: async (videoData: {
    title: string;
    description: string;
    channelId: string;
    category: string;
    tags: string[];
    visibility: 'public' | 'unlisted' | 'private' | 'scheduled';
    scheduledPublishTime?: string;
    contentRating: 'general' | 'teen' | 'mature' | 'explicit';
    commentsEnabled?: boolean;
    ratingsEnabled?: boolean;
    embeddingEnabled?: boolean;
    chapters?: Array<{
      title: string;
      startTime: number;
      endTime: number;
    }>;
    location?: {
      latitude: number;
      longitude: number;
      name: string;
    };
    copyright?: {
      owner: string;
      license: string;
      allowReuse: boolean;
      allowCommercialUse: boolean;
      allowModification: boolean;
      attributionRequired: boolean;
    };
    file: {
      originalName: string;
      size: number;
      mimeType: string;
    };
    thumbnailUrl: string;
  }) => {
    const response = await api.post('/videos/upload', videoData);
    return response.data;
  },

  /**
   * Import a video from an external source
   */
  importVideo: async (videoData: {
    title: string;
    description: string;
    channelId: string;
    category: string;
    tags: string[];
    visibility: 'public' | 'unlisted' | 'private' | 'scheduled';
    scheduledPublishTime?: string;
    contentRating: 'general' | 'teen' | 'mature' | 'explicit';
    commentsEnabled?: boolean;
    ratingsEnabled?: boolean;
    embeddingEnabled?: boolean;
    chapters?: Array<{
      title: string;
      startTime: number;
      endTime: number;
    }>;
    source: {
      type: 'import' | 'embed';
      originalUrl: string;
      platform: string;
      externalId?: string;
    };
    thumbnailUrl?: string;
    duration?: number;
  }) => {
    try {
      console.log('Importing video with data:', JSON.stringify(videoData));
      const response = await api.post('/videos/import', videoData);
      console.log('Import video response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error importing video:', error);
      if (error.response) {
        console.error('Response status:', error.response.status);
        console.error('Response data:', error.response.data);
      }
      throw error;
    }
  },

  /**
   * Update an existing video
   */
  updateVideo: async (videoId: string, videoData: {
    title?: string;
    description?: string;
    channelId?: string;
    category?: string;
    tags?: string[];
    visibility?: 'public' | 'unlisted' | 'private' | 'scheduled';
    scheduledPublishTime?: string;
    contentRating?: 'general' | 'teen' | 'mature' | 'explicit';
    commentsEnabled?: boolean;
    ratingsEnabled?: boolean;
    embeddingEnabled?: boolean;
    chapters?: Array<{
      title: string;
      startTime: number;
      endTime: number;
    }>;
    location?: {
      latitude: number;
      longitude: number;
      name: string;
    };
    copyright?: {
      owner: string;
      license: string;
      allowReuse: boolean;
      allowCommercialUse: boolean;
      allowModification: boolean;
      attributionRequired: boolean;
    };
    thumbnailUrl?: string;
    languages?: Array<{
      code: string;
      name: string;
      flag?: string;
      url?: string;
      isDefault?: boolean;
    }>;
  }) => {
    try {
      // Process languages to ensure URLs are valid Engaxe IDs
      if (videoData.languages && videoData.languages.length > 0) {
        videoData.languages = videoData.languages.map(lang => {
          if (lang.url) {
            // Check if URL is a full Engaxe URL
            if (lang.url.includes('engaxe.com')) {
              // Try to extract the ID using regex
              const match = lang.url.match(/(?:https?:\/\/)?(?:www\.)?engaxe\.com\/v\/([^/?]+)/i);
              if (match && match[1]) {
                console.log(`Extracted ID from language URL: ${match[1]} (original: ${lang.url})`);
                return { ...lang, url: match[1] };
              }
            }

            // Try a more aggressive approach for URLs like https://engaxe.com/v/XjKFqQ
            if (lang.url.includes('/')) {
              const engaxeIdMatch = lang.url.match(/\/([a-zA-Z0-9]{6,7})(?:\/|$)/);
              if (engaxeIdMatch && engaxeIdMatch[1]) {
                console.log(`Extracted ID using fallback method: ${engaxeIdMatch[1]} (original: ${lang.url})`);
                return { ...lang, url: engaxeIdMatch[1] };
              }
            }
          }
          return lang;
        });
      }

      // Check if videoId is a hash ID (32 hex characters)
      if (/^[a-f0-9]{32}$/.test(videoId)) {
        console.log(`Video ID ${videoId} appears to be a hash ID, trying to find the Engaxe ID`);

        // Try to get the video from the API to find its Engaxe ID
        try {
          const response = await api.get('/videos', {
            params: { limit: 50, sort: 'newest' }
          });

          if (response.data && response.data.videos) {
            const video = response.data.videos.find(v => v.id === videoId);
            if (video && video.url) {
              console.log(`Found Engaxe ID ${video.url} for hash ID ${videoId}`);
              videoId = video.url;
            }
          }
        } catch (findError) {
          console.error('Error finding Engaxe ID for hash ID:', findError);
          // Continue with the original ID
        }
      }

      console.log(`Updating video ${videoId} with data:`, videoData);
      const response = await api.put(`/videos/${videoId}`, videoData);
      console.log(`Update video response:`, response.data);
      return response.data;
    } catch (error) {
      console.error(`Error updating video ${videoId}:`, error);
      if (error.response) {
        console.error('Response status:', error.response.status);
        console.error('Response data:', error.response.data);
      }
      throw error;
    }
  },

  /**
   * Get a video by ID
   */
  getVideoById: async (videoId: string, incrementViews = false) => {
    const response = await api.get(`/videos/${videoId}`, {
      params: { view: incrementViews },
    });
    return response.data;
  },

  /**
   * Get video metadata from Engaxe
   */
  getVideoMetadata: async (videoId: string) => {
    try {
      console.log(`Fetching metadata for Engaxe video ID: ${videoId}`);
      const response = await api.get(`/videos/metadata/${videoId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching video metadata for ${videoId}:`, error);
      throw error;
    }
  },

  /**
   * Get videos with pagination and filtering
   */
  getVideos: async (params?: {
    page?: number;
    limit?: number;
    sort?: 'newest' | 'popular' | 'trending';
    category?: string;
    channelId?: string;
    userId?: string;
    search?: string;
    tags?: string[];
    contentRating?: 'general' | 'teen' | 'mature' | 'explicit';
    _t?: number; // Timestamp for cache busting
  }) => {
    try {
      // Make sure limit is a number to match server validation
      const modifiedParams = { ...params };
      if (modifiedParams.limit) {
        modifiedParams.limit = Number(modifiedParams.limit);
      }

      // Add timestamp for cache busting if not already provided
      if (!modifiedParams._t) {
        modifiedParams._t = new Date().getTime();
      }

      console.log(`Fetching videos with params:`, modifiedParams);

      // Log the full URL being requested
      const baseURL = api.defaults.baseURL || '';
      const url = `${baseURL}/videos`;
      console.log(`Making request to: ${url}`);

      const response = await api.get('/videos', {
        params: modifiedParams,
        // Disable caching
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });

      console.log(`API Response status:`, response.status);
      console.log(`API Response data:`, response.data);

      // Check if the response has the expected structure
      if (!response.data) {
        console.error('API response missing data:', response);
        return {
          success: false,
          message: 'API response missing data',
          videos: []
        };
      }

      if (!response.data.videos) {
        console.error('API response missing videos array:', response.data);
        return {
          success: false,
          message: 'API response missing videos array',
          videos: []
        };
      }

      console.log(`Received ${response.data.videos.length || 0} videos from API`);

      // Log the first few videos for debugging
      if (response.data.videos.length > 0) {
        console.log('First video:', response.data.videos[0]);
      }

      return response.data;
    } catch (error) {
      console.error('Error fetching videos:', error);

      // Log more detailed error information
      if (error.response) {
        console.error('Response status:', error.response.status);
        console.error('Response data:', error.response.data);
      } else if (error.request) {
        console.error('No response received. Request details:', error.request);
      } else {
        console.error('Error setting up request:', error.message);
      }

      // Return a default response structure
      return {
        success: false,
        message: 'Failed to fetch videos',
        videos: []
      };
    }
  },

  /**
   * Get videos by channel
   */
  getChannelVideos: async (channelId: string, params?: {
    page?: number;
    limit?: number;
    sort?: 'newest' | 'popular' | 'oldest';
  }) => {
    const response = await api.get(`/videos/channel/${channelId}`, { params });
    return response.data;
  },

  /**
   * Save Engaxe video to database
   */
  saveEngaxeVideo: async (videoId: string, channelId: string, languages?: Array<{
    code: string;
    name: string;
    flag?: string;
    url?: string;
    isDefault?: boolean;
  }>) => {
    try {
      // Validate inputs
      if (!videoId) {
        throw new Error('Video ID is required');
      }

      if (!channelId) {
        throw new Error('Channel ID is required');
      }

      console.log(`Saving Engaxe video to database: videoId=${videoId}, channelId=${channelId}`);

      // Extract video ID from URL if needed
      let processedVideoId = videoId;

      // Check if it's a URL
      if (videoId.includes('engaxe.com')) {
        // Try to extract the ID using regex
        const match = videoId.match(/(?:https?:\/\/)?(?:www\.)?engaxe\.com\/v\/([^/?]+)/i);
        if (match && match[1]) {
          processedVideoId = match[1];
          console.log(`Extracted video ID from URL: ${processedVideoId} (original: ${videoId})`);
        }
      }

      // Validate that the ID is a valid 6-7 character Engaxe ID
      const isValidEngaxeId = /^[a-zA-Z0-9]{6,7}$/.test(processedVideoId);
      if (!isValidEngaxeId) {
        console.error(`Invalid Engaxe ID format: ${processedVideoId}. Must be 6-7 alphanumeric characters.`);
        throw new Error('Invalid Engaxe ID format. Must be 6-7 alphanumeric characters.');
      }

      // Process languages to ensure URLs are valid Engaxe IDs
      let processedLanguages = languages || [];

      if (processedLanguages.length > 0) {
        console.log(`Processing ${processedLanguages.length} languages before sending to server`);

        processedLanguages = processedLanguages.map((lang, idx) => {
          // Create a copy to avoid mutating the original
          const processedLang = { ...lang };

          console.log(`Processing language ${idx + 1}/${processedLanguages.length}: ${processedLang.name} (${processedLang.code}), URL: ${processedLang.url || 'none'}, isDefault: ${processedLang.isDefault || false}, flag: ${processedLang.flag || 'none'}`);

          // Ensure language has a URL
          if (processedLang.url) {
            // Check if URL is a full Engaxe URL
            if (processedLang.url.includes('engaxe.com')) {
              // Try to extract the ID using regex
              const match = processedLang.url.match(/(?:https?:\/\/)?(?:www\.)?engaxe\.com\/v\/([a-zA-Z0-9]{6,7})/i);
              if (match && match[1]) {
                console.log(`Extracted ID from language URL: ${match[1]} (original: ${processedLang.url})`);
                processedLang.url = match[1];
              } else {
                // Try a more aggressive approach for URLs
                const engaxeIdMatch = processedLang.url.match(/\/([a-zA-Z0-9]{6,7})(?:\/|$)/);
                if (engaxeIdMatch && engaxeIdMatch[1]) {
                  console.log(`Extracted ID using fallback method: ${engaxeIdMatch[1]} (original: ${processedLang.url})`);
                  processedLang.url = engaxeIdMatch[1];
                }
              }
            }

            // Validate that the URL is a valid Engaxe ID
            if (!/^[a-zA-Z0-9]{6,7}$/.test(processedLang.url)) {
              console.warn(`Language ${processedLang.name} has invalid URL format: ${processedLang.url}. Using video ID instead.`);
              processedLang.url = processedVideoId;
            }
          } else {
            // If no URL, use the video ID
            console.log(`Language ${processedLang.name} has no URL, using video ID: ${processedVideoId}`);
            processedLang.url = processedVideoId;
          }

          // Ensure language has a flag
          if (!processedLang.flag) {
            switch (processedLang.code) {
              case 'en': processedLang.flag = '🇺🇸'; break;
              case 'hi': processedLang.flag = '🇮🇳'; break;
              case 'es': processedLang.flag = '🇪🇸'; break;
              case 'fr': processedLang.flag = '🇫🇷'; break;
              case 'de': processedLang.flag = '🇩🇪'; break;
              case 'ja': processedLang.flag = '🇯🇵'; break;
              case 'zh': processedLang.flag = '🇨🇳'; break;
              case 'ru': processedLang.flag = '🇷🇺'; break;
              case 'ar': processedLang.flag = '🇸🇦'; break;
              case 'pt': processedLang.flag = '🇵🇹'; break;
              case 'it': processedLang.flag = '🇮🇹'; break;
              case 'nl': processedLang.flag = '🇳🇱'; break;
              case 'ko': processedLang.flag = '🇰🇷'; break;
              case 'tr': processedLang.flag = '🇹🇷'; break;
              default: processedLang.flag = '🌐'; break;
            }
            console.log(`Added flag ${processedLang.flag} for language ${processedLang.name}`);
          }

          // Ensure isDefault is a boolean
          processedLang.isDefault = !!processedLang.isDefault;

          console.log(`Processed language ${idx + 1}: ${processedLang.name} (${processedLang.code}), URL: ${processedLang.url}, isDefault: ${processedLang.isDefault}, flag: ${processedLang.flag}`);
          return processedLang;
        });

        // Ensure at least one language is marked as default
        const hasDefaultLanguage = processedLanguages.some(lang => lang.isDefault);
        if (!hasDefaultLanguage && processedLanguages.length > 0) {
          console.log(`No default language found, setting ${processedLanguages[0].name} as default`);
          processedLanguages[0].isDefault = true;
        }
      } else {
        // If no languages provided, create a default English language
        processedLanguages = [{
          code: 'en',
          name: 'English',
          flag: '🇺🇸',
          url: processedVideoId,
          isDefault: true
        }];
        console.log('No languages provided, created default English language');
      }

      // Simplified payload with processed fields
      const payload = {
        videoId: processedVideoId,
        channelId,
        languages: processedLanguages
      };

      console.log('Processed languages in payload:', JSON.stringify(processedLanguages, null, 2));
      console.log('Final request payload:', JSON.stringify(payload, null, 2));

      // Try to save the video
      try {
        // Make sure we're using the correct endpoint
        const response = await api.post('/videos/save-engaxe', payload);
        console.log('Save Engaxe video response:', response.data);
        return response.data;
      } catch (saveError) {
        console.error('Error in initial save attempt:', saveError);

        // Log more detailed error information
        if (saveError.response) {
          console.error('Response status:', saveError.response.status);
          console.error('Response data:', saveError.response.data);
        } else if (saveError.request) {
          console.error('No response received. Request details:', saveError.request);
        } else {
          console.error('Error setting up request:', saveError.message);
        }

        // If we get a channel not found error, try to create a channel first
        if (saveError.response &&
            (saveError.response.data?.error?.code === 'CHANNEL_NOT_FOUND' ||
             saveError.response.data?.error?.message?.includes('Channel not found'))) {

          console.log('Channel not found error detected. Attempting to create a default channel...');

          // Get current user from localStorage
          const userJson = localStorage.getItem('lawengaxe-user');
          if (!userJson) {
            throw new Error('User not found in local storage. Please log in again.');
          }

          const user = JSON.parse(userJson);
          if (!user || !user.id) {
            throw new Error('Invalid user data in local storage. Please log in again.');
          }

          // Create a default channel
          const channelData = {
            name: `channel-${user.id.substring(0, 8)}`,
            displayName: 'My Default Channel',
            description: 'My default channel created automatically',
            visibility: 'public',
            tags: []
          };

          console.log('Creating default channel with data:', channelData);

          // Import the channel API
          const { channelAPI } = await import('./channel-api');
          const createResponse = await channelAPI.createChannel(channelData);

          if (createResponse.success && createResponse.channel) {
            console.log('Successfully created default channel:', createResponse.channel);

            // Try saving the video again with the new channel ID
            const newPayload = {
              ...payload,
              channelId: createResponse.channel.id,
              languages: languages || []
            };

            console.log('Retrying save with new channel ID:', newPayload);
            const retryResponse = await api.post('/videos/save-engaxe', newPayload);
            console.log('Retry save response:', retryResponse.data);
            return retryResponse.data;
          } else {
            console.error('Failed to create default channel:', createResponse);
            throw new Error('Could not create a default channel. Please create a channel manually.');
          }
        } else {
          // If it's not a channel not found error, rethrow
          throw saveError;
        }
      }
    } catch (error) {
      console.error('Error saving Engaxe video:', error);
      if (error.response) {
        console.error('Response status:', error.response.status);
        console.error('Response data:', error.response.data);

        // Extract error message from response if available
        const errorMessage = error.response.data?.error?.message ||
                            error.response.data?.message ||
                            'Failed to save Engaxe video';

        throw new Error(errorMessage);
      }
      throw error;
    }
  },

  /**
   * Delete a video
   */
  deleteVideo: async (videoId: string) => {
    const response = await api.delete(`/videos/${videoId}`);
    return response.data;
  },

  /**
   * Update languages for all videos
   * This endpoint will add default languages to videos that don't have any
   * and ensure all language URLs are valid Engaxe IDs
   */
  updateAllVideoLanguages: async () => {
    try {
      console.log('Updating languages for all videos');
      const response = await api.post('/videos/update-languages');
      console.log('Update languages response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error updating video languages:', error);
      if (error.response) {
        console.error('Response status:', error.response.status);
        console.error('Response data:', error.response.data);
      }
      throw error;
    }
  },

  /**
   * Fix languages for all videos
   * This is a more comprehensive fix that will add default languages to videos that don't have any,
   * ensure all language URLs are valid Engaxe IDs, and fix any other language-related issues
   */
  fixAllVideoLanguages: async () => {
    try {
      console.log('Fixing languages for all videos');

      // First try the simple fix endpoint
      try {
        console.log('Trying simple-fix endpoint...');
        const response = await api.post('/simple-fix/languages');
        console.log('Simple fix response:', response.data);
        return response.data;
      } catch (simpleFixError) {
        console.warn('Error using simple-fix endpoint, trying fix-languages endpoint:', simpleFixError);

        // Log more details about the error
        if (simpleFixError.response) {
          console.warn('Response status:', simpleFixError.response.status);
          console.warn('Response data:', simpleFixError.response.data);
        } else if (simpleFixError.request) {
          console.warn('No response received:', simpleFixError.request);
        } else {
          console.warn('Error message:', simpleFixError.message);
        }

        // Try the fix-languages endpoint
        try {
          console.log('Trying fix-languages endpoint...');
          const response = await api.post('/fix-languages/fix-all');
          console.log('Fix languages response:', response.data);
          return response.data;
        } catch (fixError) {
          console.warn('Error using fix-languages endpoint, falling back to update-languages:', fixError);

          // Log more details about the error
          if (fixError.response) {
            console.warn('Response status:', fixError.response.status);
            console.warn('Response data:', fixError.response.data);
          } else if (fixError.request) {
            console.warn('No response received:', fixError.request);
          } else {
            console.warn('Error message:', fixError.message);
          }

          // Fall back to the update-languages endpoint if the fix-languages endpoint fails
          console.log('Trying update-languages endpoint...');
          const fallbackResponse = await api.post('/videos/update-languages');
          console.log('Update languages fallback response:', fallbackResponse.data);
          return fallbackResponse.data;
        }
      }
    } catch (error) {
      console.error('Error fixing video languages:', error);
      if (error.response) {
        console.error('Response status:', error.response.status);
        console.error('Response data:', error.response.data);
      } else if (error.request) {
        console.error('No response received:', error.request);
      } else {
        console.error('Error message:', error.message);
      }
      throw error;
    }
  },
};

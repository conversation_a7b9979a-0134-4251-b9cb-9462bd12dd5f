import { FastifyInstance, FastifyPluginAsync } from 'fastify';
import { MetadataController } from '../controllers/metadata.controller';
import { Type } from '@sinclair/typebox';
import { optionalAuthenticate } from '../middleware/auth';

/**
 * Routes for metadata-related endpoints
 */
const metadataRoutes: FastifyPluginAsync = async (fastify: FastifyInstance) => {
  const metadataController = new MetadataController();

  // Fetch video metadata from a URL
  fastify.get<{
    Querystring: {
      url: string;
    }
  }>(
    '/video',
    {
      preHandler: optionalAuthenticate,
      schema: {
        querystring: Type.Object({
          url: Type.String()
        }),
        response: {
          200: Type.Object({
            success: Type.Boolean(),
            metadata: Type.Object({
              title: Type.String(),
              description: Type.String(),
              thumbnailUrl: Type.String(),
              embedUrl: Type.String(),
              platform: Type.String(),
              externalId: Type.String(),
              originalUrl: Type.String(),
              author: Type.String(),
              authorUrl: Type.String(),
              duration: Type.Optional(Type.Number()),
              views: Type.Optional(Type.Number()),
              likes: Type.Optional(Type.Number()),
              dislikes: Type.Optional(Type.Number()),
              publishedAt: Type.Optional(Type.String()),
              category: Type.Optional(Type.String()),
              tags: Type.Optional(Type.Array(Type.String())),
              videoUrl: Type.Optional(Type.String())
            })
          }),
          400: Type.Object({
            success: Type.Boolean(),
            message: Type.String(),
            error: Type.Object({
              code: Type.String(),
              message: Type.String()
            })
          }),
          500: Type.Object({
            success: Type.Boolean(),
            message: Type.String(),
            error: Type.Object({
              code: Type.String(),
              message: Type.String()
            })
          })
        }
      },
    },
    (request, reply) => metadataController.fetchVideoMetadata(request, reply)
  );
};

export default metadataRoutes;

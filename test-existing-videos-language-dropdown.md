# Testing Language Dropdown for Existing Videos

This document outlines the steps to test the updated language dropdown functionality for existing videos.

## Test Plan

1. Open an existing video with multiple languages
2. Verify the language dropdown shows all available languages
3. Test language switching during playback

## Step 1: Open an Existing Video with Multiple Languages

1. Navigate to the Home page
2. Find an existing video that has multiple languages (look for the language count badge)
3. Click on the video to open the video page

## Step 2: Verify Language Dropdown Shows All Languages

1. Open the browser console to monitor the logs
2. Look for logs like:
   - `Video has X languages:`
   - `Language 1: English (en), URL: 7mXOfb, Default: Yes`
   - `Language 2: Hindi (hi), URL: tzbtmk, Default: No`
3. Click on the language dropdown button
4. Verify that the dropdown shows all languages for the video
5. Verify that the dropdown header shows "Select Language (X available)" where X is the number of languages
6. Verify that each language in the dropdown has:
   - A flag icon
   - The language name
   - A "Default" label for the default language
   - A dot indicator for the currently selected language

## Step 3: Test Language Switching During Playback

1. Note which language is currently selected (should be the default language)
2. Click on a different language in the dropdown
3. Verify in the console that:
   - The URL is properly extracted: `Extracted Engaxe ID from language URL: [ID] (original: [URL])`
   - The iframe source is updated: `Updating iframe source to: https://engaxe.com/e/[ID]`
4. Verify that the video source changes to the selected language
5. Verify that the language dropdown button now shows the selected language
6. Click on the language dropdown button again
7. Verify that the selected language has a dot indicator in the dropdown
8. Select another language and verify the same behavior

## Expected Results

- The language dropdown should show all available languages for the video
- The dropdown header should show the correct count of available languages
- Each language in the dropdown should have a flag icon and name
- The default language should be marked with "(Default)"
- The currently selected language should have a dot indicator
- When switching languages, the video source should change to the corresponding Engaxe URL
- The console should show the proper extraction of Engaxe IDs from URLs

import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { channelAPI } from '@/services/api';
import ChannelGrid from '@/components/channels/ChannelGrid';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import Layout from '@/components/layout/Layout';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Search, Plus } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import { toast } from 'sonner';

const ChannelsPage: React.FC = () => {
  const { currentUser } = useAuth();
  const [searchParams, setSearchParams] = useSearchParams();
  const [channels, setChannels] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [subscribedChannels, setSubscribedChannels] = useState<string[]>([]);
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    limit: 20,
    pages: 1,
  });

  // Get query parameters
  const page = Number(searchParams.get('page') || '1');
  const sort = searchParams.get('sort') || 'newest';
  const category = searchParams.get('category') || '';
  const search = searchParams.get('search') || '';
  const featured = searchParams.get('featured') === 'true';
  const tab = searchParams.get('tab') || 'all';

  // Fetch channels
  const fetchChannels = async () => {
    try {
      setIsLoading(true);

      const params: any = {
        page,
        sort,
        limit: 20,
      };

      if (category) params.category = category;
      if (search) params.search = search;
      if (featured) params.featured = true;

      const response = await channelAPI.getChannels(params);

      if (response.success) {
        setChannels(response.channels);
        setPagination(response.pagination);
      } else {
        toast.error('Failed to load channels');
      }
    } catch (error) {
      console.error('Error fetching channels:', error);
      toast.error('Failed to load channels');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle subscription toggle
  const handleSubscribe = async (channelId: string) => {
    if (!currentUser) {
      toast.error('Please sign in to subscribe to channels');
      return;
    }

    try {
      const response = await channelAPI.toggleSubscription(channelId);

      if (response.success) {
        if (response.isSubscribed) {
          setSubscribedChannels(prev => [...prev, channelId]);
          toast.success('Subscribed to channel');
        } else {
          setSubscribedChannels(prev => prev.filter(id => id !== channelId));
          toast.success('Unsubscribed from channel');
        }
      } else {
        toast.error(response.message || 'Failed to update subscription');
      }
    } catch (error: any) {
      console.error('Error updating subscription:', error);
      toast.error(error.message || 'Failed to update subscription');
    }
  };

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    const formData = new FormData(e.target as HTMLFormElement);
    const searchTerm = formData.get('search') as string;

    setSearchParams(prev => {
      const newParams = new URLSearchParams(prev);
      if (searchTerm) {
        newParams.set('search', searchTerm);
      } else {
        newParams.delete('search');
      }
      newParams.set('page', '1');
      return newParams;
    });
  };

  // Handle sort change
  const handleSortChange = (value: string) => {
    setSearchParams(prev => {
      const newParams = new URLSearchParams(prev);
      newParams.set('sort', value);
      newParams.set('page', '1');
      return newParams;
    });
  };

  // Handle tab change
  const handleTabChange = (value: string) => {
    setSearchParams(prev => {
      const newParams = new URLSearchParams(prev);
      newParams.set('tab', value);

      if (value === 'featured') {
        newParams.set('featured', 'true');
      } else {
        newParams.delete('featured');
      }

      newParams.set('page', '1');
      return newParams;
    });
  };

  // Handle pagination
  const handlePageChange = (newPage: number) => {
    if (newPage < 1 || newPage > pagination.pages) return;

    setSearchParams(prev => {
      const newParams = new URLSearchParams(prev);
      newParams.set('page', newPage.toString());
      return newParams;
    });
  };

  // Fetch channels when parameters change
  useEffect(() => {
    fetchChannels();
  }, [page, sort, category, search, featured]);

  return (
    <Layout>
      <div className="container py-8">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
        <h1 className="text-3xl font-bold">Channels</h1>

        {currentUser && (
          <Button asChild>
            <a href="/create-channel">
              <Plus className="h-4 w-4 mr-2" />
              Create Channel
            </a>
          </Button>
        )}
      </div>

      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <Tabs value={tab} onValueChange={handleTabChange} className="w-full md:w-auto">
          <TabsList>
            <TabsTrigger value="all">All Channels</TabsTrigger>
            <TabsTrigger value="featured">Featured</TabsTrigger>
          </TabsList>
        </Tabs>

        <div className="flex-1 flex flex-col sm:flex-row gap-2">
          <form onSubmit={handleSearch} className="flex-1 flex gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                name="search"
                placeholder="Search channels..."
                className="pl-8"
                defaultValue={search}
              />
            </div>
            <Button type="submit">Search</Button>
          </form>

          <Select value={sort} onValueChange={handleSortChange}>
            <SelectTrigger className="w-full sm:w-[180px]">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="newest">Newest</SelectItem>
              <SelectItem value="popular">Most Popular</SelectItem>
              <SelectItem value="trending">Trending</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <ChannelGrid
        channels={channels}
        isLoading={isLoading}
        emptyMessage={search ? `No channels found for "${search}"` : 'No channels found'}
        onSubscribe={handleSubscribe}
        subscribedChannels={subscribedChannels}
      />

      {/* Pagination */}
      {pagination.pages > 1 && (
        <div className="flex justify-center mt-8">
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => handlePageChange(page - 1)}
              disabled={page <= 1}
            >
              Previous
            </Button>

            <div className="flex items-center gap-1">
              {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
                let pageNum = page;
                if (pagination.pages <= 5) {
                  pageNum = i + 1;
                } else if (page <= 3) {
                  pageNum = i + 1;
                } else if (page >= pagination.pages - 2) {
                  pageNum = pagination.pages - 4 + i;
                } else {
                  pageNum = page - 2 + i;
                }

                return (
                  <Button
                    key={pageNum}
                    variant={pageNum === page ? 'default' : 'outline'}
                    onClick={() => handlePageChange(pageNum)}
                    className="w-10 h-10 p-0"
                  >
                    {pageNum}
                  </Button>
                );
              })}
            </div>

            <Button
              variant="outline"
              onClick={() => handlePageChange(page + 1)}
              disabled={page >= pagination.pages}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
    </Layout>
  );
};

export default ChannelsPage;

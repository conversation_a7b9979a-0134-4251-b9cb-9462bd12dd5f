/**
 * Simple script to run the fix-videos.ts script using ts-node
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('Running video URL migration script...');

// Run the script using ts-node
const scriptPath = path.join(__dirname, 'src', 'scripts', 'fix-videos.ts');
const child = spawn('npx', ['ts-node', scriptPath], {
  stdio: 'inherit',
  shell: true
});

child.on('close', (code) => {
  if (code === 0) {
    console.log('Migration completed successfully!');
  } else {
    console.error(`Migration failed with code ${code}`);
  }
});

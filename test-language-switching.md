# Testing Multiple Languages with Engaxe URLs

This document outlines the steps to test the implementation of multiple languages with Engaxe video URLs.

## Test Plan

1. Add a new video with multiple languages
2. Verify the language count shows on the home page
3. Test language switching during playback

## Step 1: Add a New Video with Multiple Languages

1. Navigate to the Creator Studio page
2. Click on "Add Video"
3. Enter the following information:
   - Engaxe URL: `https://engaxe.com/v/XLcMq2` (or any valid Engaxe URL)
   - Title: "Test Video with Multiple Languages"
   - Description: "This is a test video with multiple languages"
   - Category: "Education"
   - Default Language: "English"
   - Default Language URL: `https://engaxe.com/v/XLcMq2`
4. Add additional languages:
   - Spanish with URL: `https://engaxe.com/v/xW36l7`
   - French with URL: `https://engaxe.com/v/suZKhW`
5. Save the video

## Step 2: Verify Language Count on Home Page

1. Navigate to the Home page
2. Find the newly added video
3. Verify that the language count badge shows "3 languages"

## Step 3: Test Language Switching During Playback

1. Click on the video to open the video page
2. Verify that the language selector in the top-right corner of the video player shows the default language (English)
3. Click on the language selector and choose Spanish
4. Verify that the video source changes to the Spanish version
5. Click on the language selector again and choose French
6. Verify that the video source changes to the French version

## Expected Results

- The video should be saved with all three languages
- The home page should display the correct language count (3 languages)
- When switching languages during playback, the video source should change to the corresponding Engaxe URL


import { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';

export default function SignUpForm() {
  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [engaxeUsername, setEngaxeUsername] = useState('');
  const [engaxePassword, setEngaxePassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showEngaxeForm, setShowEngaxeForm] = useState(false);
  const navigate = useNavigate();
  const { signup, loginWithEngaxe } = useAuth();
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!username || !email || !firstName || !lastName || !password || !confirmPassword) {
      toast({
        title: "Error",
        description: "Please fill in all fields",
        variant: "destructive"
      });
      return;
    }

    if (password !== confirmPassword) {
      toast({
        title: "Error",
        description: "Passwords do not match",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);
    try {
      // Create a display name from first and last name
      const displayName = `${firstName} ${lastName}`;

      await signup({
        username,
        email,
        password,
        firstName,
        lastName,
        displayName
      });

      toast({
        title: "Success",
        description: "Your account has been created",
      });
      navigate('/');
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to sign up",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleEngaxeLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!engaxeUsername || !engaxePassword) {
      toast({
        title: "Error",
        description: "Please fill in all Engaxe fields",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);
    try {
      await loginWithEngaxe(engaxeUsername, engaxePassword);
      toast({
        title: "Success",
        description: "You have been signed up with Engaxe",
      });
      navigate('/');
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to sign up with Engaxe",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const toggleEngaxeForm = () => {
    setShowEngaxeForm(!showEngaxeForm);
  };

  return (
    <Card className="w-full max-w-md mx-auto bg-lingstream-card border-gray-700">
      <CardHeader>
        <CardTitle className="text-2xl font-bold">Create an account</CardTitle>
        <CardDescription>Enter your details to create a new account</CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit}>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="username">Username</Label>
              <Input
                id="username"
                placeholder="Enter your username"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                className="bg-gray-800 border-gray-700"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                placeholder="Enter your email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="bg-gray-800 border-gray-700"
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="firstName">First Name</Label>
                <Input
                  id="firstName"
                  placeholder="First name"
                  value={firstName}
                  onChange={(e) => setFirstName(e.target.value)}
                  className="bg-gray-800 border-gray-700"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="lastName">Last Name</Label>
                <Input
                  id="lastName"
                  placeholder="Last name"
                  value={lastName}
                  onChange={(e) => setLastName(e.target.value)}
                  className="bg-gray-800 border-gray-700"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                placeholder="Create a password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="bg-gray-800 border-gray-700"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="confirmPassword">Confirm Password</Label>
              <Input
                id="confirmPassword"
                type="password"
                placeholder="Confirm your password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="bg-gray-800 border-gray-700"
              />
            </div>
            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? 'Creating account...' : 'Sign Up'}
            </Button>
          </div>
        </form>
        <div className="mt-4 relative">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-700" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-lingstream-card px-2 text-lingstream-muted">
              Or continue with
            </span>
          </div>
        </div>
        <Button
          variant="outline"
          onClick={toggleEngaxeForm}
          className="w-full mt-4 border-gray-700 hover:bg-lingstream-card hover:text-white"
          disabled={isLoading}
        >
          <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" />
            <path d="M8 12L10.5 14.5L16 9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
          </svg>
          {showEngaxeForm ? 'Hide Engaxe Login' : 'Sign up with Engaxe'}
        </Button>

        {showEngaxeForm && (
          <form onSubmit={handleEngaxeLogin} className="mt-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="engaxeUsername">Engaxe Username</Label>
                <Input
                  id="engaxeUsername"
                  type="text"
                  placeholder="Enter your Engaxe username"
                  value={engaxeUsername}
                  onChange={(e) => setEngaxeUsername(e.target.value)}
                  className="bg-gray-800 border-gray-700"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="engaxePassword">Engaxe Password</Label>
                <Input
                  id="engaxePassword"
                  type="password"
                  placeholder="Enter your Engaxe password"
                  value={engaxePassword}
                  onChange={(e) => setEngaxePassword(e.target.value)}
                  className="bg-gray-800 border-gray-700"
                />
              </div>
              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? 'Signing up...' : 'Sign Up with Engaxe'}
              </Button>
            </div>
          </form>
        )}
      </CardContent>
      <CardFooter className="flex justify-center">
        <p className="text-sm text-lingstream-muted">
          Already have an account?{' '}
          <Link to="/signin" className="font-medium text-lingstream-accent hover:underline">
            Sign in
          </Link>
        </p>
      </CardFooter>
    </Card>
  );
}

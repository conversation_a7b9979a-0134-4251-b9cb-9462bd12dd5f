import axios from 'axios';
import { AppError, createInternalServerError } from '../utils/error';

/**
 * AI Assistant service for handling AI-related operations
 */
export class AIAssistantService {
  /**
   * Send a message to the AI Assistant
   */
  async sendMessage(data: {
    message: string;
    provider: string;
    apiKey: string;
    endpoint?: string;
    conversationId?: string;
    creatorId: string;
    creatorName?: string; // Added creator name for personalized responses
    videoId?: string;
    chatHistory?: Array<{
      role: 'user' | 'assistant';
      content: string;
    }>;
  }) {
    try {
      let response;

      // Format chat history for the API request
      const formattedHistory = data.chatHistory || [];

      // Add the current message to the history
      formattedHistory.push({
        role: 'user',
        content: data.message
      });

      // Use creator name if provided, otherwise use a generic fallback
      const creatorName = data.creatorName || 'Video Creator';

      switch (data.provider) {
        case 'openai':
          response = await this.callOpenAI(data.apiKey, formattedHistory, data.creatorId, creatorName);
          break;
        case 'deepseek':
          response = await this.callDeepSeek(data.apiKey, formattedHistory, data.creatorId, creatorName);
          break;
        case 'bhashini':
          response = await this.callBhashini(data.apiKey, formattedHistory, data.creatorId, creatorName);
          break;
        case 'custom':
          if (!data.endpoint) {
            throw new AppError('Custom endpoint URL is required for custom provider', 400);
          }
          response = await this.callCustomAPI(data.endpoint, data.apiKey, formattedHistory, data.creatorId, creatorName);
          break;
        default:
          throw new AppError(`Unsupported AI provider: ${data.provider}`, 400);
      }

      return {
        response: response.content,
        conversationId: data.conversationId || `conv_${Date.now()}`,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error in chat service:', error);
      throw createInternalServerError('Failed to process chat message');
    }
  }

  /**
   * Call OpenAI API
   */
  private async callOpenAI(
    apiKey: string,
    messages: Array<{ role: 'user' | 'assistant'; content: string }>,
    creatorId: string,
    creatorName: string
  ) {
    try {
      // In a real implementation, we would call the OpenAI API
      // For now, we'll simulate a response

      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      return {
        content: `I'm happy to help! ${this.simulateResponse(messages[messages.length - 1].content)}`
      };
    } catch (error) {
      console.error('Error calling OpenAI API:', error);
      throw error;
    }
  }

  /**
   * Call DeepSeek API
   */
  private async callDeepSeek(
    apiKey: string,
    messages: Array<{ role: 'user' | 'assistant'; content: string }>,
    creatorId: string,
    creatorName: string
  ) {
    try {
      // In a real implementation, we would call the DeepSeek API
      // For now, we'll simulate a response

      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      return {
        content: `Thanks for your message about my video! ${this.simulateResponse(messages[messages.length - 1].content)}`
      };
    } catch (error) {
      console.error('Error calling DeepSeek API:', error);
      throw error;
    }
  }

  /**
   * Call Bhashini API
   */
  private async callBhashini(
    apiKey: string,
    messages: Array<{ role: 'user' | 'assistant'; content: string }>,
    creatorId: string,
    creatorName: string
  ) {
    try {
      // In a real implementation, we would call the Bhashini API
      // For now, we'll simulate a response

      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      return {
        content: `Thank you for reaching out about my content! ${this.simulateResponse(messages[messages.length - 1].content)}`
      };
    } catch (error) {
      console.error('Error calling Bhashini API:', error);
      throw error;
    }
  }

  /**
   * Call Custom API
   */
  private async callCustomAPI(
    endpoint: string,
    apiKey: string,
    messages: Array<{ role: 'user' | 'assistant'; content: string }>,
    creatorId: string,
    creatorName: string
  ) {
    try {
      // In a real implementation, we would call the custom API
      // For now, we'll simulate a response

      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      return {
        content: `I appreciate your interest in my video! ${this.simulateResponse(messages[messages.length - 1].content)}`
      };
    } catch (error) {
      console.error('Error calling Custom API:', error);
      throw error;
    }
  }

  /**
   * Helper function to generate creator responses
   */
  private simulateResponse(message: string): string {
    // Simple response generation based on message content
    if (message.toLowerCase().includes('hello') || message.toLowerCase().includes('hi')) {
      return "Hello! How can I help you with this video today?";
    }

    if (message.toLowerCase().includes('thank')) {
      return "You're welcome! Feel free to ask if you have any other questions.";
    }

    if (message.toLowerCase().includes('how') && message.toLowerCase().includes('work')) {
      return "That's a great question about how this works. In the video, I explain the process step by step, but essentially it involves understanding the core concepts and applying them correctly.";
    }

    if (message.toLowerCase().includes('explain')) {
      return "I'd be happy to explain that further. The concept I covered in the video is based on several key principles that work together to achieve the result.";
    }

    if (message.toLowerCase().includes('when') || message.toLowerCase().includes('time')) {
      return "The timing depends on several factors that I mentioned in the video. Generally, it takes about 2-3 weeks to see results, but it can vary based on your specific situation.";
    }

    // Default response for other messages
    return "That's an interesting point! In my video, I cover several aspects of this topic. Is there something specific you'd like me to elaborate on?";
  }
}

export const aiAssistantService = new AIAssistantService();

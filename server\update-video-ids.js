/**
 * <PERSON><PERSON><PERSON> to update video IDs in the database with valid Engaxe IDs
 * This script will update videos that have invalid IDs with valid ones
 */

const mongoose = require('mongoose');
const readline = require('readline');

// MongoDB connection string
const MONGODB_URI = 'mongodb://localhost:27017/lawengaxe';

// List of known valid Engaxe video IDs from our database
const validEngaxeIds = [
  'XLcMq2', 'xW36l7', 'suZKhW', 'wollzl', 'axHkJa', 'KxyzuN', '4OE4QR'
];

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Connect to MongoDB
mongoose.connect(MONGODB_URI)
  .then(() => {
    console.log('Connected to MongoDB');
    promptUser();
  })
  .catch(err => {
    console.error('Error connecting to MongoDB:', err);
    process.exit(1);
  });

// Define the Video schema
const VideoSchema = new mongoose.Schema({
  id: String,
  title: String,
  description: String,
  url: String,
  thumbnailUrl: String,
  duration: Number,
  userId: String,
  channelId: String,
  visibility: String,
  languages: [{
    code: String,
    name: String,
    isDefault: Boolean,
    url: String
  }],
  source: mongoose.Schema.Types.Mixed
});

// Create the Video model
const Video = mongoose.model('Video', VideoSchema);

// Function to prompt the user for confirmation
function promptUser() {
  console.log('\n--- WARNING ---');
  console.log('This script will update all videos in the database that have invalid Engaxe IDs.');
  console.log('It will replace them with valid Engaxe IDs from the following list:');
  console.log(validEngaxeIds.join(', '));
  console.log('\nThis operation cannot be undone. Make sure you have a backup of your database.');

  rl.question('\nDo you want to continue? (yes/no): ', (answer) => {
    if (answer.toLowerCase() === 'yes') {
      updateVideoIds();
    } else {
      console.log('Operation cancelled.');
      rl.close();
      mongoose.disconnect()
        .then(() => {
          console.log('Disconnected from MongoDB');
          process.exit(0);
        });
    }
  });
}

// Function to update video IDs
async function updateVideoIds() {
  try {
    console.log('Starting to update video IDs...');

    // Get all videos
    const videos = await Video.find({});
    console.log(`Found ${videos.length} videos in the database`);

    let updatedCount = 0;
    let skippedCount = 0;

    // Process each video
    for (const video of videos) {
      console.log(`Checking video ${video.id} with URL: ${video.url}`);

      // Check if the URL looks like a valid Engaxe ID
      const isLikelyValidFormat = /^[a-zA-Z0-9]{6,10}$/.test(video.url);

      if (!isLikelyValidFormat) {
        console.log(`❌ Video ${video.id} has an invalid URL format: ${video.url}`);

        // Get a random valid ID from the list
        const randomIndex = Math.floor(Math.random() * validEngaxeIds.length);
        const validId = validEngaxeIds[randomIndex];

        console.log(`Updating video ${video.id} with valid ID: ${validId}`);

        // Update the video URL
        video.url = validId;

        // Update source if it exists
        if (video.source) {
          if (typeof video.source === 'object') {
            video.source.originalUrl = validId;
            video.source.externalId = validId;
          }
        }

        // Update language URLs
        if (video.languages && video.languages.length > 0) {
          for (const lang of video.languages) {
            lang.url = validId;
          }
        }

        // Save the updated video
        await video.save();
        updatedCount++;
        console.log(`✅ Updated video ${video.id}`);
      } else {
        console.log(`✓ Video ${video.id} already has a valid URL format: ${video.url}`);
        skippedCount++;
      }
    }

    console.log('\n--- SUMMARY ---');
    console.log(`Total videos: ${videos.length}`);
    console.log(`Updated videos: ${updatedCount}`);
    console.log(`Skipped videos (already valid): ${skippedCount}`);

    rl.close();

    // Disconnect from MongoDB
    mongoose.disconnect()
      .then(() => {
        console.log('Disconnected from MongoDB');
        process.exit(0);
      })
      .catch(err => {
        console.error('Error disconnecting from MongoDB:', err);
        process.exit(1);
      });
  } catch (error) {
    console.error('Error updating video IDs:', error);
    rl.close();
    process.exit(1);
  }
}

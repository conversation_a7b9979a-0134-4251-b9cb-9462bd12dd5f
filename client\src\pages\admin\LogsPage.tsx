import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { useTheme } from '@/context/ThemeContext';
import AdminLayout from '@/components/admin/AdminLayout';
import {
  Search, Filter, RefreshCw, Download, Clock, AlertCircle,
  CheckCircle, XCircle, Info, User, Calendar, FileText,
  ChevronDown, X, ChevronUp
} from 'lucide-react';
import axios from 'axios';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { format } from 'date-fns';

// Log interface
interface Log {
  id: string;
  action: string;
  category: string;
  userId: string;
  userIp: string;
  userAgent: string;
  resourceType: string;
  resourceId?: string;
  status: string; // Changed from enum to string to accommodate all possible values
  severity: string; // Changed from enum to string to accommodate all possible values
  details?: string;
  createdAt: string;
  previousState?: any;
  newState?: any;
}

export default function LogsPage() {
  const { isAdmin, token } = useAuth();
  const navigate = useNavigate();
  const { theme } = useTheme();

  // Mock data for seed
  const seedLogs: Log[] = [
    {
      id: '1',
      action: 'login',
      category: 'security',
      userId: 'admin',
      userIp: '127.0.0.1',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      resourceType: 'auth',
      status: 'success',
      severity: 'low',
      details: 'User login successful',
      createdAt: new Date().toISOString()
    },
    {
      id: '2',
      action: 'update',
      category: 'user',
      userId: 'admin',
      userIp: '127.0.0.1',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      resourceType: 'user',
      resourceId: 'user123',
      status: 'success',
      severity: 'medium',
      details: 'User profile updated',
      createdAt: new Date(Date.now() - 3600000).toISOString()
    },
    {
      id: '3',
      action: 'create',
      category: 'content',
      userId: 'creator',
      userIp: '***********',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      resourceType: 'video',
      resourceId: 'video456',
      status: 'success',
      severity: 'low',
      details: 'New video uploaded',
      createdAt: new Date(Date.now() - 7200000).toISOString()
    },
    {
      id: '4',
      action: 'delete',
      category: 'content',
      userId: 'admin',
      userIp: '127.0.0.1',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      resourceType: 'video',
      resourceId: 'video789',
      status: 'warning',
      severity: 'medium',
      details: 'Video deleted',
      createdAt: new Date(Date.now() - 10800000).toISOString()
    },
    {
      id: '5',
      action: 'permission_change',
      category: 'admin',
      userId: 'admin',
      userIp: '127.0.0.1',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      resourceType: 'role',
      resourceId: 'editor',
      status: 'info',
      severity: 'high',
      details: 'Role permissions updated',
      createdAt: new Date(Date.now() - 86400000).toISOString()
    }
  ];

  // State for logs data
  const [logs, setLogs] = useState<Log[]>(seedLogs); // Initialize with seed data
  const [loading, setLoading] = useState(false); // Set to false to show seed data immediately
  const [error, setError] = useState<string | null>(null);

  // State for pagination
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [total, setTotal] = useState(seedLogs.length);
  const [totalPages, setTotalPages] = useState(1);

  // State for filters
  const [action, setAction] = useState('all_actions');
  const [category, setCategory] = useState('all_categories');
  const [status, setStatus] = useState('all_statuses');
  const [severity, setSeverity] = useState('all_severities');
  const [resourceType, setResourceType] = useState('all_resources');
  const [searchQuery, setSearchQuery] = useState('');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');

  // Auth debug info removed as requested

  // State for selected log
  const [selectedLog, setSelectedLog] = useState<Log | null>(null);
  const [showLogDetails, setShowLogDetails] = useState(false);

  // State for showing advanced filters (Gmail-style)
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);

  // Redirect non-admin users to home page
  useEffect(() => {
    if (!isAdmin) {
      navigate('/');
    }
  }, [isAdmin, navigate]);

  // Filter logs based on selected filters
  const filterLogs = (logs: Log[]) => {
    let filteredLogs = [...logs];

    // Apply action filter
    if (action && action !== 'all_actions') {
      filteredLogs = filteredLogs.filter(log => log.action === action);
    }

    // Apply category filter
    if (category && category !== 'all_categories') {
      filteredLogs = filteredLogs.filter(log => log.category === category);
    }

    // Apply status filter
    if (status && status !== 'all_statuses') {
      filteredLogs = filteredLogs.filter(log => log.status === status);
    }

    // Apply severity filter
    if (severity && severity !== 'all_severities') {
      filteredLogs = filteredLogs.filter(log => log.severity === severity);
    }

    // Apply resource type filter
    if (resourceType && resourceType !== 'all_resources') {
      filteredLogs = filteredLogs.filter(log => log.resourceType === resourceType);
    }

    // Apply date range filters
    if (startDate) {
      const startDateTime = new Date(startDate).getTime();
      filteredLogs = filteredLogs.filter(log => new Date(log.createdAt).getTime() >= startDateTime);
    }

    if (endDate) {
      const endDateTime = new Date(endDate).getTime();
      filteredLogs = filteredLogs.filter(log => new Date(log.createdAt).getTime() <= endDateTime);
    }

    // Apply search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filteredLogs = filteredLogs.filter(log =>
        log.action.toLowerCase().includes(query) ||
        log.category.toLowerCase().includes(query) ||
        log.userId.toLowerCase().includes(query) ||
        log.resourceType.toLowerCase().includes(query) ||
        (log.details && log.details.toLowerCase().includes(query))
      );
    }

    return filteredLogs;
  };

  // Fetch logs
  const fetchLogs = async () => {
    if (!token) return;

    setLoading(true);
    setError(null);

    try {
      // Build query parameters
      const params = new URLSearchParams();
      params.append('page', page.toString());
      params.append('limit', limit.toString());

      if (action && action !== 'all_actions') params.append('action', action);
      if (category && category !== 'all_categories') params.append('category', category);
      if (status && status !== 'all_statuses') params.append('status', status);
      if (severity && severity !== 'all_severities') params.append('severity', severity);
      if (resourceType && resourceType !== 'all_resources') params.append('resourceType', resourceType);
      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);

      // For development/testing - if API is not ready, use mock data
      if (process.env.NODE_ENV === 'development') {
        console.log('Using mock data for logs');
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 500));

        // Filter the seed logs based on the selected filters
        const filteredLogs = filterLogs(seedLogs);

        setLogs(filteredLogs);
        setTotal(filteredLogs.length);
        setTotalPages(Math.ceil(filteredLogs.length / limit));
        setLoading(false);
        return;
      }

      // Fetch logs from API
      try {
        const response = await axios.get(`/api/v1/logs?${params.toString()}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });

        const data = response.data;

        if (data.success) {
          setLogs(data.logs);
          setTotal(data.pagination.total);
          setTotalPages(data.pagination.pages);
        } else {
          throw new Error(data.message || 'Failed to fetch logs');
        }
      } catch (axiosError) {
        console.error('Error fetching logs:', axiosError);
        // If API endpoint doesn't exist yet, use mock data
        console.log('Using mock data as fallback');

        // Use the same filtering logic for fallback
        const filteredLogs = filterLogs(seedLogs);

        setLogs(filteredLogs);
        setTotal(filteredLogs.length);
        setTotalPages(Math.ceil(filteredLogs.length / limit));
      }
    } catch (err) {
      console.error('Error in fetchLogs:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Fetch logs on mount and ensure seed data is visible
  useEffect(() => {
    // Make sure seed data is visible by default
    setLogs(seedLogs);
    setTotal(seedLogs.length);
    setTotalPages(Math.ceil(seedLogs.length / limit));
  }, [token, limit]); // Only depend on token and limit to avoid refetching on every filter change

  // Apply filters immediately
  const applyFilters = () => {
    setPage(1); // Reset to first page
    const filteredLogs = filterLogs(seedLogs);
    setLogs(filteredLogs);
    setTotal(filteredLogs.length);
    setTotalPages(Math.ceil(filteredLogs.length / limit));
  };

  // Reset filters and show all logs
  const resetFilters = () => {
    setAction('all_actions');
    setCategory('all_categories');
    setStatus('all_statuses');
    setSeverity('all_severities');
    setResourceType('all_resources');
    setSearchQuery('');
    setStartDate('');
    setEndDate('');
    setPage(1);

    // Reset to show all seed logs
    setLogs(seedLogs);
    setTotal(seedLogs.length);
    setTotalPages(Math.ceil(seedLogs.length / limit));
  };

  // Export logs
  const exportLogs = () => {
    // Create CSV content
    const headers = ['ID', 'Action', 'Category', 'User ID', 'Resource Type', 'Status', 'Severity', 'Created At', 'Details'];
    const csvContent = [
      headers.join(','),
      ...logs.map(log => [
        log.id,
        log.action,
        log.category,
        log.userId,
        log.resourceType,
        log.status,
        log.severity,
        log.createdAt,
        log.details ? `"${log.details.replace(/"/g, '""')}"` : ''
      ].join(','))
    ].join('\n');

    // Create download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `logs_export_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // View log details
  const viewLogDetails = (log: Log) => {
    setSelectedLog(log);
    setShowLogDetails(true);
  };

  // Get status badge color
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'success':
        return <Badge className="bg-green-500">{status}</Badge>;
      case 'failure':
        return <Badge className="bg-red-500">{status}</Badge>;
      case 'warning':
        return <Badge className="bg-yellow-500">{status}</Badge>;
      case 'info':
        return <Badge className="bg-blue-500">{status}</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  // Get severity badge color
  const getSeverityBadge = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <Badge className="bg-red-600">{severity}</Badge>;
      case 'high':
        return <Badge className="bg-red-500">{severity}</Badge>;
      case 'medium':
        return <Badge className="bg-yellow-500">{severity}</Badge>;
      case 'low':
        return <Badge className="bg-green-500">{severity}</Badge>;
      default:
        return <Badge>{severity}</Badge>;
    }
  };

  // If not admin, don't render the page content
  if (!isAdmin) {
    return null;
  }

  return (
    <AdminLayout>
      <div className="bg-gray-900 p-4 min-h-screen">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-white">System Logs</h1>
          <p className="text-gray-300">
            View and manage system activity logs
          </p>
        </div>

        {/* Gmail-style Search Bar and Filters */}
        <div className="mb-6">
          {/* Gmail-style Search Bar */}
          <div className="flex items-center bg-gray-800 border border-gray-700 rounded-lg p-2 mb-0">
            <div className="flex-grow flex items-center">
              <Search className="h-5 w-5 text-gray-400 mr-2 ml-2" />
              <Input
                type="text"
                placeholder="Search logs..."
                className="border-0 bg-transparent text-white focus-visible:ring-0 focus-visible:ring-offset-0 placeholder:text-gray-400"
                value={searchQuery}
                onChange={(e) => {
                  setSearchQuery(e.target.value);
                }}
              />
            </div>
            <div className="flex items-center">
              <Button
                variant="ghost"
                size="sm"
                className="text-gray-400 hover:text-white hover:bg-gray-700 rounded-full p-2"
                onClick={resetFilters}
              >
                <X className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="text-gray-400 hover:text-white hover:bg-gray-700 rounded-full p-2 ml-1"
                onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
              >
                <Filter className="h-5 w-5" />
              </Button>
              <div className="h-6 mx-2 border-l border-gray-600"></div>
              <Button
                variant="ghost"
                size="sm"
                className="text-gray-400 hover:text-white hover:bg-gray-700 rounded-full p-2"
                onClick={applyFilters}
              >
                <Search className="h-5 w-5" />
              </Button>
            </div>
          </div>

          {/* Active Filters Display - Directly below search bar */}
          {(action !== 'all_actions' || category !== 'all_categories' || status !== 'all_statuses' ||
            severity !== 'all_severities' || resourceType !== 'all_resources' || startDate || endDate || searchQuery) && (
            <div className="flex flex-wrap gap-2 py-2 px-3 border-t-0 border-b border-l border-r border-gray-700 bg-gray-800 rounded-b-lg mb-4">
            {action !== 'all_actions' && (
              <Badge className="bg-gray-700 text-white hover:bg-gray-600 cursor-pointer flex items-center gap-1 px-3 py-1">
                Action: {action}
                <X className="h-3 w-3 ml-1" onClick={() => setAction('all_actions')} />
              </Badge>
            )}
            {category !== 'all_categories' && (
              <Badge className="bg-gray-700 text-white hover:bg-gray-600 cursor-pointer flex items-center gap-1 px-3 py-1">
                Category: {category}
                <X className="h-3 w-3 ml-1" onClick={() => setCategory('all_categories')} />
              </Badge>
            )}
            {status !== 'all_statuses' && (
              <Badge className="bg-gray-700 text-white hover:bg-gray-600 cursor-pointer flex items-center gap-1 px-3 py-1">
                Status: {status}
                <X className="h-3 w-3 ml-1" onClick={() => setStatus('all_statuses')} />
              </Badge>
            )}
            {severity !== 'all_severities' && (
              <Badge className="bg-gray-700 text-white hover:bg-gray-600 cursor-pointer flex items-center gap-1 px-3 py-1">
                Severity: {severity}
                <X className="h-3 w-3 ml-1" onClick={() => setSeverity('all_severities')} />
              </Badge>
            )}
            {resourceType !== 'all_resources' && (
              <Badge className="bg-gray-700 text-white hover:bg-gray-600 cursor-pointer flex items-center gap-1 px-3 py-1">
                Resource: {resourceType}
                <X className="h-3 w-3 ml-1" onClick={() => setResourceType('all_resources')} />
              </Badge>
            )}
            {startDate && (
              <Badge className="bg-gray-700 text-white hover:bg-gray-600 cursor-pointer flex items-center gap-1 px-3 py-1">
                From: {new Date(startDate).toLocaleDateString()}
                <X className="h-3 w-3 ml-1" onClick={() => setStartDate('')} />
              </Badge>
            )}
            {endDate && (
              <Badge className="bg-gray-700 text-white hover:bg-gray-600 cursor-pointer flex items-center gap-1 px-3 py-1">
                To: {new Date(endDate).toLocaleDateString()}
                <X className="h-3 w-3 ml-1" onClick={() => setEndDate('')} />
              </Badge>
            )}
            {searchQuery && (
              <Badge className="bg-gray-700 text-white hover:bg-gray-600 cursor-pointer flex items-center gap-1 px-3 py-1">
                Search: {searchQuery}
                <X className="h-3 w-3 ml-1" onClick={() => setSearchQuery('')} />
              </Badge>
            )}
          </div>
          )}

          {/* Advanced Filters (Gmail-style) */}
          {showAdvancedFilters && (
            <Card className="bg-gray-800 border-gray-700 text-white">
              <CardContent className="pt-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div>
                    <label className="text-sm font-medium mb-1 block">Action</label>
                    <Select value={action} onValueChange={(value) => {
                      setAction(value);
                    }}>
                      <SelectTrigger className="bg-gray-800 border-gray-600 text-white">
                        <SelectValue placeholder="Select action" />
                      </SelectTrigger>
                      <SelectContent className="bg-gray-800 border-gray-600 text-white">
                        <SelectItem value="all_actions">All Actions</SelectItem>
                        <SelectItem value="login">Login</SelectItem>
                        <SelectItem value="logout">Logout</SelectItem>
                        <SelectItem value="create">Create</SelectItem>
                        <SelectItem value="update">Update</SelectItem>
                        <SelectItem value="delete">Delete</SelectItem>
                        <SelectItem value="register">Register</SelectItem>
                        <SelectItem value="permission_change">Permission Change</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="text-sm font-medium mb-1 block">Category</label>
                    <Select value={category} onValueChange={(value) => {
                      setCategory(value);
                    }}>
                      <SelectTrigger className="bg-gray-800 border-gray-600 text-white">
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent className="bg-gray-800 border-gray-600 text-white">
                        <SelectItem value="all_categories">All Categories</SelectItem>
                        <SelectItem value="user">User</SelectItem>
                        <SelectItem value="security">Security</SelectItem>
                        <SelectItem value="content">Content</SelectItem>
                        <SelectItem value="admin">Admin</SelectItem>
                        <SelectItem value="system">System</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="text-sm font-medium mb-1 block">Status</label>
                    <Select value={status} onValueChange={(value) => {
                      setStatus(value);
                    }}>
                      <SelectTrigger className="bg-gray-800 border-gray-600 text-white">
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent className="bg-gray-800 border-gray-600 text-white">
                        <SelectItem value="all_statuses">All Statuses</SelectItem>
                        <SelectItem value="success">Success</SelectItem>
                        <SelectItem value="failure">Failure</SelectItem>
                        <SelectItem value="warning">Warning</SelectItem>
                        <SelectItem value="info">Info</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="text-sm font-medium mb-1 block">Severity</label>
                    <Select value={severity} onValueChange={(value) => {
                      setSeverity(value);
                    }}>
                      <SelectTrigger className="bg-gray-800 border-gray-600 text-white">
                        <SelectValue placeholder="Select severity" />
                      </SelectTrigger>
                      <SelectContent className="bg-gray-800 border-gray-600 text-white">
                        <SelectItem value="all_severities">All Severities</SelectItem>
                        <SelectItem value="critical">Critical</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="low">Low</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="text-sm font-medium mb-1 block">Resource Type</label>
                    <Select value={resourceType} onValueChange={(value) => {
                      setResourceType(value);
                    }}>
                      <SelectTrigger className="bg-gray-800 border-gray-600 text-white">
                        <SelectValue placeholder="Select resource type" />
                      </SelectTrigger>
                      <SelectContent className="bg-gray-800 border-gray-600 text-white">
                        <SelectItem value="all_resources">All Resources</SelectItem>
                        <SelectItem value="user">User</SelectItem>
                        <SelectItem value="role">Role</SelectItem>
                        <SelectItem value="permission">Permission</SelectItem>
                        <SelectItem value="video">Video</SelectItem>
                        <SelectItem value="auth">Auth</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <label className="text-sm font-medium mb-1 block">From</label>
                      <Input
                        type="datetime-local"
                        value={startDate}
                        onChange={(e) => {
                          setStartDate(e.target.value);
                        }}
                        className="bg-gray-800 border-gray-600 text-white"
                        placeholder="mm/dd/yyyy --:-- --"
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium mb-1 block">To</label>
                      <Input
                        type="datetime-local"
                        value={endDate}
                        onChange={(e) => {
                          setEndDate(e.target.value);
                        }}
                        className="bg-gray-800 border-gray-600 text-white"
                        placeholder="mm/dd/yyyy --:-- --"
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-end border-t border-gray-700 pt-4">
                <Button variant="outline" onClick={resetFilters} className="border-gray-600 text-white hover:bg-gray-700 mr-2">
                  Reset
                </Button>
                <Button onClick={() => {
                  applyFilters();
                  setShowAdvancedFilters(false);
                }} className="bg-orange-500 hover:bg-orange-600 text-white">
                  Apply Filters
                </Button>
              </CardFooter>
            </Card>
          )}
        </div>

        {/* Auth Debug Panel removed as requested */}

        {/* Logs Table */}
        <Card className="bg-gray-800 border-gray-700 text-white">
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle className="text-lg">System Logs</CardTitle>
            <div className="flex space-x-2">
              <Button variant="outline" size="sm" onClick={fetchLogs} className="border-gray-600 text-white hover:bg-gray-700">
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
              <Button variant="outline" size="sm" onClick={exportLogs} className="border-gray-600 text-white hover:bg-gray-700">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {loading ? (
              // Loading skeleton
              <div className="space-y-4">
                {Array.from({ length: 5 }).map((_, i) => (
                  <div key={i} className="flex items-center space-x-4">
                    <Skeleton className="h-12 w-12 rounded-full bg-gray-700" />
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-[250px] bg-gray-700" />
                      <Skeleton className="h-4 w-[200px] bg-gray-700" />
                    </div>
                  </div>
                ))}
              </div>
            ) : error ? (
              // Error message
              <div className="text-center py-8 text-white">
                <AlertCircle className="h-12 w-12 mx-auto text-red-500 mb-4" />
                <h3 className="text-lg font-medium">Error loading logs</h3>
                <p className="text-gray-400 mt-2">{error}</p>
                <Button className="mt-4 bg-orange-500 hover:bg-orange-600 text-white" onClick={fetchLogs}>
                  Try Again
                </Button>
              </div>
            ) : logs.length === 0 ? (
              // No logs found
              <div className="text-center py-8 text-white">
                <FileText className="h-12 w-12 mx-auto text-gray-500 mb-4" />
                <h3 className="text-lg font-medium">No logs found</h3>
                <p className="text-gray-400 mt-2">Try adjusting your filters or create some activity</p>
              </div>
            ) : (
              // Logs table
              <div className="overflow-x-auto">
                <Table className="text-white">
                  <TableHeader className="bg-gray-900">
                    <TableRow>
                      <TableHead className="text-gray-300">Time</TableHead>
                      <TableHead className="text-gray-300">Action</TableHead>
                      <TableHead className="text-gray-300">Category</TableHead>
                      <TableHead className="text-gray-300">User</TableHead>
                      <TableHead className="text-gray-300">Resource</TableHead>
                      <TableHead className="text-gray-300">Status</TableHead>
                      <TableHead className="text-gray-300">Severity</TableHead>
                      <TableHead className="text-gray-300">Details</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {logs.map((log) => (
                      <TableRow
                        key={log.id}
                        className="cursor-pointer border-gray-700 hover:bg-gray-700"
                        onClick={() => viewLogDetails(log)}
                      >
                        <TableCell className="whitespace-nowrap">
                          <div className="flex items-center">
                            <Clock className="h-4 w-4 mr-2 text-gray-500" />
                            {format(new Date(log.createdAt), 'MMM d, yyyy HH:mm:ss')}
                          </div>
                        </TableCell>
                        <TableCell>{log.action}</TableCell>
                        <TableCell>{log.category}</TableCell>
                        <TableCell className="whitespace-nowrap">
                          <div className="flex items-center">
                            <User className="h-4 w-4 mr-2 text-gray-500" />
                            {log.userId}
                          </div>
                        </TableCell>
                        <TableCell>
                          {log.resourceType}
                          {log.resourceId && <span className="text-gray-500 ml-1">({log.resourceId})</span>}
                        </TableCell>
                        <TableCell>{getStatusBadge(log.status)}</TableCell>
                        <TableCell>{getSeverityBadge(log.severity)}</TableCell>
                        <TableCell className="max-w-xs truncate">
                          {log.details || '-'}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
              )}

            {/* Pagination */}
            {!loading && !error && logs.length > 0 && (
              <div className="mt-4">
                <Pagination>
                  <PaginationContent className="text-white">
                    <PaginationItem>
                      <PaginationPrevious
                        onClick={() => setPage(p => Math.max(1, p - 1))}
                        className={`${page <= 1 ? 'pointer-events-none opacity-50' : ''} bg-gray-800 border-gray-700 hover:bg-gray-700 text-white`}
                      />
                    </PaginationItem>

                    {Array.from({ length: Math.min(5, totalPages) }).map((_, i) => {
                      let pageNum: number;
                      if (totalPages <= 5) {
                        pageNum = i + 1;
                      } else if (page <= 3) {
                        pageNum = i + 1;
                      } else if (page >= totalPages - 2) {
                        pageNum = totalPages - 4 + i;
                      } else {
                        pageNum = page - 2 + i;
                      }

                      return (
                        <PaginationItem key={i}>
                          <PaginationLink
                            isActive={pageNum === page}
                            onClick={() => setPage(pageNum)}
                            className={`${pageNum === page ? 'bg-orange-500 text-white' : 'bg-gray-800 text-white hover:bg-gray-700'} border-gray-700`}
                          >
                            {pageNum}
                          </PaginationLink>
                        </PaginationItem>
                      );
                    })}

                    {totalPages > 5 && page < totalPages - 2 && (
                      <>
                        <PaginationItem>
                          <PaginationEllipsis className="bg-gray-800 border-gray-700 text-white" />
                        </PaginationItem>
                        <PaginationItem>
                          <PaginationLink
                            onClick={() => setPage(totalPages)}
                            className="bg-gray-800 border-gray-700 text-white hover:bg-gray-700"
                          >
                            {totalPages}
                          </PaginationLink>
                        </PaginationItem>
                      </>
                    )}

                    <PaginationItem>
                      <PaginationNext
                        onClick={() => setPage(p => Math.min(totalPages, p + 1))}
                        className={`${page >= totalPages ? 'pointer-events-none opacity-50' : ''} bg-gray-800 border-gray-700 hover:bg-gray-700 text-white`}
                      />
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
      {/* Log Details Dialog */}
      <Dialog open={showLogDetails} onOpenChange={setShowLogDetails}>
      <DialogContent className="max-w-3xl bg-gray-800 text-white border-gray-700">
        <DialogHeader>
          <DialogTitle>Log Details</DialogTitle>
          <DialogDescription className="text-gray-400">
            Detailed information about the selected log entry
          </DialogDescription>
        </DialogHeader>

        {selectedLog && (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h3 className="text-sm font-medium text-gray-400">ID</h3>
                <p>{selectedLog.id}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-400">Timestamp</h3>
                <p>{format(new Date(selectedLog.createdAt), 'PPpp')}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-400">Action</h3>
                <p>{selectedLog.action}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-400">Category</h3>
                <p>{selectedLog.category}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-400">User ID</h3>
                <p>{selectedLog.userId}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-400">User IP</h3>
                <p>{selectedLog.userIp}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-400">Resource Type</h3>
                <p>{selectedLog.resourceType}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-400">Resource ID</h3>
                <p>{selectedLog.resourceId || '-'}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-400">Status</h3>
                <p>{getStatusBadge(selectedLog.status)}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-400">Severity</h3>
                <p>{getSeverityBadge(selectedLog.severity)}</p>
              </div>
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-400">User Agent</h3>
              <p className="text-sm break-all">{selectedLog.userAgent}</p>
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-400">Details</h3>
              <p className="whitespace-pre-wrap">{selectedLog.details || '-'}</p>
            </div>

            {selectedLog.previousState && (
              <div>
                <h3 className="text-sm font-medium text-gray-400">Previous State</h3>
                <pre className="bg-gray-900 p-3 rounded text-xs overflow-auto max-h-40 text-gray-300">
                  {JSON.stringify(selectedLog.previousState, null, 2)}
                </pre>
              </div>
            )}

            {selectedLog.newState && (
              <div>
                <h3 className="text-sm font-medium text-gray-400">New State</h3>
                <pre className="bg-gray-900 p-3 rounded text-xs overflow-auto max-h-40 text-gray-300">
                  {JSON.stringify(selectedLog.newState, null, 2)}
                </pre>
              </div>
            )}
          </div>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={() => setShowLogDetails(false)} className="border-gray-600 text-white hover:bg-gray-700">
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
      </Dialog>
    </AdminLayout>
  );
}

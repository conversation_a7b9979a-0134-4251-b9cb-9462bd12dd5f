
import React, { createContext, useContext, useState, useEffect } from 'react';
import { Video, Language } from '@/types';
import { videoAPI } from '@/services/api';
import { getLocalVideos } from '@/utils/localVideoStorage';
// Removed imports for engaxe-utils and video-id-store
import { processVideoForClient, processVideosForClient, extractEngaxeVideoId } from '@/utils/videoIdConverter';
import { discoverEngaxeIdsForVideos } from '@/utils/videoIdDiscovery';

interface VideoContextType {
  videos: Video[];
  trendingVideos: Video[];
  categories: string[];
  languages: Language[];
  getVideoById: (id: string) => Video | undefined;
  getVideosByCategory: (category: string) => Video[];
  isLoading: boolean;
  refreshVideos: () => Promise<void>;
}

const VideoContext = createContext<VideoContextType | undefined>(undefined);

export const useVideos = () => {
  const context = useContext(VideoContext);
  if (context === undefined) {
    throw new Error('useVideos must be used within a VideoProvider');
  }
  return context;
};

// Helper function to get flag emoji based on language code
const getLanguageFlag = (code: string): string => {
  switch (code) {
    case 'en': return '🇺🇸';
    case 'hi': return '🇮🇳';
    case 'es': return '🇪🇸';
    case 'fr': return '🇫🇷';
    case 'de': return '🇩🇪';
    case 'ja': return '🇯🇵';
    case 'zh': return '🇨🇳';
    case 'ru': return '🇷🇺';
    case 'ar': return '🇸🇦';
    case 'pt': return '🇧🇷';
    case 'it': return '🇮🇹';
    case 'nl': return '🇳🇱';
    case 'ko': return '🇰🇷';
    case 'tr': return '🇹🇷';
    default: return '🌐';
  }
};

// Available languages
const languages: Language[] = [
  { code: 'en', name: 'English', flag: '🇺🇸' },
  { code: 'es', name: 'Spanish', flag: '🇪🇸' },
  { code: 'fr', name: 'French', flag: '🇫🇷' },
  { code: 'de', name: 'German', flag: '🇩🇪' },
  { code: 'ja', name: 'Japanese', flag: '🇯🇵' },
  { code: 'zh', name: 'Chinese', flag: '🇨🇳' },
  { code: 'ru', name: 'Russian', flag: '🇷🇺' },
  { code: 'ar', name: 'Arabic', flag: '🇸🇦' },
  { code: 'hi', name: 'Hindi', flag: '🇮🇳' },
  { code: 'pt', name: 'Portuguese', flag: '🇧🇷' }
];

// Categories
const categories = [
  'Education', 'Business', 'Technology', 'Cooking', 'Travel',
  'Music', 'Sports', 'Gaming', 'Health', 'Science'
];

// Mock videos data
const mockVideos: Video[] = [
  {
    id: 'v1',
    title: 'International Tech Conference 2025',
    thumbnail: '/placeholder.svg',
    creator: { id: 'c1', username: 'TechEvents', avatar: '/placeholder.svg' },
    views: 1250000,
    likes: 45700,
    createdAt: new Date(Date.now() - 2 * 24 * 60 * 60000).toISOString(),
    languages: languages.slice(0, 12), // All languages
    category: 'Technology',
    isLive: true,
    description: 'Join tech leaders from around the world discussing the future of AI and machine learning in 12 different languages.',
    duration: '1:45:32'
  },
  {
    id: 'v2',
    title: 'Learn React in 10 Different Languages',
    thumbnail: '/placeholder.svg',
    creator: { id: 'c2', username: 'CodeMaster', avatar: '/placeholder.svg' },
    views: 890000,
    likes: 32400,
    createdAt: new Date(Date.now() - 7 * 24 * 60 * 60000).toISOString(),
    languages: languages.slice(0, 10),
    category: 'Education',
    duration: '42:18'
  },
  {
    id: 'v3',
    title: 'Global Cooking Challenge',
    thumbnail: '/placeholder.svg',
    creator: { id: 'c3', username: 'ChefGlobal', avatar: '/placeholder.svg' },
    views: 567000,
    likes: 24100,
    createdAt: new Date(Date.now() - 14 * 24 * 60 * 60000).toISOString(),
    languages: languages.slice(0, 8),
    category: 'Cooking',
    duration: '28:45'
  },
  {
    id: 'v4',
    title: 'World Tour 2025: Virtual Edition',
    thumbnail: '/placeholder.svg',
    creator: { id: 'c4', username: 'TravelEnthusiast', avatar: '/placeholder.svg' },
    views: 420000,
    likes: 18900,
    createdAt: new Date(Date.now() - 21 * 24 * 60 * 60000).toISOString(),
    languages: languages.slice(0, 6),
    category: 'Travel',
    duration: '1:12:36'
  },
  {
    id: 'v5',
    title: 'Cross-Cultural Communication Strategies',
    thumbnail: '/placeholder.svg',
    creator: { id: 'c5', username: 'GlobalEducator', avatar: '/placeholder.svg' },
    views: 380000,
    likes: 15600,
    createdAt: new Date(Date.now() - 30 * 24 * 60 * 60000).toISOString(),
    languages: languages.slice(0, 5),
    category: 'Business',
    duration: '35:22'
  },
  {
    id: 'v6',
    title: 'Universal Game Design Principles',
    thumbnail: '/placeholder.svg',
    creator: { id: 'c6', username: 'GameDevPro', avatar: '/placeholder.svg' },
    views: 320000,
    likes: 13200,
    createdAt: new Date(Date.now() - 45 * 24 * 60 * 60000).toISOString(),
    languages: languages.slice(0, 4),
    category: 'Gaming',
    duration: '52:10'
  }
];

export const VideoProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [videos, setVideos] = useState<Video[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Function to fetch videos from API
  const fetchVideos = async (forceRefresh = false) => {
    try {
      setIsLoading(true);

      // Add timestamp for cache busting
      const timestamp = new Date().getTime();
      console.log(`Fetching videos with timestamp: ${timestamp}, forceRefresh: ${forceRefresh}`);

      let apiVideos: Video[] = [];
      console.log('Initialized apiVideos as empty array');

      // Get locally saved videos using our utility function
      const localVideos = getLocalVideos();
      console.log('Found locally saved videos:', localVideos.length);

      // Debug local videos
      if (localVideos.length > 0) {
        console.log('Local videos:', localVideos);
      }

      // Try to get videos from API (but don't worry if it fails)
      try {
        console.log('Fetching videos from API...');

        // Add random query parameter to bust cache
        const cacheParam = `_cache=${Math.random().toString(36).substring(2, 15)}`;

        console.log('Making API request with params:', {
          limit: 50,
          sort: 'newest',
          _t: timestamp,
          _r: forceRefresh ? Math.random() : undefined
        });

        const response = await videoAPI.getVideos({
          limit: 50, // Increase limit to ensure we get all videos
          sort: 'newest',
          _t: timestamp, // Always add timestamp for cache busting
          _r: forceRefresh ? Math.random() : undefined // Add random parameter for forced refresh
        });

        console.log('Full API response:', response);

        // Check if the response has the expected structure
        if (!response || !response.success) {
          console.error('API response indicates failure:', response);
          throw new Error('API response indicates failure');
        }

        if (!response.videos) {
          console.error('API response missing videos array:', response);
          throw new Error('API response missing videos array');
        }

        // Log the video IDs for debugging
        try {
          if (Array.isArray(response.videos)) {
            console.log('Video IDs from API:', response.videos.map((v: any) => v.id).join(', '));
          } else {
            console.error('response.videos is not an array:', response.videos);
          }
        } catch (error) {
          console.error('Error logging video IDs:', error);
        }

        console.log('API response status:', response.success);
        console.log('API response videos count:', response.videos?.length || 0);
        console.log('API response videos:', response.videos);

        if (response.success && response.videos) {
          if (response.videos.length === 0) {
            console.log('API returned an empty videos array');
            apiVideos = []; // Set to empty array
          } else {
            console.log(`Received ${response.videos.length} videos from API`);

            // Map API response to our Video type
            apiVideos = response.videos.map((video: any) => {
            console.log('Processing video from API:', video.id, video.title, 'URL:', video.url);

            // Debug the full video object to see what we're working with
            console.log('Full video object:', JSON.stringify(video, null, 2));

            // CRITICAL FIX: Check if the video has all required properties
            if (!video.id || !video.title) {
              console.error('Video is missing required properties:', video);
              return null; // Skip this video
            }

            // Process the video using our videoIdConverter utility
            // This will ensure it has a valid Engaxe ID for the URL
            const processedVideo = processVideoForClient(video);

            // Get the processed URL
            let processedUrl = processedVideo.url || '';

            // If URL is still missing after processing, use the ID as a last resort
            if (!processedUrl) {
              console.error(`Video still has no URL after processing: ${video.id}`);
              processedUrl = video.id || '';
            } else {
              console.log(`Using processed URL for video ${video.id}: ${processedUrl}`);
            }

            // Process languages using the same approach as saveVideoLocally
            let videoLanguages: Language[] = [];

            // If languages are directly provided, process them
            if (processedVideo.languages && Array.isArray(processedVideo.languages) && processedVideo.languages.length > 0) {
              console.log(`Processing ${processedVideo.languages.length} languages for video ${processedVideo.id}`);

              // Process each language to ensure it has all required properties
              videoLanguages = processedVideo.languages.map((lang: any) => {
                // Create a new language object with all required properties
                const processedLang = {
                  code: lang.code || 'en',
                  name: lang.name || 'English',
                  flag: lang.flag || getLanguageFlag(lang.code || 'en'),
                  url: lang.url || processedUrl, // Use video URL as fallback
                  isDefault: typeof lang.isDefault !== 'undefined' ? lang.isDefault : lang.code === 'en'
                };

                // Log the processed language
                console.log(`Processed language: ${processedLang.name} (${processedLang.code}), URL: ${processedLang.url}, isDefault: ${processedLang.isDefault}`);

                return processedLang;
              });

              // Ensure at least one language is marked as default
              const hasDefaultLanguage = videoLanguages.some(lang => lang.isDefault);
              if (!hasDefaultLanguage && videoLanguages.length > 0) {
                console.log(`No default language found, setting ${videoLanguages[0].name} as default`);
                videoLanguages[0].isDefault = true;
              }
            } else {
              // If no languages, create a default English language
              videoLanguages = [{
                code: 'en',
                name: 'English',
                flag: '🇺🇸',
                isDefault: true,
                url: processedUrl
              }];
              console.log(`No languages found for video ${processedVideo.id}, added default English language with URL: ${processedUrl}`);
            }

            // Update the processed video with the processed languages
            processedVideo.languages = videoLanguages;

            // Log all processed languages
            console.log(`Final languages for video ${processedVideo.id}:`);
            processedVideo.languages.forEach((lang: Language, idx: number) => {
              console.log(`Language ${idx + 1}/${processedVideo.languages.length}: ${lang.name} (${lang.code}), URL: ${lang.url}, isDefault: ${lang.isDefault}, flag: ${lang.flag}`);
            });

            // Debug the video object to help diagnose issues
            console.log(`Processing video ${video.id} with title "${video.title}"`);
            console.log(`Video has languages: ${video.languages ? 'Yes' : 'No'}`);
            if (video.languages) {
              console.log(`Languages length: ${video.languages.length}`);
              // Log each language for debugging
              video.languages.forEach((lang, idx) => {
                console.log(`Language ${idx + 1}/${video.languages.length}: ${lang.name} (${lang.code}), URL: ${lang.url}, isDefault: ${lang.isDefault}`);

                // Ensure each language has a valid URL
                if (lang.url && (lang.url.includes('/') || lang.url.includes('.'))) {
                  // Try to extract the Engaxe ID from the URL
                  const extractedId = extractEngaxeVideoId(lang.url);
                  if (extractedId) {
                    console.log(`Extracted Engaxe ID from language URL: ${extractedId} (original: ${lang.url})`);
                    lang.url = extractedId;
                  }
                }
              });
            }

            return {
              id: video.id,
              title: video.title,
              thumbnail: video.thumbnailUrl || '/placeholder.svg',
              creator: {
                id: video.userId || 'unknown',
                username: video.channel?.name || 'Unknown',
                avatar: video.channel?.avatar || '/placeholder.svg'
              },
              views: video.stats?.views || 0,
              likes: video.stats?.likes || 0,
              createdAt: video.createdAt,
              // Use the processed languages from our videoIdConverter utility
              languages: processedVideo.languages || [{ code: 'en', name: 'English', flag: '🇺🇸', url: processedUrl, isDefault: true }],
              category: video.category,
              description: video.description,
              duration: video.duration ? `${Math.floor(video.duration / 60)}:${(video.duration % 60).toString().padStart(2, '0')}` : '0:00',
              url: processedUrl
            };
          });
          }
        } else {
          console.warn('API returned no videos or unsuccessful response');
        }
      } catch (apiError) {
        console.warn('Error with API call:', apiError);
        // Continue with local videos only
      }

      // Debug the API videos
      console.log('API videos before combining:', apiVideos);

      // Ensure apiVideos is an array
      if (!Array.isArray(apiVideos)) {
        console.error('apiVideos is not an array:', apiVideos);
        apiVideos = [];
      }

      // Filter out any null values from the API videos
      const validApiVideos = apiVideos.filter(video => video !== null);
      console.log('Valid API videos after filtering:', validApiVideos);

      // Combine API videos and local videos, removing duplicates
      const allVideos = [...validApiVideos];

      console.log('All videos after adding API videos:', allVideos);

      // Add local videos that don't exist in API videos
      localVideos.forEach(localVideo => {
        if (!allVideos.some(v => v.id === localVideo.id)) {
          // Ensure the video has all required properties
          const validatedVideo: Video = {
            ...localVideo,
            // Provide defaults for any missing properties
            id: localVideo.id || `local-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
            title: localVideo.title || 'Untitled Video',
            thumbnail: localVideo.thumbnail || '/placeholder.svg',
            creator: localVideo.creator || {
              id: 'local-user',
              username: 'You',
              avatar: '/placeholder.svg'
            },
            views: localVideo.views || 0,
            likes: localVideo.likes || 0,
            createdAt: localVideo.createdAt || new Date().toISOString(),
            languages: localVideo.languages || [languages[0]],
            category: localVideo.category || 'Uncategorized',
            description: localVideo.description || '',
            duration: localVideo.duration || '0:00',
            savedLocally: true
          };
          allVideos.push(validatedVideo);
        }
      });

      console.log('All videos after adding local videos:', allVideos);

      if (allVideos.length > 0) {
        // Sort by creation date (newest first)
        allVideos.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
        console.log('Final sorted videos:', allVideos);
        setVideos(allVideos);

        // After setting the videos, try to discover Engaxe IDs for any videos that need them
        // This runs asynchronously and won't block the UI
        setTimeout(() => {
          console.log('Starting Engaxe ID discovery process');
          discoverEngaxeIdsForVideos(allVideos).then(() => {
            console.log('Engaxe ID discovery process completed');
            // Update the videos with any newly discovered IDs
            setVideos([...allVideos]);
          }).catch(error => {
            console.error('Error in Engaxe ID discovery process:', error);
          });
        }, 2000); // Wait 2 seconds to avoid overwhelming the browser
      } else {
        // If no videos found, use mock data as fallback
        console.warn('No videos found, using mock data');
        setVideos(mockVideos);
      }
    } catch (error) {
      console.error('Error in fetchVideos:', error);
      // Use mock data as final fallback
      setVideos(mockVideos);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch videos on mount
  useEffect(() => {
    fetchVideos();
  }, []);

  // Get trending videos
  const trendingVideos = videos.slice().sort((a, b) => b.views - a.views);

  // Get a video by ID or URL
  const getVideoById = (idOrUrl: string) => {
    // First try to find the video by ID
    let video = videos.find(video => video.id === idOrUrl);

    // If not found by ID, try to find by URL
    if (!video) {
      video = videos.find(video => video.url === idOrUrl);
      if (video) {
        console.log(`Found video by URL: ${idOrUrl}`);
      } else {
        console.log(`No video found with URL: ${idOrUrl}, trying by ID`);
        // Try one more time with the ID, in case it's a hash ID
        video = videos.find(video => video.id === idOrUrl);
        if (video) {
          console.log(`Found video by ID: ${idOrUrl}`);
        }
      }
    } else {
      console.log(`Found video by ID: ${idOrUrl}`);
    }

    // If we found a video, process it to ensure it has valid Engaxe IDs
    if (video) {
      return processVideoForClient(video);
    }

    return video;
  };

  const getVideosByCategory = (category: string) => {
    return videos.filter(video => video.category === category);
  };

  // Function to refresh videos
  const refreshVideos = async () => {
    console.log('Refreshing videos...');
    setIsLoading(true);
    try {
      // Clear the current videos to force a complete refresh
      setVideos([]);

      // Wait a moment to ensure state updates
      await new Promise(resolve => setTimeout(resolve, 500));

      // Clear local storage cache to ensure we get fresh data
      localStorage.removeItem('cachedVideos');

      // Clear any cached API responses
      if (window.caches) {
        try {
          const cache = await window.caches.open('api-cache');
          const keys = await cache.keys();
          for (const key of keys) {
            if (key.url.includes('/videos')) {
              await cache.delete(key);
              console.log('Deleted cached API response:', key.url);
            }
          }
        } catch (cacheError) {
          console.warn('Error clearing cache:', cacheError);
        }
      }

      // Fetch videos again with a cache-busting parameter
      try {
        console.log('Performing primary refresh');
        await fetchVideos(true);
        console.log('Primary refresh completed successfully');
      } catch (primaryError) {
        console.error('Error in primary refresh:', primaryError);
        // Continue with secondary refresh even if primary fails
      }

      // Force another refresh after a delay to ensure new videos are loaded
      setTimeout(() => {
        try {
          console.log('Performing secondary refresh to ensure new videos are loaded');
          fetchVideos(true).then(() => {
            console.log('Secondary refresh completed successfully');
          }).catch((secondaryError) => {
            console.error('Error in secondary refresh:', secondaryError);
          });
        } catch (timeoutError) {
          console.error('Error setting up secondary refresh:', timeoutError);
        }
      }, 2000);

      console.log('Videos refresh process initiated successfully');
    } catch (error) {
      console.error('Error in overall refresh process:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <VideoContext.Provider value={{
      videos,
      trendingVideos,
      categories,
      languages,
      getVideoById,
      getVideosByCategory,
      isLoading,
      refreshVideos
    }}>
      {children}
    </VideoContext.Provider>
  );
};

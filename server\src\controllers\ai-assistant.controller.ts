import { FastifyRequest, FastifyReply } from 'fastify';
import { aiAssistantService } from '../services/ai-assistant.service';

/**
 * AI Assistant controller for handling AI-related requests
 */
export class AIAssistantController {
  /**
   * Send a message to the AI Assistant
   */
  async sendMessage(
    request: FastifyRequest<{
      Body: {
        message: string;
        provider: string;
        apiKey: string;
        endpoint?: string;
        conversationId?: string;
        creatorId: string;
        creatorName?: string; // Added creator name for personalized responses
        videoId?: string;
        chatHistory?: Array<{
          role: 'user' | 'assistant';
          content: string;
        }>;
      };
    }>,
    reply: FastifyReply
  ) {
    try {
      const { message, provider, apiKey, endpoint, conversationId, creatorId, creatorName, videoId, chatHistory } = request.body;

      // Validate required fields
      if (!message || !provider || !apiKey || !creatorId) {
        return reply.code(400).send({
          success: false,
          message: 'Missing required fields'
        });
      }

      // Send message to AI Assistant service
      const response = await aiAssistantService.sendMessage({
        message,
        provider,
        apiKey,
        endpoint,
        conversationId,
        creatorId,
        creatorName, // Pass the creator name for personalized responses
        videoId,
        chatHistory
      });

      return reply.code(200).send({
        success: true,
        data: response
      });
    } catch (error) {
      console.error('Error in chat controller:', error);
      return reply.code(500).send({
        success: false,
        message: 'Failed to process chat message'
      });
    }
  }
}

export const aiAssistantController = new AIAssistantController();

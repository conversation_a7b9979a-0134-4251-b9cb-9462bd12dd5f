import React, { createContext, useContext, useState, useEffect } from 'react';

// Define AI Assistant provider types
export type AIAssistantProvider = 'deepseek' | 'openai' | 'bhashini' | 'custom';

// Define AI Provider configuration interface
export interface AIProviderConfig {
  enabled: boolean;
  apiKey: string;
  endpoint?: string; // Only needed for custom provider
}

// Define AI Providers configuration map
export interface AIProvidersConfig {
  deepseek: AIProviderConfig;
  openai: AIProviderConfig;
  bhashini: AIProviderConfig;
  custom: AIProviderConfig;
}

interface ChatbotContextType {
  // Chatbot
  isChatbotEnabled: boolean;
  enableChatbot: () => void;
  disableChatbot: () => void;
  chatbotApiKey: string;
  setChatbotApiKey: (key: string) => void;
  chatbotSecurityKey: string;
  setChatbotSecurityKey: (key: string) => void;

  // AI Assistant
  isAIAssistantEnabled: boolean;
  enableAIAssistant: () => void;
  disableAIAssistant: () => void;

  // Legacy single provider support (for backward compatibility)
  aiAssistantProvider: AIAssistantProvider;
  setAIAssistantProvider: (provider: AIAssistantProvider) => void;
  aiAssistantApiKey: string;
  setAIAssistantApiKey: (key: string) => void;
  aiAssistantEndpoint: string;
  setAIAssistantEndpoint: (endpoint: string) => void;

  // Multi-provider support
  aiProviders: AIProvidersConfig;
  updateAIProvider: (provider: AIAssistantProvider, config: Partial<AIProviderConfig>) => void;
  getEnabledProviders: () => AIAssistantProvider[];

  // Chatbot Voice Calling
  isVoiceCallingEnabled: boolean;
  enableVoiceCalling: () => void;
  disableVoiceCalling: () => void;
  voiceCallingApiKey: string;
  setVoiceCallingApiKey: (key: string) => void;
  voiceCallingSecurityKey: string;
  setVoiceCallingSecurityKey: (key: string) => void;

  // Other APIs toggle states
  isChattingEnabled: boolean;
  setChattingEnabled: (enabled: boolean) => void;
  isVideoTranslationEnabled: boolean;
  setVideoTranslationEnabled: (enabled: boolean) => void;
  isVoiceTranslationEnabled: boolean;
  setVoiceTranslationEnabled: (enabled: boolean) => void;

  // Other APIs keys
  chattingApiKey: string;
  setChattingApiKey: (key: string) => void;
  chattingSecurityKey: string;
  setChattingSecurityKey: (key: string) => void;

  videoTranslationApiKey: string;
  setVideoTranslationApiKey: (key: string) => void;
  videoTranslationSecurityKey: string;
  setVideoTranslationSecurityKey: (key: string) => void;

  voiceTranslationApiKey: string;
  setVoiceTranslationApiKey: (key: string) => void;
  voiceTranslationSecurityKey: string;
  setVoiceTranslationSecurityKey: (key: string) => void;
}

const ChatbotContext = createContext<ChatbotContextType | undefined>(undefined);

export function ChatbotProvider({ children }: { children: React.ReactNode }) {
  // Chatbot states
  const [isChatbotEnabled, setIsChatbotEnabled] = useState(false);
  const [chatbotApiKey, setChatbotApiKey] = useState('');
  const [chatbotSecurityKey, setChatbotSecurityKey] = useState('');

  // AI Assistant states (legacy single provider)
  const [isAIAssistantEnabled, setIsAIAssistantEnabled] = useState(false);
  const [aiAssistantProvider, setAIAssistantProvider] = useState<AIAssistantProvider>('openai');
  const [aiAssistantApiKey, setAIAssistantApiKey] = useState('');
  const [aiAssistantEndpoint, setAIAssistantEndpoint] = useState('');

  // AI Assistant states (multi-provider)
  const [aiProviders, setAIProviders] = useState<AIProvidersConfig>({
    deepseek: { enabled: false, apiKey: '' },
    openai: { enabled: true, apiKey: 'sk-REPLACE_WITH_YOUR_ACTUAL_OPENAI_API_KEY' },
    bhashini: { enabled: false, apiKey: '' },
    custom: { enabled: false, apiKey: '', endpoint: '' }
  });

  // Voice calling states
  const [isVoiceCallingEnabled, setIsVoiceCallingEnabled] = useState(false);
  const [voiceCallingApiKey, setVoiceCallingApiKey] = useState('');
  const [voiceCallingSecurityKey, setVoiceCallingSecurityKey] = useState('');

  // Other API toggle states
  const [isChattingEnabled, setChattingEnabled] = useState(false);
  const [isVideoTranslationEnabled, setVideoTranslationEnabled] = useState(false);
  const [isVoiceTranslationEnabled, setVoiceTranslationEnabled] = useState(false);

  // Other API keys
  const [chattingApiKey, setChattingApiKey] = useState('');
  const [chattingSecurityKey, setChattingSecurityKey] = useState('');

  const [videoTranslationApiKey, setVideoTranslationApiKey] = useState('');
  const [videoTranslationSecurityKey, setVideoTranslationSecurityKey] = useState('');

  const [voiceTranslationApiKey, setVoiceTranslationApiKey] = useState('');
  const [voiceTranslationSecurityKey, setVoiceTranslationSecurityKey] = useState('');

  // Check local storage on initial load
  useEffect(() => {
    // Load toggle states
    const chatbotEnabled = localStorage.getItem('chatbotEnabled') === 'true';
    const voiceCallingEnabled = localStorage.getItem('voiceCallingEnabled') === 'true';
    const chattingEnabled = localStorage.getItem('chattingEnabled') === 'true';
    const videoTranslationEnabled = localStorage.getItem('videoTranslationEnabled') === 'true';
    const voiceTranslationEnabled = localStorage.getItem('voiceTranslationEnabled') === 'true';
    const aiAssistantEnabled = localStorage.getItem('aiAssistantEnabled') === 'true';

    setIsChatbotEnabled(chatbotEnabled);
    setIsVoiceCallingEnabled(voiceCallingEnabled);
    setChattingEnabled(chattingEnabled);
    setVideoTranslationEnabled(videoTranslationEnabled);
    setVoiceTranslationEnabled(voiceTranslationEnabled);
    setIsAIAssistantEnabled(aiAssistantEnabled);

    // Chatbot keys
    const storedChatbotApiKey = localStorage.getItem('chatbotApiKey');
    const storedChatbotSecurityKey = localStorage.getItem('chatbotSecurityKey');

    if (storedChatbotApiKey) setChatbotApiKey(storedChatbotApiKey);
    if (storedChatbotSecurityKey) setChatbotSecurityKey(storedChatbotSecurityKey);

    // Voice calling keys
    const storedVoiceApiKey = localStorage.getItem('voiceCallingApiKey');
    const storedVoiceSecurityKey = localStorage.getItem('voiceCallingSecurityKey');

    if (storedVoiceApiKey) setVoiceCallingApiKey(storedVoiceApiKey);
    if (storedVoiceSecurityKey) setVoiceCallingSecurityKey(storedVoiceSecurityKey);

    // Chatting keys
    const storedChattingApiKey = localStorage.getItem('chattingApiKey');
    const storedChattingSecurityKey = localStorage.getItem('chattingSecurityKey');

    if (storedChattingApiKey) setChattingApiKey(storedChattingApiKey);
    if (storedChattingSecurityKey) setChattingSecurityKey(storedChattingSecurityKey);

    // Video translation keys
    const storedVideoTranslationApiKey = localStorage.getItem('videoTranslationApiKey');
    const storedVideoTranslationSecurityKey = localStorage.getItem('videoTranslationSecurityKey');

    if (storedVideoTranslationApiKey) setVideoTranslationApiKey(storedVideoTranslationApiKey);
    if (storedVideoTranslationSecurityKey) setVideoTranslationSecurityKey(storedVideoTranslationSecurityKey);

    // Voice translation keys
    const storedVoiceTranslationApiKey = localStorage.getItem('voiceTranslationApiKey');
    const storedVoiceTranslationSecurityKey = localStorage.getItem('voiceTranslationSecurityKey');

    if (storedVoiceTranslationApiKey) setVoiceTranslationApiKey(storedVoiceTranslationApiKey);
    if (storedVoiceTranslationSecurityKey) setVoiceTranslationSecurityKey(storedVoiceTranslationSecurityKey);

    // AI Assistant settings (legacy)
    const storedAIAssistantProvider = localStorage.getItem('aiAssistantProvider') as AIAssistantProvider;
    const storedAIAssistantApiKey = localStorage.getItem('aiAssistantApiKey');
    const storedAIAssistantEndpoint = localStorage.getItem('aiAssistantEndpoint');

    if (storedAIAssistantProvider) setAIAssistantProvider(storedAIAssistantProvider);
    if (storedAIAssistantApiKey) setAIAssistantApiKey(storedAIAssistantApiKey);
    if (storedAIAssistantEndpoint) setAIAssistantEndpoint(storedAIAssistantEndpoint);

    // Load multi-provider AI Assistant settings
    const storedAIProviders = localStorage.getItem('aiProviders');
    if (storedAIProviders) {
      try {
        const parsedProviders = JSON.parse(storedAIProviders) as AIProvidersConfig;
        setAIProviders(parsedProviders);
      } catch (error) {
        console.error('Failed to parse AI providers from localStorage:', error);
      }
    }
  }, []);

  const enableChatbot = () => {
    setIsChatbotEnabled(true);
    localStorage.setItem('chatbotEnabled', 'true');
  };

  const disableChatbot = () => {
    setIsChatbotEnabled(false);
    localStorage.setItem('chatbotEnabled', 'false');
  };

  const enableVoiceCalling = () => {
    setIsVoiceCallingEnabled(true);
    localStorage.setItem('voiceCallingEnabled', 'true');
  };

  const disableVoiceCalling = () => {
    setIsVoiceCallingEnabled(false);
    localStorage.setItem('voiceCallingEnabled', 'false');
  };

  // Update AI Provider configuration
  const updateAIProvider = (provider: AIAssistantProvider, config: Partial<AIProviderConfig>) => {
    setAIProviders(prev => {
      const updatedProviders = {
        ...prev,
        [provider]: {
          ...prev[provider],
          ...config
        }
      };

      // Save to localStorage
      localStorage.setItem('aiProviders', JSON.stringify(updatedProviders));

      return updatedProviders;
    });

    // If any provider is enabled, enable AI Assistant
    if (config.enabled) {
      enableAIAssistant();
    } else {
      // Check if any other provider is enabled
      const anyProviderEnabled = Object.values(aiProviders)
        .filter((_, key) => key !== provider)
        .some(p => p.enabled);

      if (!anyProviderEnabled) {
        disableAIAssistant();
      }
    }
  };

  // Get list of enabled providers
  const getEnabledProviders = (): AIAssistantProvider[] => {
    return Object.entries(aiProviders)
      .filter(([_, config]) => config.enabled)
      .map(([provider]) => provider as AIAssistantProvider);
  };

  const enableAIAssistant = () => {
    setIsAIAssistantEnabled(true);
    localStorage.setItem('aiAssistantEnabled', 'true');
  };

  const disableAIAssistant = () => {
    setIsAIAssistantEnabled(false);
    localStorage.setItem('aiAssistantEnabled', 'false');
  };

  return (
    <ChatbotContext.Provider
      value={{
        // Chatbot
        isChatbotEnabled,
        enableChatbot,
        disableChatbot,
        chatbotApiKey,
        setChatbotApiKey,
        chatbotSecurityKey,
        setChatbotSecurityKey,

        // AI Assistant (legacy single provider)
        isAIAssistantEnabled,
        enableAIAssistant,
        disableAIAssistant,
        aiAssistantProvider,
        setAIAssistantProvider,
        aiAssistantApiKey,
        setAIAssistantApiKey,
        aiAssistantEndpoint,
        setAIAssistantEndpoint,

        // AI Assistant (multi-provider)
        aiProviders,
        updateAIProvider,
        getEnabledProviders,

        // Voice calling
        isVoiceCallingEnabled,
        enableVoiceCalling,
        disableVoiceCalling,
        voiceCallingApiKey,
        setVoiceCallingApiKey,
        voiceCallingSecurityKey,
        setVoiceCallingSecurityKey,

        // Other API toggle states
        isChattingEnabled,
        setChattingEnabled,
        isVideoTranslationEnabled,
        setVideoTranslationEnabled,
        isVoiceTranslationEnabled,
        setVoiceTranslationEnabled,

        // Other API keys
        chattingApiKey,
        setChattingApiKey,
        chattingSecurityKey,
        setChattingSecurityKey,

        videoTranslationApiKey,
        setVideoTranslationApiKey,
        videoTranslationSecurityKey,
        setVideoTranslationSecurityKey,

        voiceTranslationApiKey,
        setVoiceTranslationApiKey,
        voiceTranslationSecurityKey,
        setVoiceTranslationSecurityKey
      }}
    >
      {children}
    </ChatbotContext.Provider>
  );
}

export function useChatbot() {
  const context = useContext(ChatbotContext);
  if (context === undefined) {
    throw new Error('useChatbot must be used within a ChatbotProvider');
  }
  return context;
}

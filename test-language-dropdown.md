# Testing Language Dropdown in Video Player

This document outlines the steps to test the updated language dropdown in the video player.

## Test Plan

1. Add a new video with multiple languages
2. Verify the language count shows on the home page
3. Play the video and verify the language dropdown shows all available languages
4. Test language switching during playback

## Step 1: Add a New Video with Multiple Languages

1. Navigate to the Creator Studio page
2. Click on "Add Video"
3. Enter the following information:
   - Engaxe URL: `https://engaxe.com/v/7mXOfb` (or any valid Engaxe URL)
   - Title: "Test Video with Multiple Languages"
   - Description: "This is a test video with multiple languages"
   - Category: "Education"
   - Default Language: "English"
   - Default Language URL: `https://engaxe.com/v/7mXOfb`
4. Add additional languages:
   - Hindi with URL: `https://engaxe.com/v/tzbtmk`
   - Spanish with URL: `https://engaxe.com/v/xW36l7`
5. Save the video

## Step 2: Verify Language Count on Home Page

1. Navigate to the Home page
2. Find the newly added video
3. Verify that the language count badge shows "3 languages"

## Step 3: Verify Language Dropdown Shows All Languages

1. Click on the video to open the video page
2. Open the browser console to monitor the logs
3. Verify that the language dropdown button shows the default language (English)
4. Click on the language dropdown button
5. Verify that the dropdown shows all 3 languages (English, Hindi, Spanish)
6. Verify that the dropdown header shows "Select Language (3 available)"

## Step 4: Test Language Switching During Playback

1. Click on the Hindi language option in the dropdown
2. Verify in the console that:
   - The URL is properly extracted: `Extracted Engaxe ID from language URL: tzbtmk (original: https://engaxe.com/v/tzbtmk)`
   - The iframe source is updated: `Updating iframe source to: https://engaxe.com/e/tzbtmk`
3. Verify that the video source changes to the Hindi version
4. Click on the language dropdown button again
5. Click on the Spanish language option in the dropdown
6. Verify in the console that:
   - The URL is properly extracted: `Extracted Engaxe ID from language URL: xW36l7 (original: https://engaxe.com/v/xW36l7)`
   - The iframe source is updated: `Updating iframe source to: https://engaxe.com/e/xW36l7`
7. Verify that the video source changes to the Spanish version

## Expected Results

- The video should be saved with all three languages
- The home page should display the correct language count (3 languages)
- The language dropdown should show all 3 languages with their correct names and flags
- The dropdown header should show "Select Language (3 available)"
- When switching languages during playback, the video source should change to the corresponding Engaxe URL
- The console should show the proper extraction of Engaxe IDs from URLs

import { FastifyRequest, FastifyReply, RouteGenericInterface } from 'fastify';
import { AuthenticatedUser } from '../types/user';

// Define a custom request type with authenticated user
interface AuthenticatedRequest<T extends RouteGenericInterface = RouteGenericInterface> extends FastifyRequest<T> {
  user: AuthenticatedUser;
}
import { ChannelService } from '../services/channel.service';
import { IChannel } from '../models';

/**
 * Controller for channel-related endpoints
 */
export class ChannelController {
  private channelService: ChannelService;

  constructor() {
    this.channelService = new ChannelService();
  }

  /**
   * Create a new channel
   */
  createChannel = async (request: AuthenticatedRequest<{
    Body: {
      name: string;
      displayName?: string;
      description: string;
      visibility?: 'public' | 'private' | 'unlisted';
      avatar?: string;
      banner?: string;
      category?: string;
      tags?: string[];
      socialLinks?: {
        website?: string;
        facebook?: string;
        twitter?: string;
        instagram?: string;
        linkedin?: string;
        youtube?: string;
      };
      settings?: {
        defaultCommentsEnabled?: boolean;
        moderateComments?: boolean;
        showSubscriberCount?: boolean;
      };
    }
  }>, reply: FastifyReply) => {
    try {
      // Get user ID from authenticated request
      const userId = request.user.id;

      // Create channel
      const channel = await this.channelService.createChannel(userId, request.body);

      return reply.code(201).send({
        success: true,
        message: 'Channel created successfully',
        channel: {
          id: channel.id,
          name: channel.name,
          displayName: channel.displayName,
          description: channel.description,
          avatar: channel.avatar,
          banner: channel.banner,
          ownerId: channel.ownerId,
          category: channel.category,
          tags: channel.tags,
          isVerified: channel.isVerified,
          isFeatured: channel.isFeatured,
          status: channel.status,
          createdAt: channel.createdAt,
          updatedAt: channel.updatedAt,
        },
      });
    } catch (error: any) {
      request.log.error(error);

      // Handle specific error types
      if (error.statusCode === 409) {
        return reply.code(409).send({
          success: false,
          message: error.message || 'Channel name already in use',
          error: {
            code: error.errorCode || 'CONFLICT',
            message: error.message,
          },
        });
      }

      return reply.code(400).send({
        success: false,
        message: error.message || 'Failed to create channel',
        error: {
          code: error.errorCode || 'BAD_REQUEST',
          message: error.message,
        },
      });
    }
  };

  /**
   * Update an existing channel
   */
  updateChannel = async (request: AuthenticatedRequest<{
    Params: {
      id: string;
    };
    Body: {
      name?: string;
      displayName?: string;
      description?: string;
      avatar?: string;
      banner?: string;
      category?: string;
      tags?: string[];
      socialLinks?: {
        website?: string;
        facebook?: string;
        twitter?: string;
        instagram?: string;
        linkedin?: string;
        youtube?: string;
      };
      settings?: {
        defaultCommentsEnabled?: boolean;
        moderateComments?: boolean;
        showSubscriberCount?: boolean;
      };
    }
  }>, reply: FastifyReply) => {
    try {
      // Get user ID from authenticated request
      const userId = request.user.id;
      const channelId = request.params.id;

      // Update channel
      const channel = await this.channelService.updateChannel(userId, channelId, request.body);

      return reply.code(200).send({
        success: true,
        message: 'Channel updated successfully',
        channel: {
          id: channel.id,
          name: channel.name,
          displayName: channel.displayName,
          description: channel.description,
          avatar: channel.avatar,
          banner: channel.banner,
          ownerId: channel.ownerId,
          category: channel.category,
          tags: channel.tags,
          isVerified: channel.isVerified,
          isFeatured: channel.isFeatured,
          status: channel.status,
          createdAt: channel.createdAt,
          updatedAt: channel.updatedAt,
        },
      });
    } catch (error: any) {
      request.log.error(error);

      // Handle specific error types
      if (error.statusCode === 404) {
        return reply.code(404).send({
          success: false,
          message: error.message || 'Channel not found',
          error: {
            code: error.errorCode || 'NOT_FOUND',
            message: error.message,
          },
        });
      } else if (error.statusCode === 403) {
        return reply.code(403).send({
          success: false,
          message: error.message || 'Permission denied',
          error: {
            code: error.errorCode || 'FORBIDDEN',
            message: error.message,
          },
        });
      } else if (error.statusCode === 409) {
        return reply.code(409).send({
          success: false,
          message: error.message || 'Channel name already in use',
          error: {
            code: error.errorCode || 'CONFLICT',
            message: error.message,
          },
        });
      }

      return reply.code(400).send({
        success: false,
        message: error.message || 'Failed to update channel',
        error: {
          code: error.errorCode || 'BAD_REQUEST',
          message: error.message,
        },
      });
    }
  };

  /**
   * Get a channel by ID
   */
  getChannelById = async (request: FastifyRequest<{
    Params: {
      id: string;
    }
  }>, reply: FastifyReply) => {
    try {
      const channelId = request.params.id;
      const channel = await this.channelService.getChannelById(channelId);

      return reply.code(200).send({
        success: true,
        channel: {
          id: channel.id,
          name: channel.name,
          displayName: channel.displayName,
          description: channel.description,
          avatar: channel.avatar,
          banner: channel.banner,
          ownerId: channel.ownerId,
          category: channel.category,
          tags: channel.tags,
          isVerified: channel.isVerified,
          isFeatured: channel.isFeatured,
          status: channel.status,
          stats: channel.stats,
          socialLinks: channel.socialLinks,
          createdAt: channel.createdAt,
          updatedAt: channel.updatedAt,
        },
      });
    } catch (error: any) {
      request.log.error(error);

      if (error.statusCode === 404) {
        return reply.code(404).send({
          success: false,
          message: error.message || 'Channel not found',
          error: {
            code: error.errorCode || 'NOT_FOUND',
            message: error.message,
          },
        });
      }

      return reply.code(400).send({
        success: false,
        message: error.message || 'Failed to get channel',
        error: {
          code: error.errorCode || 'BAD_REQUEST',
          message: error.message,
        },
      });
    }
  };

  /**
   * Get a channel by name
   */
  getChannelByName = async (request: FastifyRequest<{
    Params: {
      name: string;
    }
  }>, reply: FastifyReply) => {
    try {
      const channelName = request.params.name;
      const channel = await this.channelService.getChannelByName(channelName);

      return reply.code(200).send({
        success: true,
        channel: {
          id: channel.id,
          name: channel.name,
          displayName: channel.displayName,
          description: channel.description,
          avatar: channel.avatar,
          banner: channel.banner,
          ownerId: channel.ownerId,
          category: channel.category,
          tags: channel.tags,
          isVerified: channel.isVerified,
          isFeatured: channel.isFeatured,
          status: channel.status,
          stats: channel.stats,
          socialLinks: channel.socialLinks,
          createdAt: channel.createdAt,
          updatedAt: channel.updatedAt,
        },
      });
    } catch (error: any) {
      request.log.error(error);

      if (error.statusCode === 404) {
        return reply.code(404).send({
          success: false,
          message: error.message || 'Channel not found',
          error: {
            code: error.errorCode || 'NOT_FOUND',
            message: error.message,
          },
        });
      }

      return reply.code(400).send({
        success: false,
        message: error.message || 'Failed to get channel',
        error: {
          code: error.errorCode || 'BAD_REQUEST',
          message: error.message,
        },
      });
    }
  };

  /**
   * Get channels with pagination and filtering
   */
  getChannels = async (request: FastifyRequest<{
    Querystring: {
      page?: number | string;
      limit?: number | string;
      sort?: 'newest' | 'popular' | 'trending';
      category?: string;
      featured?: boolean | string;
      search?: string;
    }
  }>, reply: FastifyReply) => {
    // Convert string parameters to their proper types
    const queryParams = {
      ...request.query,
      page: request.query.page ? Number(request.query.page) : undefined,
      limit: request.query.limit ? Number(request.query.limit) : undefined,
      featured: request.query.featured === 'true' ? true : request.query.featured === 'false' ? false : request.query.featured,
    };
    try {
      const { channels, pagination } = await this.channelService.getChannels(queryParams);

      return reply.code(200).send({
        success: true,
        channels: channels.map((channel: IChannel) => ({
          id: channel.id,
          name: channel.name,
          displayName: channel.displayName,
          description: channel.description,
          avatar: channel.avatar,
          banner: channel.banner,
          ownerId: channel.ownerId,
          category: channel.category,
          tags: channel.tags,
          isVerified: channel.isVerified,
          isFeatured: channel.isFeatured,
          stats: channel.stats,
          createdAt: channel.createdAt,
        })),
        pagination,
      });
    } catch (error: any) {
      request.log.error(error);

      return reply.code(400).send({
        success: false,
        message: error.message || 'Failed to get channels',
        error: {
          code: error.errorCode || 'BAD_REQUEST',
          message: error.message,
        },
      });
    }
  };

  /**
   * Get channels by user ID
   */
  getUserChannels = async (request: FastifyRequest<{
    Params: {
      userId: string;
    }
  }>, reply: FastifyReply) => {
    try {
      const userId = request.params.userId;
      const channels = await this.channelService.getUserChannels(userId);

      return reply.code(200).send({
        success: true,
        channels: channels.map((channel: IChannel) => ({
          id: channel.id,
          name: channel.name,
          displayName: channel.displayName,
          description: channel.description,
          avatar: channel.avatar,
          category: channel.category,
          isVerified: channel.isVerified,
          isFeatured: channel.isFeatured,
          stats: channel.stats,
          createdAt: channel.createdAt,
        })),
      });
    } catch (error: any) {
      request.log.error(error);

      return reply.code(400).send({
        success: false,
        message: error.message || 'Failed to get user channels',
        error: {
          code: error.errorCode || 'BAD_REQUEST',
          message: error.message,
        },
      });
    }
  };

  /**
   * Get channels for the current authenticated user
   */
  getCurrentUserChannels = async (request: AuthenticatedRequest<{}>, reply: FastifyReply) => {
    try {
      // Get user ID from authenticated request
      const userId = request.user.id;
      console.log(`Getting channels for authenticated user: ${userId}`);

      const channels = await this.channelService.getUserChannels(userId);
      console.log(`Found ${channels.length} channels for user ${userId}`);

      // If no channels found, create a default one
      if (channels.length === 0) {
        console.log(`No channels found for user ${userId}, creating a default channel`);

        // Create a default channel with all required fields
        const defaultChannel = await this.channelService.createChannel(userId, {
          name: `channel-${userId.substring(0, 8)}`,
          displayName: 'My Channel',
          description: 'My default channel',
          visibility: 'public',
          tags: [],
        });

        console.log(`Created default channel with ID: ${defaultChannel.id}`);

        return reply.code(200).send({
          success: true,
          channels: [{
            id: defaultChannel.id,
            name: defaultChannel.name,
            displayName: defaultChannel.displayName,
            description: defaultChannel.description,
            avatar: defaultChannel.avatar,
            category: defaultChannel.category,
            isVerified: defaultChannel.isVerified,
            isFeatured: defaultChannel.isFeatured,
            stats: defaultChannel.stats,
            createdAt: defaultChannel.createdAt,
          }],
        });
      }

      return reply.code(200).send({
        success: true,
        channels: channels.map((channel: IChannel) => ({
          id: channel.id,
          name: channel.name,
          displayName: channel.displayName,
          description: channel.description,
          avatar: channel.avatar,
          category: channel.category,
          isVerified: channel.isVerified,
          isFeatured: channel.isFeatured,
          stats: channel.stats,
          createdAt: channel.createdAt,
        })),
      });
    } catch (error: any) {
      request.log.error(error);

      return reply.code(400).send({
        success: false,
        message: error.message || 'Failed to get user channels',
        error: {
          code: error.errorCode || 'BAD_REQUEST',
          message: error.message,
        },
      });
    }
  };

  /**
   * Delete a channel
   */
  deleteChannel = async (request: AuthenticatedRequest<{
    Params: {
      id: string;
    }
  }>, reply: FastifyReply) => {
    try {
      // Get user ID from authenticated request
      const userId = request.user.id;
      const channelId = request.params.id;

      // Delete channel
      await this.channelService.deleteChannel(userId, channelId);

      return reply.code(200).send({
        success: true,
        message: 'Channel deleted successfully',
      });
    } catch (error: any) {
      request.log.error(error);

      // Handle specific error types
      if (error.statusCode === 404) {
        return reply.code(404).send({
          success: false,
          message: error.message || 'Channel not found',
          error: {
            code: error.errorCode || 'NOT_FOUND',
            message: error.message,
          },
        });
      } else if (error.statusCode === 403) {
        return reply.code(403).send({
          success: false,
          message: error.message || 'Permission denied',
          error: {
            code: error.errorCode || 'FORBIDDEN',
            message: error.message,
          },
        });
      }

      return reply.code(400).send({
        success: false,
        message: error.message || 'Failed to delete channel',
        error: {
          code: error.errorCode || 'BAD_REQUEST',
          message: error.message,
        },
      });
    }
  };

  /**
   * Subscribe or unsubscribe from a channel
   */
  toggleSubscription = async (request: AuthenticatedRequest<{
    Params: {
      id: string;
    }
  }>, reply: FastifyReply) => {
    try {
      // Get user ID from authenticated request
      const userId = request.user.id;
      const channelId = request.params.id;

      // Toggle subscription
      const result = await this.channelService.toggleSubscription(userId, channelId);

      return reply.code(200).send({
        success: true,
        message: result.message,
        isSubscribed: result.isSubscribed,
      });
    } catch (error: any) {
      request.log.error(error);

      // Handle specific error types
      if (error.statusCode === 404) {
        return reply.code(404).send({
          success: false,
          message: error.message || 'Channel not found',
          error: {
            code: error.errorCode || 'NOT_FOUND',
            message: error.message,
          },
        });
      } else if (error.statusCode === 400) {
        return reply.code(400).send({
          success: false,
          message: error.message || 'Invalid operation',
          error: {
            code: error.errorCode || 'BAD_REQUEST',
            message: error.message,
          },
        });
      }

      return reply.code(400).send({
        success: false,
        message: error.message || 'Failed to update subscription',
        error: {
          code: error.errorCode || 'BAD_REQUEST',
          message: error.message,
        },
      });
    }
  };
}

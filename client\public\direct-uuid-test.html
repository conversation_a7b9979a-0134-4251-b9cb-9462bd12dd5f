<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Direct UUID Video Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      background-color: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    h1 {
      text-align: center;
      color: #333;
    }
    .video-container {
      position: relative;
      width: 100%;
      aspect-ratio: 16/9;
      background-color: black;
      margin: 20px 0;
      overflow: hidden;
    }
    iframe {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border: none;
    }
    .controls {
      margin-top: 20px;
    }
    input {
      width: 100%;
      padding: 10px;
      margin-bottom: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    button {
      background-color: #4CAF50;
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
    }
    button:hover {
      background-color: #45a049;
    }
    .test-buttons {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 10px;
      margin-top: 20px;
    }
    .test-button {
      background-color: #2196F3;
    }
    .test-button:hover {
      background-color: #0b7dda;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Direct UUID Video Test</h1>
    
    <div class="video-container" id="video-container">
      <!-- Video will be embedded here -->
    </div>
    
    <div class="controls">
      <input type="text" id="uuid-input" placeholder="Enter UUID format video ID" value="8f23eb35-db65-44cb-906d-24356f2edaf2">
      <button id="play-button">Play Video</button>
      
      <div class="test-buttons">
        <button class="test-button" data-uuid="8f23eb35-db65-44cb-906d-24356f2edaf2">Test UUID 1</button>
        <button class="test-button" data-uuid="4f6c268e-8f71-4615-bbcb-1d333823690e">Test UUID 2</button>
        <button class="test-button" data-uuid="wollzl">Known Working ID</button>
        <button class="test-button" data-uuid="XLcMq2">Another Working ID</button>
      </div>
    </div>
  </div>

  <script>
    // Function to play a video with the given UUID
    function playVideo(uuid) {
      const container = document.getElementById('video-container');
      
      // Clear the container
      container.innerHTML = '';
      
      // Create a new iframe
      const iframe = document.createElement('iframe');
      iframe.src = `https://engaxe.com/embed/${uuid}?autoplay=1&controls=1`;
      iframe.allowFullscreen = true;
      iframe.allow = 'encrypted-media; picture-in-picture; autoplay';
      iframe.setAttribute('frameborder', '0');
      iframe.setAttribute('scrolling', 'no');
      iframe.setAttribute('seamless', 'seamless');
      
      // Add the iframe to the container
      container.appendChild(iframe);
      
      console.log(`Playing video with UUID: ${uuid}`);
    }
    
    // Set up event listeners
    document.getElementById('play-button').addEventListener('click', function() {
      const uuid = document.getElementById('uuid-input').value.trim();
      playVideo(uuid);
    });
    
    // Set up test button event listeners
    document.querySelectorAll('.test-button').forEach(button => {
      button.addEventListener('click', function() {
        const uuid = this.getAttribute('data-uuid');
        document.getElementById('uuid-input').value = uuid;
        playVideo(uuid);
      });
    });
    
    // Auto-play the default video on page load
    window.addEventListener('DOMContentLoaded', function() {
      document.getElementById('play-button').click();
    });
  </script>
</body>
</html>

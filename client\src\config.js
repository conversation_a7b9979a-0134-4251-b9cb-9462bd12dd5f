/**
 * Application configuration
 */

// API base URL
export const API_BASE_URL = 'http://localhost:3001/api/v1';

// WebSocket URL
export const WS_URL = 'ws://localhost:3001/ws/chat';

// Default pagination settings
export const DEFAULT_PAGE_SIZE = 10;

// File upload limits
export const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
export const ALLOWED_FILE_TYPES = [
  'image/jpeg',
  'image/png',
  'image/gif',
  'audio/mpeg',
  'audio/wav',
  'video/mp4',
  'application/pdf',
];

// Avatar placeholder URL
export const DEFAULT_AVATAR_URL = 'https://ui-avatars.com/api/?background=random';

// Debounce delay for search inputs (in milliseconds)
export const SEARCH_DEBOUNCE_DELAY = 300;

// WebSocket ping interval (in milliseconds)
export const WS_PING_INTERVAL = 30000; // 30 seconds

// Typing indicator timeout (in milliseconds)
export const TYPING_INDICATOR_TIMEOUT = 3000; // 3 seconds

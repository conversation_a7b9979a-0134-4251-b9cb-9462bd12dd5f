import { useState, useEffect, useRef } from 'react';
import { MessageCir<PERSON>, X, Send, ArrowR<PERSON>, Mic, MicOff } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardFooter, CardHeader } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useAuth } from '@/context/AuthContext';
import { useChatbot, AIAssistantProvider } from '@/context/ChatbotContext';
import { useMessages } from '@/context/MessageContext';
import { useToast } from '@/hooks/use-toast';
import { User, Video, Conversation, Message as MessageType } from '@/types';
import aiAssistantAPI from '@/services/aiAssistantApi';
import { useNavigate } from 'react-router-dom';

interface CreatorChatProps {
  isOpen?: boolean;
  onClose?: () => void;
  creator: User;
  video?: Video;
}

export default function CreatorChat({ isOpen = false, onClose, creator, video }: CreatorChatProps) {
  const { currentUser } = useAuth();
  const {
    isAIAssistantEnabled,
    aiAssistantProvider,
    aiAssistantApiKey,
    aiAssistantEndpoint,
    aiProviders,
    getEnabledProviders
  } = useChatbot();

  // Get the first enabled provider or fall back to the legacy provider
  const enabledProviders = getEnabledProviders();
  const activeProvider = enabledProviders.length > 0 ? enabledProviders[0] : aiAssistantProvider;
  const providerConfig = aiProviders[activeProvider];
  const { createConversation, conversations, setActiveConversation } = useMessages();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [isExpanded, setIsExpanded] = useState(isOpen);
  const [message, setMessage] = useState('');
  const [isSending, setIsSending] = useState(false);
  const [conversationId, setConversationId] = useState<string | null>(null);
  const [existingConversation, setExistingConversation] = useState<Conversation | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<BlobPart[]>([]);
  const inputRef = useRef<HTMLInputElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Get the best name to display for the creator
  const creatorDisplayName = creator.displayName || creator.username || 'Video Creator';

  const [chatHistory, setChatHistory] = useState<Array<{
    role: 'user' | 'assistant';
    content: string;
    timestamp: string;
  }>>([
    {
      role: 'assistant',
      content: `Hello! Thanks for reaching out. How can I help you with questions about ${video?.title || 'this content'}?`,
      timestamp: new Date().toISOString()
    }
  ]);

  // Check for existing conversations with this creator
  useEffect(() => {
    const existingConv = conversations.find(c =>
      c.participants.some(p => p.id === creator.id)
    );

    if (existingConv) {
      console.log('Found existing conversation with creator:', existingConv.id);
      setExistingConversation(existingConv);
      setConversationId(existingConv.id);
    }
  }, [creator.id, conversations]);

  // Update expanded state when isOpen prop changes
  useEffect(() => {
    setIsExpanded(isOpen);
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  // Scroll to bottom when chat history changes
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [chatHistory]);

  const handleClose = () => {
    setIsExpanded(false);
    if (onClose) onClose();
  };

  const handleSendMessage = async () => {
    if (!message.trim() || isSending) return;

    if (!currentUser) {
      toast({
        title: "Login Required",
        description: "Please login to chat with creators",
        variant: "destructive"
      });
      return;
    }

    // Add user message to chat
    const userMessage = {
      role: 'user' as const,
      content: message,
      timestamp: new Date().toISOString()
    };

    setChatHistory(prev => [...prev, userMessage]);
    setMessage('');
    setIsSending(true);

    try {
      // If AI Assistant is enabled, use it to respond
      if (isAIAssistantEnabled) {
        // Format chat history for API
        const formattedHistory = chatHistory.map(msg => ({
          role: msg.role,
          content: msg.content
        }));

        // Send message to AI Assistant using the active provider
        const response = await aiAssistantAPI.sendMessage({
          message: message,
          provider: activeProvider,
          apiKey: providerConfig.apiKey || aiAssistantApiKey, // Fall back to legacy key if needed
          endpoint: activeProvider === 'custom' ? (providerConfig.endpoint || aiAssistantEndpoint) : undefined,
          conversationId: conversationId || undefined,
          creatorId: creator.id,
          creatorName: creatorDisplayName, // Pass the creator's display name
          videoId: video?.id,
          chatHistory: formattedHistory
        });

        if (response.success) {
          // Add assistant response to chat
          setChatHistory(prev => [
            ...prev,
            {
              role: 'assistant',
              content: response.data.response,
              timestamp: response.data.timestamp
            }
          ]);

          // Save conversation ID for future messages
          if (!conversationId) {
            setConversationId(response.data.conversationId);
          }

          // Also store this in the messaging system
          try {
            // Get the formatted subject line
            const subject = video ? `Chat about: ${video.title}` : `Chat with ${creatorDisplayName}`;

            // If we have an existing conversation, use it, otherwise create a new one
            if (existingConversation) {
              console.log('Adding message to existing conversation:', existingConversation.id);
              // The message will be added to the existing conversation
              await createConversation({
                creatorId: creator.id,
                subject,
                initialMessage: message
              });
            } else {
              // Create a new conversation with the creator
              const result = await createConversation({
                creatorId: creator.id,
                subject,
                initialMessage: message
              });

              if (result?.success && result.data) {
                setExistingConversation(result.data);
                setConversationId(result.data.id);
              }
            }
          } catch (error) {
            console.error('Failed to save chat to messaging system:', error);
          }
        } else {
          // Show error message
          toast({
            title: "Error",
            description: "Failed to send message. Please try again.",
            variant: "destructive"
          });

          // Add error message to chat
          setChatHistory(prev => [
            ...prev,
            {
              role: 'assistant',
              content: "I'm sorry, I couldn't process your request. Please try again later.",
              timestamp: new Date().toISOString()
            }
          ]);
        }
      } else {
        // If AI Assistant is not enabled, use regular messaging
        try {
          // Get the formatted subject line
          const subject = video ? `Message about: ${video.title}` : `Message to ${creatorDisplayName}`;

          // If we have an existing conversation, use it, otherwise create a new one
          if (existingConversation) {
            console.log('Adding message to existing conversation:', existingConversation.id);
            // The message will be added to the existing conversation
            await createConversation({
              creatorId: creator.id,
              subject,
              initialMessage: message
            });
          } else {
            // Create a new conversation with the creator
            const result = await createConversation({
              creatorId: creator.id,
              subject,
              initialMessage: message
            });

            if (result?.success && result.data) {
              setExistingConversation(result.data);
              setConversationId(result.data.id);
            }
          }

          // Add a confirmation message to the chat
          setChatHistory(prev => [
            ...prev,
            {
              role: 'assistant',
              content: "Your message has been sent. You can view your conversation in the messages section.",
              timestamp: new Date().toISOString()
            }
          ]);

          // Show success toast
          toast({
            title: "Message Sent",
            description: `Your message has been sent to ${creatorDisplayName}`,
          });

          // Optionally navigate to messages page after a delay
          setTimeout(() => {
            handleClose();
            navigate('/messages');
          }, 3000);
        } catch (error) {
          console.error('Failed to send message:', error);

          // Show error message
          toast({
            title: "Error",
            description: "Failed to send your message. Please try again.",
            variant: "destructive"
          });

          // Add error message to chat
          setChatHistory(prev => [
            ...prev,
            {
              role: 'assistant',
              content: "I'm sorry, there was an error sending your message. Please try again later.",
              timestamp: new Date().toISOString()
            }
          ]);
        }
      }
    } catch (error) {
      console.error('Error in chat:', error);

      // Show error message
      toast({
        title: "Error",
        description: "An error occurred while sending your message. Please try again.",
        variant: "destructive"
      });

      // Add error message to chat
      setChatHistory(prev => [
        ...prev,
        {
          role: 'assistant',
          content: "I'm sorry, there was an error processing your message. Please try again later.",
          timestamp: new Date().toISOString()
        }
      ]);
    } finally {
      setIsSending(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Voice recording functions
  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      audioChunksRef.current = [];
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });
        setAudioBlob(audioBlob);

        // Convert audio to text using OpenAI Whisper API
        transcribeAudio(audioBlob);

        // Stop all tracks in the stream to release the microphone
        stream.getTracks().forEach(track => track.stop());
      };

      mediaRecorder.start();
      setIsRecording(true);

      toast({
        title: "Recording Started",
        description: "Speak clearly into your microphone"
      });
    } catch (error) {
      console.error('Error starting recording:', error);
      toast({
        title: "Recording Error",
        description: "Could not access your microphone",
        variant: "destructive"
      });
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      toast({
        title: "Recording Stopped",
        description: "Processing your audio..."
      });
    }
  };

  const transcribeAudio = async (audioBlob: Blob) => {
    try {
      setIsSending(true);

      // Log the API key (masked for security) to help with debugging
      const apiKey = aiProviders.openai.apiKey || aiAssistantApiKey;
      const maskedKey = apiKey ? `${apiKey.substring(0, 3)}...${apiKey.substring(apiKey.length - 4)}` : 'not set';
      console.log(`Using OpenAI API key: ${maskedKey}`);

      if (!apiKey || apiKey.includes('REPLACE_WITH_YOUR_ACTUAL_OPENAI_API_KEY')) {
        // If API key is not set or is still the placeholder, show a helpful error
        toast({
          title: "API Key Not Configured",
          description: "Please set your OpenAI API key in the settings",
          variant: "destructive"
        });
        throw new Error('OpenAI API key not properly configured');
      }

      // Create a FormData object to send the audio file
      const formData = new FormData();
      formData.append('file', audioBlob, 'recording.webm');
      formData.append('model', 'whisper-1');

      // Log the audio blob details to help with debugging
      console.log(`Audio blob size: ${audioBlob.size} bytes, type: ${audioBlob.type}`);

      // For debugging - convert small audio to base64 to check content
      if (audioBlob.size < 100000) { // Only for small files
        const reader = new FileReader();
        reader.readAsDataURL(audioBlob);
        reader.onloadend = () => {
          console.log('Audio sample (base64):', reader.result?.toString().substring(0, 50) + '...');
        };
      }

      // Send to OpenAI Whisper API
      console.log('Sending request to OpenAI Whisper API...');
      const response = await fetch('https://api.openai.com/v1/audio/transcriptions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`
        },
        body: formData
      });

      if (!response.ok) {
        // Get detailed error information
        const errorText = await response.text();
        console.error(`Transcription failed: Status ${response.status}, Response:`, errorText);

        let errorMessage = `Error ${response.status}: ${response.statusText}`;
        try {
          // Try to parse the error as JSON for more details
          const errorJson = JSON.parse(errorText);
          if (errorJson.error && errorJson.error.message) {
            errorMessage = errorJson.error.message;
          }
        } catch (e) {
          // If parsing fails, use the raw text
          if (errorText) errorMessage += ` - ${errorText}`;
        }

        toast({
          title: "Transcription Error",
          description: errorMessage,
          variant: "destructive"
        });

        throw new Error(`Transcription failed: ${errorMessage}`);
      }

      const data = await response.json();
      console.log('Transcription successful:', data);

      // Set the transcribed text as the message
      if (data.text) {
        setMessage(data.text);
        toast({
          title: "Transcription Successful",
          description: "Your speech has been converted to text",
          variant: "default"
        });
        // Automatically send the transcribed message
        setTimeout(() => {
          handleSendMessage();
        }, 500);
      } else {
        toast({
          title: "Transcription Error",
          description: "Could not transcribe your audio - no text returned",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Error transcribing audio:', error);
      toast({
        title: "Transcription Error",
        description: error instanceof Error ? error.message : "Failed to convert speech to text",
        variant: "destructive"
      });
    } finally {
      setIsSending(false);
    }
  };

  // Collapsed state (chat bubble)
  if (!isExpanded) {
    return (
      <div className="fixed bottom-6 right-6 z-50">
        <Button
          className="h-14 w-14 rounded-full bg-orange-500 shadow-lg hover:bg-orange-600 text-white"
          onClick={() => setIsExpanded(true)}
        >
          <MessageCircle className="h-6 w-6" />
        </Button>
      </div>
    );
  }

  // Expanded state (chat window)
  return (
    <div className="fixed bottom-6 right-6 z-50">
      <Card className="w-80 h-96 shadow-xl flex flex-col">
        <CardHeader className="p-3 border-b flex justify-between items-center">
          <div className="flex items-center gap-2">
            <Avatar className="h-8 w-8">
              <AvatarImage src={creator.avatar} alt={creator.username} />
              <AvatarFallback>{creator.username[0]}</AvatarFallback>
            </Avatar>
            <div>
              <span className="font-medium">{creatorDisplayName}</span>
              <div className="text-xs text-muted-foreground">
                <span>Online</span>
              </div>
            </div>
          </div>
          <div className="flex items-center gap-1">
            {existingConversation && (
              <Button
                variant="ghost"
                size="icon"
                title="View full conversation history"
                onClick={() => {
                  setActiveConversation(existingConversation.id);
                  navigate('/messages');
                }}
              >
                <ArrowRight className="h-4 w-4" />
              </Button>
            )}
            <Button variant="ghost" size="icon" onClick={handleClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>

        <CardContent className="flex-1 overflow-y-auto p-3 space-y-4">
          {chatHistory.map((msg, index) => (
            <div
              key={index}
              className={`flex ${msg.role === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-[80%] p-2 rounded-lg ${
                  msg.role === 'user'
                    ? 'bg-primary text-primary-foreground'
                    : 'bg-muted'
                }`}
              >
                {msg.content}
              </div>
            </div>
          ))}
          <div ref={messagesEndRef} />
        </CardContent>

        <CardFooter className="p-3 border-t">
          <div className="flex w-full gap-2">
            <Button
              variant="ghost"
              size="icon"
              className={isRecording ? "text-red-500 bg-red-500 bg-opacity-20" : ""}
              onClick={isRecording ? stopRecording : startRecording}
              title={isRecording ? "Stop recording" : "Start voice recording"}
            >
              {isRecording ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
            </Button>
            <Input
              ref={inputRef}
              placeholder={isRecording ? "Recording..." : `Ask about this video...`}
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyDown={handleKeyPress}
              disabled={isSending || isRecording}
              className="flex-1"
            />
            <Button
              size="icon"
              onClick={handleSendMessage}
              disabled={(!message.trim() || isSending) && !isRecording}
              className="bg-orange-500 hover:bg-orange-600 text-white"
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}

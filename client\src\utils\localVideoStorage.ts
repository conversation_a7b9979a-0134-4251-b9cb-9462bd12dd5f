import { Video, User, Language } from '@/types';

/**
 * Save a video to local storage
 */
export const saveVideoLocally = (
  video: {
    title: string;
    description: string;
    thumbnail: string | null;
    category: string;
    url: string;
    languages?: Language[];
    defaultLanguage?: string;
    additionalLanguages?: {
      id: string;
      languageCode: string;
      url: string;
    }[];
  },
  currentUser: User | null | undefined,
  availableLanguages: Language[] = [{ code: 'en', name: 'English', flag: '🇺🇸' }]
): Video => {
  // Generate a unique ID for the video
  const videoId = `local-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

  // Determine languages for the video
  let videoLanguages: Language[] = [];

  // If languages are directly provided, use them
  if (video.languages && video.languages.length > 0) {
    videoLanguages = video.languages;
  }

  // If additionalLanguages are provided, add them with their URLs
  if (video.additionalLanguages && video.additionalLanguages.length > 0) {
    // Start with the default language
    const defaultLang = availableLanguages.find(lang => lang.code === video.defaultLanguage);
    if (defaultLang) {
      videoLanguages = [{
        ...defaultLang,
        url: video.url // Use the main URL for default language
      }];
      console.log(`Added default language ${defaultLang.name} with URL: ${video.url}`);
    }

    // Add additional languages with their URLs
    video.additionalLanguages.forEach(entry => {
      const lang = availableLanguages.find(l => l.code === entry.languageCode);
      if (lang && entry.url) {
        const languageWithUrl = {
          ...lang,
          url: entry.url
        };
        videoLanguages.push(languageWithUrl);
        console.log(`Added language ${lang.name} with URL: ${entry.url}`);
      } else if (lang) {
        console.warn(`Language ${lang.name} has no URL, skipping`);
      }
    });

    // Log all languages with their URLs
    console.log('Video languages with URLs:',
      videoLanguages.map(l => `${l.name}: ${l.url || 'No URL'}`).join(', ')
    );
  }
  // Otherwise, try to build from defaultLanguage and additionalLanguages
  else if (video.defaultLanguage || video.additionalLanguages) {
    // Add default language
    if (video.defaultLanguage) {
      const defaultLang = availableLanguages.find(l => l.code === video.defaultLanguage);
      if (defaultLang) {
        videoLanguages.push(defaultLang);
      }
    }

    // Add additional languages
    if (video.additionalLanguages && video.additionalLanguages.length > 0) {
      video.additionalLanguages.forEach(lang => {
        const foundLang = availableLanguages.find(l => l.code === lang.languageCode);
        if (foundLang && !videoLanguages.some(l => l.code === foundLang.code)) {
          videoLanguages.push(foundLang);
        }
      });
    }
  }

  // If no languages were determined, use English as default
  if (videoLanguages.length === 0) {
    videoLanguages = [availableLanguages[0] || { code: 'en', name: 'English', flag: '🇺🇸' }];
  }

  // Create a new video object
  const newVideo: Video = {
    id: videoId,
    title: video.title,
    thumbnail: video.thumbnail || '/placeholder.svg',
    creator: currentUser || {
      id: 'local-user',
      username: 'You',
      avatar: '/placeholder.svg'
    },
    views: 0,
    likes: 0,
    createdAt: new Date().toISOString(),
    languages: videoLanguages,
    category: video.category,
    description: video.description,
    duration: '5:00', // Default 5 minutes
    url: video.url,
    savedLocally: true
  };

  // Get existing saved videos
  const savedVideosJson = localStorage.getItem('lawengaxe-saved-videos');
  const savedVideos = savedVideosJson ? JSON.parse(savedVideosJson) : [];

  // Add the new video
  savedVideos.push(newVideo);

  // Save back to localStorage
  localStorage.setItem('lawengaxe-saved-videos', JSON.stringify(savedVideos));

  return newVideo;
};

/**
 * Get all locally saved videos
 */
export const getLocalVideos = (): Video[] => {
  try {
    const savedVideosJson = localStorage.getItem('lawengaxe-saved-videos');
    if (!savedVideosJson) return [];

    const savedVideos = JSON.parse(savedVideosJson);
    if (!Array.isArray(savedVideos)) return [];

    return savedVideos;
  } catch (error) {
    console.error('Error loading local videos:', error);
    return [];
  }
};

/**
 * Delete a locally saved video
 */
export const deleteLocalVideo = (videoId: string): boolean => {
  try {
    const savedVideosJson = localStorage.getItem('lawengaxe-saved-videos');
    if (!savedVideosJson) return false;

    const savedVideos = JSON.parse(savedVideosJson);
    if (!Array.isArray(savedVideos)) return false;

    const filteredVideos = savedVideos.filter(video => video.id !== videoId);

    // If no videos were removed, return false
    if (filteredVideos.length === savedVideos.length) return false;

    // Save the filtered videos back to localStorage
    localStorage.setItem('lawengaxe-saved-videos', JSON.stringify(filteredVideos));

    return true;
  } catch (error) {
    console.error('Error deleting local video:', error);
    return false;
  }
};

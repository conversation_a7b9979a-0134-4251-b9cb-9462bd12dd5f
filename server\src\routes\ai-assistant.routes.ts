import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import { Type } from '@sinclair/typebox';
import { aiAssistantController } from '../controllers/ai-assistant.controller';

/**
 * AI Assistant routes
 */
export default async function aiAssistantRoutes(
  fastify: FastifyInstance,
  options: FastifyPluginOptions
): Promise<void> {
  // Send message to AI Assistant
  fastify.post('/message', {
    schema: {
      tags: ['AI Assistant'],
      description: 'Send a message to the AI Assistant',
      body: Type.Object({
        message: Type.String(),
        provider: Type.Enum({ openai: 'openai', deepseek: 'deepseek', bhashini: 'bhashini', custom: 'custom' }),
        apiKey: Type.String(),
        endpoint: Type.Optional(Type.String()),
        conversationId: Type.Optional(Type.String()),
        creatorId: Type.String(),
        creatorName: Type.Optional(Type.String()), // Added creator name for personalized responses
        videoId: Type.Optional(Type.String()),
        chatHistory: Type.Optional(Type.Array(Type.Object({
          role: Type.Enum({ user: 'user', assistant: 'assistant' }),
          content: Type.String()
        })))
      }),
      response: {
        200: Type.Object({
          success: Type.Boolean(),
          data: Type.Object({
            response: Type.String(),
            conversationId: Type.String(),
            timestamp: Type.String({ format: 'date-time' })
          })
        }),
        400: Type.Object({
          success: Type.Boolean(),
          message: Type.String()
        }),
        500: Type.Object({
          success: Type.Boolean(),
          message: Type.String()
        })
      }
    }
  }, aiAssistantController.sendMessage);
}

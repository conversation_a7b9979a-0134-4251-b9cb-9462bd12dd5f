import { useAuth } from '@/context/AuthContext';

export default function AuthDebug() {
  const { currentUser, isAdmin, isCreator } = useAuth();

  return (
    <div className="fixed bottom-0 right-0 bg-black bg-opacity-80 text-white p-4 text-xs z-50 max-w-xs overflow-auto">
      <h3 className="font-bold mb-2">Auth Debug</h3>
      <div>
        <p><strong>User:</strong> {currentUser ? currentUser.username : 'Not logged in'}</p>
        <p><strong>Email:</strong> {currentUser ? currentUser.email : 'N/A'}</p>
        <p><strong>isAdmin:</strong> {isAdmin ? 'Yes' : 'No'}</p>
        <p><strong>isCreator:</strong> {isCreator ? 'Yes' : 'No'}</p>
      </div>
    </div>
  );
}

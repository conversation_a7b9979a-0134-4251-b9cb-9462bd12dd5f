import { config } from 'dotenv';
import Fastify, { FastifyInstance } from 'fastify';
import cors from '@fastify/cors';
import rateLimit from '@fastify/rate-limit';
import cookie from '@fastify/cookie';
import fastifyStatic from '@fastify/static';
import path from 'path';
import typeboxPlugin from './plugins/typebox';
import authPlugin from './plugins/auth';
import errorHandlerPlugin from './plugins/errorHandler';
import swaggerPlugin from './plugins/swagger';
import websocketPlugin from './plugins/websocket';
import { notFoundHandler } from './middleware/notFound';
import { performanceMonitor } from './middleware/performance';
import { secureSwagger } from './middleware/secureSwagger';
import { registerResponseTransformer } from './middleware/response-transformer.middleware';

// Load environment variables
config();

// Set NODE_ENV to development if not set
if (!process.env.NODE_ENV) {
  process.env.NODE_ENV = 'development';
  console.log('NODE_ENV not set, defaulting to development mode');
} else {
  console.log(`Running in ${process.env.NODE_ENV} mode`);
}

// Import database connector
import { connectDB } from './config/database';

// Import routes
import routes from './routes';

const server: FastifyInstance = Fastify({
  logger: {
    level: process.env.LOG_LEVEL || 'info',
  },
});

// Register plugins
async function registerPlugins() {
  // TypeBox schema compiler
  await server.register(typeboxPlugin);

  // Error handler
  await server.register(errorHandlerPlugin);

  // Add performance monitoring middleware
  server.addHook('onRequest', performanceMonitor);

  // CORS
  await server.register(cors, {
    origin: true,
    credentials: true,
  });

  // Authentication plugin (includes JWT)
  await server.register(authPlugin);

  // Rate limiting
  await server.register(rateLimit, {
    max: 100,
    timeWindow: '1 minute',
  });

  // Cookie parser for authentication
  await server.register(cookie, {
    secret: process.env.COOKIE_SECRET || 'default_cookie_secret_change_this',
    parseOptions: {}
  });

  // Swagger documentation
  await server.register(swaggerPlugin);

  // Secure Swagger documentation
  server.addHook('preHandler', secureSwagger);

  // WebSocket support for real-time messaging
  await server.register(websocketPlugin);

  // Static file serving
  await server.register(fastifyStatic, {
    root: path.join(__dirname, '../src/public'),
    prefix: '/public/',
  });

  // Register response transformer middleware
  registerResponseTransformer(server);

  // Register all routes
  await server.register(routes);

  // Set up 404 handler for routes that don't exist
  server.setNotFoundHandler(notFoundHandler);
}

// Start the server
async function start() {
  try {
    // Register all plugins
    await registerPlugins();

    // Connect to MongoDB
    await connectDB();

    // CRITICAL FIX: Run the fixVideoUrls method to fix ALL videos in the database
    try {
      console.log('Running fixVideoUrls to fix ALL videos in the database...');

      // Import the VideoService directly
      const { VideoService } = require('./services/video.service');
      const videoService = new VideoService();

      // Call the fixVideoUrls method directly with forceUpdate=true to update ALL videos
      const result = await videoService.fixVideoUrls(true);
      console.log(`Fixed ${result.fixedCount} out of ${result.totalCount} videos`);

      // Log a message to indicate that the fixVideoUrls method has completed
      console.log('Video URL fixing process completed. All videos should now have valid 6-7 character Engaxe IDs.');

      // Verify that all videos have valid Engaxe IDs
      const verifyResult = await videoService.verifyAllVideosHaveValidEngaxeIds();
      console.log(`Verification result: ${verifyResult.validCount} out of ${verifyResult.totalCount} videos have valid Engaxe IDs`);

      if (verifyResult.invalidCount > 0) {
        console.error(`WARNING: ${verifyResult.invalidCount} videos still have invalid IDs!`);
        console.error('Invalid video IDs:', verifyResult.invalidIds.join(', '));
      } else {
        console.log('All videos have valid Engaxe IDs. The system is ready to use.');
      }

      // Ensure all videos have mappings
      try {
        console.log('Ensuring all videos have mappings...');
        const mappingResult = await videoService.ensureAllVideosMapped();
        console.log(`Mapping result: ${mappingResult.createdCount} mappings created, ${mappingResult.existingCount} already existed`);
      } catch (mappingError) {
        console.error('Error ensuring video mappings:', mappingError);
      }
    } catch (error) {
      console.error('Error fixing video URLs:', error);
    }

    // Health check route is defined in routes/index.ts

    // Start the server
    const port = process.env.PORT ? parseInt(process.env.PORT, 10) : 3002; // Changed to 3002 to avoid conflicts with existing process
    await server.listen({ port, host: '0.0.0.0' });

    console.log(`Server is running on port ${port}`);
  } catch (err) {
    server.log.error(err);
    process.exit(1);
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (err) => {
  server.log.error('Unhandled Rejection:', err);
  // Don't exit in production, just log the error
  if (process.env.NODE_ENV === 'development') {
    process.exit(1);
  }
});

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  server.log.error('Uncaught Exception:', err);
  // Don't exit in production, just log the error
  if (process.env.NODE_ENV === 'development') {
    process.exit(1);
  }
});

// Start the server
start();

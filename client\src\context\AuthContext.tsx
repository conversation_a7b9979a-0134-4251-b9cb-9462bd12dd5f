
import React, { createContext, useContext, useState, useEffect } from 'react';
import { User } from '@/types';
import { authAPI } from '@/services/api';

interface AuthContextType {
  currentUser: User | null;
  userAccounts: User[];
  isLoading: boolean;
  token: string | null;
  login: (email: string, password: string) => Promise<void>;
  loginWithEngaxe: (username: string, password: string) => Promise<void>;
  signup: (userData: {
    username: string;
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    displayName?: string;
  }) => Promise<void>;
  logout: () => void;
  switchAccount: (userId: string) => void;
  addAccount: (username: string, email: string) => Promise<void>;
  isCreator: boolean;
  setIsCreator: (value: boolean) => void;
  isAdmin: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [userAccounts, setUserAccounts] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isCreator, setIsCreator] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);
  const [token, setToken] = useState<string | null>(localStorage.getItem('lawengaxe-token'));

  useEffect(() => {
    const fetchCurrentUser = async () => {
      try {
        // Check if token exists
        const token = localStorage.getItem('lawengaxe-token');
        if (!token) {
          setIsLoading(false);
          return;
        }

        // Fetch current user from API
        const response = await authAPI.getCurrentUser();
        if (response.success && response.user) {
          const isUserAdmin = response.user.email === '<EMAIL>';
          console.log('Is user admin? (fetchCurrentUser)', { isUserAdmin, email: response.user.email });

          const user: User = {
            id: response.user.id,
            username: response.user.username,
            email: response.user.email,
            avatar: response.user.avatar || '/placeholder.svg',
            isAdmin: isUserAdmin // Set admin flag based on email
          };

          console.log('Setting current user (fetchCurrentUser):', user);
          setCurrentUser(user);
          console.log('Setting isAdmin (fetchCurrentUser):', user.isAdmin);
          setIsAdmin(user.isAdmin || false);

          // Check if user is a creator
          if (response.user.roles.includes('creator')) {
            setIsCreator(true);
          }

          // Load saved accounts
          const storedAccounts = localStorage.getItem('lawengaxe-accounts');
          if (storedAccounts) {
            setUserAccounts(JSON.parse(storedAccounts));
          } else {
            // If we have a current user but no accounts list, initialize with current user
            setUserAccounts([user]);
            localStorage.setItem('lawengaxe-accounts', JSON.stringify([user]));
          }
        }
      } catch (error) {
        console.error('Failed to fetch current user:', error);
        // Clear token if it's invalid
        localStorage.removeItem('lawengaxe-token');
        localStorage.removeItem('lawengaxe-refresh-token');
      } finally {
        setIsLoading(false);
      }
    };

    fetchCurrentUser();
  }, []);

  const login = async (email: string, password: string) => {
    try {
      // Call the real API login endpoint
      console.log('Attempting to login with:', { email });
      const response = await authAPI.login(email, password);
      console.log('Login response:', response);

      if (!response.success) {
        throw new Error(response.message || 'Login failed');
      }

      // Save tokens
      localStorage.setItem('lawengaxe-token', response.accessToken);
      localStorage.setItem('lawengaxe-refresh-token', response.refreshToken);
      setToken(response.accessToken);

      // Create user object from response
      const isUserAdmin = response.user.email === '<EMAIL>';
      console.log('Is user admin?', { isUserAdmin, email: response.user.email });

      const user: User = {
        id: response.user.id,
        username: response.user.username,
        email: response.user.email,
        avatar: response.user.avatar || '/placeholder.svg',
        isAdmin: isUserAdmin // Set admin flag based on email
      };

      console.log('Setting current user:', user);
      setCurrentUser(user);
      console.log('Setting isAdmin:', user.isAdmin);
      setIsAdmin(user.isAdmin || false);

      // Check if user is a creator
      if (response.user.roles.includes('creator')) {
        setIsCreator(true);
      }

      // Add to accounts if not already present
      const accountExists = userAccounts.some(account => account.id === user.id);
      if (!accountExists) {
        const updatedAccounts = [...userAccounts, user];
        setUserAccounts(updatedAccounts);
        localStorage.setItem('lawengaxe-accounts', JSON.stringify(updatedAccounts));
      }
    } catch (error: any) {
      console.error('Login failed:', error);

      // Handle network errors
      if (error.name === 'NetworkError') {
        throw new Error('Unable to connect to server. Please check your internet connection and try again.');
      }

      // Handle axios error
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        const errorData = error.response.data;
        if (errorData && errorData.error) {
          throw new Error(errorData.error.message || 'Login failed');
        }
      }

      // If we couldn't extract a specific error message, throw the original error
      throw error;
    }
  };

  const loginWithEngaxe = async (username: string, password: string) => {
    try {
      // Call the real Engaxe login endpoint
      console.log('Attempting to login with Engaxe:', { username });
      const response = await authAPI.loginWithEngaxe(username, password);
      console.log('Engaxe login response:', response);

      if (!response.success) {
        throw new Error(response.message || 'Engaxe login failed');
      }

      // Save tokens
      localStorage.setItem('lawengaxe-token', response.accessToken);
      localStorage.setItem('lawengaxe-refresh-token', response.refreshToken);
      setToken(response.accessToken);

      // Create user object from response
      const user: User = {
        id: response.user.id,
        username: response.user.username,
        email: response.user.email,
        avatar: response.user.avatar || '/placeholder.svg',
        isAdmin: false, // Engaxe users are never admins
        engaxeUserId: response.user.engaxeUserId
      };

      console.log('Setting current user (Engaxe):', user);
      setCurrentUser(user);

      // Set as creator since logged in with Engaxe
      setIsCreator(true);
      localStorage.setItem('lawengaxe-creator', 'true');

      // Ensure admin status is set to false
      setIsAdmin(false);

      // Add to accounts if not already present
      const accountExists = userAccounts.some(account => account.id === user.id);
      if (!accountExists) {
        const updatedAccounts = [...userAccounts, user];
        setUserAccounts(updatedAccounts);
        localStorage.setItem('lawengaxe-accounts', JSON.stringify(updatedAccounts));
      }
    } catch (error: any) {
      console.error('Engaxe login failed:', error);

      // Handle network errors
      if (error.name === 'NetworkError') {
        throw new Error('Unable to connect to server. Please check your internet connection and try again.');
      }

      // Handle axios error
      if (error.response) {
        const errorData = error.response.data;
        if (errorData && errorData.error) {
          throw new Error(errorData.error.message || 'Engaxe login failed');
        }
      }

      throw error;
    }
  };

  const signup = async (userData: {
    username: string;
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    displayName?: string;
  }) => {
    try {
      // Call the real API register endpoint
      const response = await authAPI.register(userData);

      if (!response.success) {
        throw new Error(response.message || 'Signup failed');
      }

      // After successful signup, log the user in
      await login(userData.email, userData.password);
    } catch (error: any) {
      console.error('Signup failed:', error);

      // Handle network errors
      if (error.name === 'NetworkError') {
        throw new Error('Unable to connect to server. Please check your internet connection and try again.');
      }

      // Handle axios error
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        const errorData = error.response.data;
        if (errorData && errorData.error) {
          throw new Error(errorData.error.message || 'Signup failed');
        }
      }

      // If we couldn't extract a specific error message, throw the original error
      throw error;
    }
  };

  const logout = () => {
    setCurrentUser(null);
    setIsCreator(false);
    setIsAdmin(false);
    setToken(null);
    localStorage.removeItem('lawengaxe-token');
    localStorage.removeItem('lawengaxe-refresh-token');
    // Note: We don't remove accounts from localStorage so they can be switched back to
  };

  const switchAccount = (userId: string) => {
    // This function would need to be updated to work with the real API
    // For now, we'll just show a console message
    console.log('Account switching is not implemented with the real API yet');
    // In a real implementation, you would need to store multiple tokens
    // and switch between them, or re-authenticate as the selected user
  };

  const addAccount = async (username: string, email: string) => {
    try {
      // This function would need to be updated to work with the real API
      // For now, we'll just show a console message
      console.log('Adding accounts is not implemented with the real API yet');
      throw new Error('Adding accounts is not implemented with the real API yet');
    } catch (error) {
      console.error('Add account failed:', error);
      throw error;
    }
  };

  return (
    <AuthContext.Provider value={{
      currentUser,
      userAccounts,
      isLoading,
      token,
      login,
      loginWithEngaxe,
      signup,
      logout,
      switchAccount,
      addAccount,
      isCreator,
      setIsCreator,
      isAdmin
    }}>
      {children}
    </AuthContext.Provider>
  );
};

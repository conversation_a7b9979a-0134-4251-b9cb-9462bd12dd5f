import React, { useState, useEffect } from 'react';
import { useParams, useSearchParams, Link } from 'react-router-dom';
import { channelAPI, videoAPI } from '@/services/api';
import ChannelHeader from '@/components/channels/ChannelHeader';
import VideoGrid from '@/components/videos/VideoGrid';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';
import { useAuth } from '@/context/AuthContext';
import Layout from '@/components/layout/Layout';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';

const ChannelPage: React.FC = () => {
  const { channelName } = useParams<{ channelName: string }>();
  const [searchParams, setSearchParams] = useSearchParams();
  const { currentUser } = useAuth();

  const [channel, setChannel] = useState<any>(null);
  const [videos, setVideos] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isVideosLoading, setIsVideosLoading] = useState(true);
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [activeTab, setActiveTab] = useState('videos');
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    limit: 20,
    pages: 1,
  });

  // Get query parameters
  const page = Number(searchParams.get('page') || '1');
  const sort = searchParams.get('sort') || 'newest';

  // Fetch channel data
  const fetchChannel = async () => {
    if (!channelName) return;

    try {
      setIsLoading(true);
      const response = await channelAPI.getChannelByName(channelName);

      if (response.success) {
        setChannel(response.channel);

        // TODO: Check if user is subscribed to this channel
        // For now, we'll just set it to false
        setIsSubscribed(false);
      } else {
        toast.error('Failed to load channel');
      }
    } catch (error) {
      console.error('Error fetching channel:', error);
      toast.error('Failed to load channel');
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch channel videos
  const fetchChannelVideos = async () => {
    if (!channel) return;

    try {
      setIsVideosLoading(true);

      const params = {
        page,
        sort,
        limit: 20,
      };

      const response = await videoAPI.getChannelVideos(channel.id, params);

      if (response.success) {
        setVideos(response.videos);
        setPagination(response.pagination);
      } else {
        toast.error('Failed to load videos');
      }
    } catch (error) {
      console.error('Error fetching videos:', error);
      toast.error('Failed to load videos');
    } finally {
      setIsVideosLoading(false);
    }
  };

  // Handle subscription toggle
  const handleSubscribe = async () => {
    if (!currentUser) {
      toast.error('Please sign in to subscribe to channels');
      return;
    }

    if (!channel) return;

    try {
      const response = await channelAPI.toggleSubscription(channel.id);

      if (response.success) {
        setIsSubscribed(response.isSubscribed);
        toast.success(response.message);
      } else {
        toast.error(response.message || 'Failed to update subscription');
      }
    } catch (error: any) {
      console.error('Error updating subscription:', error);
      toast.error(error.message || 'Failed to update subscription');
    }
  };

  // Handle share
  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: channel.displayName,
        text: channel.description,
        url: window.location.href,
      }).catch(error => {
        console.error('Error sharing:', error);
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
      toast.success('Channel URL copied to clipboard');
    }
  };

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value);

    // Reset pagination when changing tabs
    setSearchParams(prev => {
      const newParams = new URLSearchParams(prev);
      newParams.set('page', '1');
      return newParams;
    });
  };

  // Handle sort change
  const handleSortChange = (value: string) => {
    setSearchParams(prev => {
      const newParams = new URLSearchParams(prev);
      newParams.set('sort', value);
      newParams.set('page', '1');
      return newParams;
    });
  };

  // Handle pagination
  const handlePageChange = (newPage: number) => {
    if (newPage < 1 || newPage > pagination.pages) return;

    setSearchParams(prev => {
      const newParams = new URLSearchParams(prev);
      newParams.set('page', newPage.toString());
      return newParams;
    });
  };

  // Fetch channel data when channelName changes
  useEffect(() => {
    fetchChannel();
  }, [channelName]);

  // Fetch channel videos when channel, page, or sort changes
  useEffect(() => {
    if (channel && activeTab === 'videos') {
      fetchChannelVideos();
    }
  }, [channel, page, sort, activeTab]);

  if (isLoading) {
    return (
      <Layout>
        <div className="container py-8 space-y-6">
        {/* Skeleton for banner */}
        <Skeleton className="w-full h-48 rounded-lg" />

        {/* Skeleton for channel info */}
        <div className="flex items-start gap-4">
          <Skeleton className="h-20 w-20 rounded-full" />
          <div className="space-y-2 flex-1">
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-4 w-48" />
            <Skeleton className="h-4 w-full max-w-md" />
          </div>
          <Skeleton className="h-10 w-28" />
        </div>

        {/* Skeleton for tabs */}
        <Skeleton className="h-10 w-full max-w-md" />

        {/* Skeleton for videos */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mt-8">
          {Array.from({ length: 8 }).map((_, index) => (
            <div key={index} className="space-y-2">
              <Skeleton className="w-full aspect-video rounded-lg" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-2/3" />
            </div>
          ))}
        </div>
      </div>
      </Layout>
    );
  }

  if (!channel) {
    return (
      <Layout>
        <div className="container py-8">
        <div className="text-center py-16">
          <h2 className="text-2xl font-bold mb-2">Channel not found</h2>
          <p className="text-muted-foreground">
            The channel you're looking for doesn't exist or has been removed.
          </p>
        </div>
      </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container py-8">
        <div className="mb-4">
          <Link to="/">
            <Button variant="outline" size="sm" className="flex items-center gap-2">
              <ArrowLeft className="h-4 w-4" />
              Back to Home
            </Button>
          </Link>
        </div>
        <ChannelHeader
          channel={channel}
          activeTab={activeTab}
          onTabChange={handleTabChange}
          isSubscribed={isSubscribed}
          onSubscribe={handleSubscribe}
          onShare={handleShare}
        />

        <div className="mt-6">
          {activeTab === 'videos' && (
            <>
            <div className="flex justify-end mb-4">
              <select
                value={sort}
                onChange={(e) => handleSortChange(e.target.value)}
                className="px-3 py-1 border rounded-md text-sm"
              >
                <option value="newest">Newest</option>
                <option value="popular">Most Popular</option>
                <option value="oldest">Oldest</option>
              </select>
            </div>

            <VideoGrid
              videos={videos}
              isLoading={isVideosLoading}
              emptyMessage="This channel hasn't uploaded any videos yet."
              hideChannelInfo
            />

            {/* Pagination */}
            {pagination.pages > 1 && (
              <div className="flex justify-center mt-8">
                <div className="flex gap-2">
                  <button
                    className={`px-3 py-1 rounded-md ${
                      page <= 1
                        ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        : 'bg-gray-200 hover:bg-gray-300'
                    }`}
                    onClick={() => handlePageChange(page - 1)}
                    disabled={page <= 1}
                  >
                    Previous
                  </button>

                  <div className="flex items-center gap-1">
                    {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
                      let pageNum = page;
                      if (pagination.pages <= 5) {
                        pageNum = i + 1;
                      } else if (page <= 3) {
                        pageNum = i + 1;
                      } else if (page >= pagination.pages - 2) {
                        pageNum = pagination.pages - 4 + i;
                      } else {
                        pageNum = page - 2 + i;
                      }

                      return (
                        <button
                          key={pageNum}
                          className={`w-8 h-8 flex items-center justify-center rounded-md ${
                            pageNum === page
                              ? 'bg-primary text-primary-foreground'
                              : 'bg-gray-100 hover:bg-gray-200'
                          }`}
                          onClick={() => handlePageChange(pageNum)}
                        >
                          {pageNum}
                        </button>
                      );
                    })}
                  </div>

                  <button
                    className={`px-3 py-1 rounded-md ${
                      page >= pagination.pages
                        ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        : 'bg-gray-200 hover:bg-gray-300'
                    }`}
                    onClick={() => handlePageChange(page + 1)}
                    disabled={page >= pagination.pages}
                  >
                    Next
                  </button>
                </div>
              </div>
            )}
            </>
          )}

          {activeTab === 'playlists' && (
            <div className="text-center py-16">
              <h2 className="text-xl font-medium mb-2">Playlists</h2>
              <p className="text-muted-foreground">
                This feature is coming soon.
              </p>
            </div>
          )}

          {activeTab === 'about' && (
            <div className="max-w-3xl mx-auto">
              <div className="bg-card rounded-lg p-6 shadow-sm">
                <h2 className="text-xl font-medium mb-4">About {channel.displayName}</h2>

                <div className="space-y-6">
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-2">Description</h3>
                    <p className="whitespace-pre-line">{channel.description}</p>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-2">Stats</h3>
                    <ul className="space-y-1">
                      <li>Joined: {new Date(channel.createdAt).toLocaleDateString()}</li>
                      <li>{channel.stats.subscribers.toLocaleString()} subscribers</li>
                      <li>{channel.stats.videoCount.toLocaleString()} videos</li>
                      <li>{channel.stats.totalViews.toLocaleString()} total views</li>
                    </ul>
                  </div>

                  {channel.socialLinks && Object.values(channel.socialLinks).some(Boolean) && (
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-2">Links</h3>
                      <ul className="space-y-1">
                        {channel.socialLinks.website && (
                          <li>
                            <a href={channel.socialLinks.website} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                              Website
                            </a>
                          </li>
                        )}
                        {channel.socialLinks.youtube && (
                          <li>
                            <a href={channel.socialLinks.youtube} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                              YouTube
                            </a>
                          </li>
                        )}
                        {channel.socialLinks.twitter && (
                          <li>
                            <a href={channel.socialLinks.twitter} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                              Twitter
                            </a>
                          </li>
                        )}
                        {channel.socialLinks.instagram && (
                          <li>
                            <a href={channel.socialLinks.instagram} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                              Instagram
                            </a>
                          </li>
                        )}
                        {channel.socialLinks.facebook && (
                          <li>
                            <a href={channel.socialLinks.facebook} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                              Facebook
                            </a>
                          </li>
                        )}
                      </ul>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'community' && (
            <div className="text-center py-16">
              <h2 className="text-xl font-medium mb-2">Community</h2>
              <p className="text-muted-foreground">
                This feature is coming soon.
              </p>
            </div>
          )}
      </div>
    </div>
    </Layout>
  );
};

export default ChannelPage;

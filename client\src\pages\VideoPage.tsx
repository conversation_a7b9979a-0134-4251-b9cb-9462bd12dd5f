
import { use<PERSON><PERSON><PERSON>, Link, useLocation, useNavigate } from 'react-router-dom';
import { useEffect, useState, useRef } from 'react';
import { useVideos } from '@/context/VideoContext';
import { useChatbot } from '@/context/ChatbotContext';
import { useAuth } from '@/context/AuthContext';
import Layout from '@/components/layout/Layout';
import VideoPlayer from '@/components/video/VideoPlayer';
import VideoCard from '@/components/video/VideoCard';
import LeaderboardTable from '@/components/video/LeaderboardTable';
import Comments from '@/components/video/Comments';
import ChatbotBalloon from '@/components/chatbot/ChatbotBalloon';
// MessageBalloon replaced by unified CreatorChat
import CreatorChat from '@/components/chat/CreatorChat';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { formatDistanceToNow } from 'date-fns';
import { ThumbsUp, MessageCircle, Share2, Flag, AlertCircle, Upload, Globe, RefreshCw, Bug, Wrench, Phone } from 'lucide-react';
import { getLocalVideos } from '@/utils/localVideoStorage';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import reportAPI from '@/services/report-api';
import { videoAPI } from '@/services/api';
import { storeVideoId, getVideoId, extractCleanVideoId } from '@/utils/video-id-store';
import { hashIdToEngaxeId, isValidEngaxeId } from '@/utils/videoIdConverter';

export default function VideoPage() {
  const { videoId } = useParams<{ videoId: string }>();
  const { getVideoById, videos, refreshVideos } = useVideos();
  const navigate = useNavigate();
  const { toast } = useToast();
  const {
    isChatbotEnabled,
    isVoiceCallingEnabled,
    isChattingEnabled,
    isAIAssistantEnabled
  } = useChatbot();
  const [selectedLanguage, setSelectedLanguage] = useState<string>("");
  const [showChatbot, setShowChatbot] = useState(false);
  // Using a single state for chat visibility
  const [showChat, setShowChat] = useState(false);
  // State for showing voice call button
  const [showVoiceCallButton, setShowVoiceCallButton] = useState(true);
  const [showReportDialog, setShowReportDialog] = useState(false);
  const [reportTitle, setReportTitle] = useState('');
  const [reportDescription, setReportDescription] = useState('');
  const [reportType, setReportType] = useState('playback_issue');
  const [reportFiles, setReportFiles] = useState<File[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { user } = useAuth();
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Check if we have a video passed via location state (for locally saved videos)
  const passedVideo = location.state?.video;

  // CRITICAL FIX: Get the video URL from location state if available
  const passedVideoUrl = location.state?.videoUrl;
  console.log('Passed video URL from location state:', passedVideoUrl);

  // CRITICAL FIX: The videoId parameter is actually the URL (6-7 character Engaxe ID)
  // First try to find the video by URL
  let video = null;

  if (videoId) {
    console.log(`Looking for video with URL: ${videoId}`);
    // Try to find a video with this URL
    const matchingVideo = videos.find(v => v.url === videoId);
    if (matchingVideo) {
      console.log(`Found video by URL: ${videoId}`);
      video = matchingVideo;
    } else {
      // If not found by URL, try to find by ID (for backward compatibility)
      console.log(`No video found with URL: ${videoId}, trying by ID`);
      video = getVideoById(videoId);
      if (video) {
        console.log(`Found video by ID: ${videoId}`);
      }
    }
  }

  // If we still don't have a video, use the passed video
  if (!video) {
    console.log(`Using passed video from location state`);
    video = passedVideo;
  }

  // If we have a valid video and a passed video URL, use it
  if (video && passedVideoUrl) {
    console.log(`Using passed video URL: ${passedVideoUrl} instead of video.url: ${video.url}`);
    // Make a copy of the video object to avoid modifying the original
    video = { ...video, url: passedVideoUrl };
  }

  // If the video ID is a UUID (like 356a84d6-1ad6-404e-bd0b-7fe0775043c6), try to find it in local storage
  if (!video && videoId && videoId.includes('-')) {
    console.log('Trying to find video with UUID:', videoId);
    const localVideos = getLocalVideos();
    video = localVideos.find(v => v.id === videoId);
  }
  const relatedVideos = videos.filter(v => v.id !== videoId).slice(0, 4);


  // Add more detailed logging for the creator username
  if (video?.creator) {
    console.log('Creator username:', video.creator.username);
    console.log('Is creator username defined?', typeof video.creator.username !== 'undefined');
    console.log('Creator username type:', typeof video.creator.username);
  }


  useEffect(() => {
    if (video) {
      document.title = `${video.title} | LegalAid`;

      // Convert hash IDs to Engaxe IDs
      if (video.url) {
        console.log(`Using video URL directly from database: ${video.url}`);

        // If the video URL is not a valid 6-7 character Engaxe ID, convert it
        if (!isValidEngaxeId(video.url)) {
          console.log(`Video has an invalid URL: ${video.url}`);

          // Convert the hash ID to an Engaxe ID
          const engaxeId = hashIdToEngaxeId(video.url);
          console.log(`Converted hash ID to Engaxe ID: ${engaxeId}`);
          video.url = engaxeId;
        }
      }

      // Process language URLs to ensure they're all valid
      if (video.languages && video.languages.length > 0) {
        console.log(`Processing ${video.languages.length} languages for video ${video.id}`);

        // Log all languages from the database for debugging
        console.log('Languages from database:');
        video.languages.forEach((lang: any, idx: number) => {
          console.log(`DB Language ${idx + 1}: ${lang.name || 'Unknown'} (${lang.code || 'unknown'}), URL: ${lang.url || 'none'}, isDefault: ${lang.isDefault || false}, flag: ${lang.flag || 'none'}`);
        });

        // Process each language to ensure it has all required properties
        // IMPORTANT: Don't filter out languages, just ensure they have valid properties
        video.languages = video.languages.map((lang: any) => {
          // Create a new language object to avoid mutating the original
          const processedLang = { ...lang };

          // Ensure the language has a URL (use video URL as fallback)
          if (!processedLang.url || processedLang.url.trim() === '') {
            console.log(`Language ${processedLang.name || processedLang.code} has no URL, using video URL: ${video.url}`);
            processedLang.url = video.url || '';
          }

          // If URL is not a valid Engaxe ID, convert it
          if (!isValidEngaxeId(processedLang.url)) {
            console.log(`Language ${processedLang.name} has invalid URL: ${processedLang.url}`);
            processedLang.url = hashIdToEngaxeId(processedLang.url);
            console.log(`Converted language URL to: ${processedLang.url}`);
          }

          // Ensure the language has a flag
          if (!processedLang.flag) {
            // Add flag based on language code
            switch (processedLang.code) {
              case 'en': processedLang.flag = '🇺🇸'; break;
              case 'hi': processedLang.flag = '🇮🇳'; break;
              case 'es': processedLang.flag = '🇪🇸'; break;
              case 'fr': processedLang.flag = '🇫🇷'; break;
              case 'de': processedLang.flag = '🇩🇪'; break;
              case 'ja': processedLang.flag = '🇯🇵'; break;
              case 'zh': processedLang.flag = '🇨🇳'; break;
              case 'ru': processedLang.flag = '🇷🇺'; break;
              case 'ar': processedLang.flag = '🇸🇦'; break;
              case 'pt': processedLang.flag = '🇵🇹'; break;
              case 'it': processedLang.flag = '🇮🇹'; break;
              case 'nl': processedLang.flag = '🇳🇱'; break;
              case 'ko': processedLang.flag = '🇰🇷'; break;
              case 'tr': processedLang.flag = '🇹🇷'; break;
              default: processedLang.flag = '🌐'; break;
            }
            console.log(`Added flag ${processedLang.flag} for language ${processedLang.name} (${processedLang.code})`);
          }

          // Return the processed language with all required properties
          return {
            code: processedLang.code || 'en',
            name: processedLang.name || 'English',
            flag: processedLang.flag || '🌐',
            url: processedLang.url,
            isDefault: processedLang.isDefault !== undefined ? processedLang.isDefault : processedLang.code === 'en'
          };
        });

        // Check if we have at least one default language
        const hasDefaultLanguage = video.languages.some((lang: any) => lang.isDefault);

        // If no default language, set the first one as default
        if (!hasDefaultLanguage && video.languages.length > 0) {
          console.log(`No default language found, setting ${video.languages[0].name} as default`);
          video.languages[0].isDefault = true;
        }
      } else {
        // If no languages, create a default one with the video's URL
        video.languages = [{
          code: 'en',
          name: 'English',
          flag: '🇺🇸',
          isDefault: true,
          url: video.url || ''
        }];
        console.log(`No languages found for video ${video.id}, added default English language with URL: ${video.url || ''}`);
      }

      // Log the final languages
      console.log(`Final languages for video ${video.id}:`);
      video.languages.forEach((lang: any, idx: number) => {
        console.log(`Language ${idx + 1}/${video.languages.length}: ${lang.name} (${lang.code}), URL: ${lang.url}, isDefault: ${lang.isDefault}, flag: ${lang.flag}`);
      });

      // Set the initial selected language
      if (video.languages && video.languages.length > 0) {
        // Find the default language or use the first one
        const defaultLang = video.languages.find(lang => lang.isDefault) || video.languages[0];
        setSelectedLanguage(defaultLang.name);

        console.log('Initial language set to:', defaultLang.name);
        console.log('Available languages:', video.languages.map(l =>
          `${l.name}${l.isDefault ? ' (Default)' : ''}${l.url ? ` (URL: ${l.url})` : ''}`
        ).join(', '));

        // Log all language details for debugging
        video.languages.forEach(lang => {
          console.log(`Language: ${lang.name} (${lang.code})`);
          console.log(`  URL: ${lang.url || 'none'}`);
          console.log(`  Default: ${lang.isDefault ? 'Yes' : 'No'}`);
          console.log(`  Flag: ${lang.flag || 'none'}`);
        });
      }
    }
  }, [video]);

  // Mock leaderboard data - in a real app this would come from an API
  const leaderboardUsers = [
    { id: 'u1', username: 'LanguageMaster', avatar: '/placeholder.svg', score: 3570, rank: 1 },
    { id: 'u2', username: 'Polyglot2025', avatar: '/placeholder.svg', score: 3240, rank: 2 },
    { id: 'u3', username: 'WordWizard', avatar: '/placeholder.svg', score: 2980, rank: 3 },
    { id: 'u4', username: 'GrammarGuru', avatar: '/placeholder.svg', score: 2650, rank: 4 },
    { id: 'u5', username: 'VocabVoyager', avatar: '/placeholder.svg', score: 2230, rank: 5 },
  ];

  if (!video) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-[calc(100vh-120px)]">
          <p>Video not found</p>
        </div>
      </Layout>
    );
  }

  return (
    <Layout showSidebar={false}>
      {/* Chatbot Balloon - only show when enabled */}
      {isChatbotEnabled && <ChatbotBalloon isOpen={showChatbot} onClose={() => setShowChatbot(false)} />}

      {/* We're now using the unified CreatorChat component instead of MessageBalloon */}

      {/* Unified Creator Chat - shows for both AI and regular messaging */}
      {(isChattingEnabled || isAIAssistantEnabled) && video && (
        <CreatorChat
          isOpen={showChat}
          onClose={() => setShowChat(false)}
          creator={video.creator}
          video={video}
        />
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          <div className="space-y-2">
            <VideoPlayer
              video={video}
              onLanguageChange={(lang) => setSelectedLanguage(lang.name)}
            />
          </div>

          <div>
            <h1 className="text-xl font-bold">{video.title}</h1>
            <div className="flex justify-between mt-3">
              <Link to={`/channel/${video.creator.id}`} className="flex items-center gap-3">
                <Avatar className="h-10 w-10">
                  <AvatarImage src={video.creator.avatar} alt={video.creator.username} />
                  <AvatarFallback>{video.creator.username[0]}</AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-medium">{video.creator.username}</p>
                  <p className="text-xs text-lingstream-muted">
                    {video.views.toLocaleString()} views • {formatDistanceToNow(new Date(video.createdAt), { addSuffix: true })}
                  </p>
                </div>
              </Link>

              <div className="flex gap-2">
                <Button variant="outline" size="sm" className="flex items-center gap-1">
                  <ThumbsUp className="h-4 w-4" />
                  <span>{video.likes.toLocaleString()}</span>
                </Button>
                <Button variant="outline" size="sm" className="flex items-center gap-1">
                  <MessageCircle className="h-4 w-4" />
                  <span>Comments</span>
                </Button>
                <Button variant="outline" size="sm" className="flex items-center gap-1">
                  <Share2 className="h-4 w-4" />
                  <span>Share</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-1 text-red-500 hover:bg-red-50"
                  onClick={() => setShowReportDialog(true)}
                >
                  <Flag className="h-4 w-4" />
                  <span>Report</span>
                </Button>

                {/* Single Chat button that shows when any chat feature is enabled */}
                {(isChattingEnabled || isAIAssistantEnabled) && (
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-1 bg-orange-500 text-white hover:bg-orange-600"
                    onClick={() => setShowChat(true)}
                  >
                    <MessageCircle className="h-4 w-4" />
                    <span>Chat</span>
                  </Button>
                )}

                {/* Voice Call button that shows when voice calling is enabled */}
                {isVoiceCallingEnabled && showVoiceCallButton && (
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-1 bg-green-500 text-white hover:bg-green-600"
                    onClick={() => setShowChatbot(true)}
                  >
                    <Phone className="h-4 w-4" />
                    <span>Voice Call</span>
                  </Button>
                )}
              </div>
            </div>

            <div className="mt-6 bg-lingstream-card p-4 rounded-lg">
              <p className="text-sm">{video.description || 'No description available'}</p>
            </div>
          </div>

          {/* Leaderboard */}
          <LeaderboardTable users={leaderboardUsers} language={selectedLanguage} />

          {/* Comments Section */}
          {/* CRITICAL FIX: Use the video URL instead of the ID for comments */}
          <Comments videoId={video.url || video.id} />
        </div>

        <div className="space-y-6">
          <h3 className="font-medium">Related Videos</h3>
          <div className="space-y-4">
            {relatedVideos.map((relatedVideo) => (
              <VideoCard key={relatedVideo.id} video={relatedVideo} />
            ))}
          </div>
        </div>
      </div>

      {/* Report Video Dialog */}
      <Dialog open={showReportDialog} onOpenChange={setShowReportDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Report Video Issue</DialogTitle>
            <DialogDescription>
              Please provide details about the issue you're experiencing with this video.
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="report-type" className="text-right">
                Issue Type
              </Label>
              <Select
                value={reportType}
                onValueChange={setReportType}
                disabled={isSubmitting}
              >
                <SelectTrigger id="report-type" className="col-span-3">
                  <SelectValue placeholder="Select issue type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="playback_issue">Playback Issue</SelectItem>
                  <SelectItem value="content_error">Content Error</SelectItem>
                  <SelectItem value="inappropriate_content">Inappropriate Content</SelectItem>
                  <SelectItem value="copyright_violation">Copyright Violation</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="report-title" className="text-right">
                Issue Title
              </Label>
              <Input
                id="report-title"
                placeholder="Brief title describing the issue"
                className="col-span-3"
                value={reportTitle}
                onChange={(e) => setReportTitle(e.target.value)}
                disabled={isSubmitting}
              />
            </div>

            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="report-description" className="text-right pt-2">
                Description
              </Label>
              <Textarea
                id="report-description"
                placeholder="Please provide more details about the issue"
                className="col-span-3"
                rows={4}
                value={reportDescription}
                onChange={(e) => setReportDescription(e.target.value)}
                disabled={isSubmitting}
              />
            </div>

            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="report-files" className="text-right pt-2">
                Screenshots
              </Label>
              <div className="col-span-3">
                <div className="flex items-center gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => fileInputRef.current?.click()}
                    disabled={isSubmitting}
                    className="gap-2"
                  >
                    <Upload className="h-4 w-4" />
                    Upload Screenshot
                  </Button>
                  <input
                    type="file"
                    id="report-files"
                    ref={fileInputRef}
                    className="hidden"
                    accept="image/*"
                    onChange={(e) => {
                      if (e.target.files && e.target.files[0]) {
                        setReportFiles([...reportFiles, e.target.files[0]]);
                      }
                    }}
                    disabled={isSubmitting}
                  />
                </div>
                {reportFiles.length > 0 && (
                  <div className="mt-2">
                    <p className="text-sm font-medium">Uploaded files:</p>
                    <ul className="text-sm">
                      {reportFiles.map((file, index) => (
                        <li key={index} className="flex items-center gap-2">
                          <span>{file.name}</span>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="h-6 p-0 text-red-500 hover:text-red-700"
                            onClick={() => setReportFiles(reportFiles.filter((_, i) => i !== index))}
                            disabled={isSubmitting}
                          >
                            Remove
                          </Button>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowReportDialog(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmitReport}
              disabled={isSubmitting || !reportTitle.trim()}
              className="gap-2"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-current border-t-transparent" />
                  Submitting...
                </>
              ) : (
                <>Submit Report</>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Layout>
  );

  // Function to handle report submission
  async function handleSubmitReport() {
    if (!reportTitle.trim()) {
      toast({
        title: "Error",
        description: "Please provide a title for your report",
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Convert files to base64 strings for API submission
      const filePromises = reportFiles.map(file => {
        return new Promise<string>((resolve) => {
          const reader = new FileReader();
          reader.onloadend = () => {
            resolve(reader.result as string);
          };
          reader.readAsDataURL(file);
        });
      });

      const fileBase64 = await Promise.all(filePromises);

      const response = await reportAPI.createReport({
        reportType: 'video',
        // CRITICAL FIX: Use the video URL instead of the ID for reports
        targetId: video.url || video.id,
        category: reportType,
        reason: reportTitle,
        description: reportDescription,
        evidence: fileBase64
      });

      toast({
        title: "Report Submitted",
        description: "Thank you for your report. Our team will review it shortly."
      });

      // Reset form and close dialog
      setReportTitle('');
      setReportDescription('');
      setReportType('playback_issue');
      setReportFiles([]);
      setShowReportDialog(false);
    } catch (error) {
      console.error('Error submitting report:', error);
      toast({
        title: "Error",
        description: "There was a problem submitting your report. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  }
}


import { useState } from 'react';
import { Link } from 'react-router-dom';
import { Video } from '@/types';
import { formatDistanceToNow } from 'date-fns';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useWatchlist, WatchlistVideo } from '@/context/WatchlistContext';
import { useLanguage } from '@/context/LanguageContext';
import { useToast } from '@/hooks/use-toast';
import { Clock, Check, Globe } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface VideoCardProps {
  video: Video;
}

export default function VideoCard({ video }: VideoCardProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [hasError, setHasError] = useState(false);
  const { addToWatchlist, isInWatchlist, removeFromWatchlist } = useWatchlist();
  const { t } = useLanguage();
  const { toast } = useToast();

  // Validate the video object to prevent rendering errors
  if (!video || !video.id || !video.title || !video.creator || !video.languages) {
    console.error('Invalid video object:', video);
    return (
      <div className="bg-gray-800 rounded-lg p-4 h-full">
        <div className="text-red-500">Error: Invalid video data</div>
      </div>
    );
  }

  const handleWatchlistToggle = () => {
    const watchlistVideo: WatchlistVideo = {
      id: video.id,
      title: video.title,
      thumbnail: video.thumbnail,
      creator: {
        name: video.creator.username,
        avatar: video.creator.avatar
      },
      views: video.views,
      duration: video.duration || '0:00',
      addedAt: new Date().toISOString()
    };

    if (isInWatchlist(video.id)) {
      removeFromWatchlist(video.id);
      toast({
        title: "Removed from watchlist",
        description: "Video has been removed from your watchlist"
      });
    } else {
      addToWatchlist(watchlistVideo);
      toast({
        title: "Added to watchlist",
        description: "Video has been added to your watchlist"
      });
    }
  };

  // No need for a separate YouTube handler - we'll use the VideoPlayer component

  // Render the video thumbnail section
  const renderThumbnail = () => (
    <div className="relative aspect-video overflow-hidden bg-gray-800">
      <img
        src={video.thumbnail || '/placeholder.svg'}
        alt={video.title}
        className="h-full w-full object-cover transition-transform duration-300 group-hover:scale-105"
        onError={(e) => {
          console.warn(`Failed to load thumbnail for video ${video.id}`, e);
          e.currentTarget.src = '/placeholder.svg';
        }}
      />
      {video.isLive && (
        <div className="absolute top-2 left-2 bg-lingstream-live rounded-full px-2 py-0.5 text-xs font-medium text-white flex items-center gap-1">
          <span className="h-2 w-2 rounded-full bg-white animate-pulse-dot"></span>
          LIVE
        </div>
      )}
      {video.savedLocally && (
        <div className={`absolute ${video.isLive ? 'top-8' : 'top-2'} left-2 bg-yellow-500 rounded-full px-2 py-0.5 text-xs font-medium text-white flex items-center gap-1`}>
          LOCAL
        </div>
      )}
      <div className="absolute bottom-2 right-2 bg-black bg-opacity-80 rounded-full px-2 py-1 text-xs flex items-center gap-1">
        <Globe className="h-3 w-3" />
        <span>
          {Array.isArray(video.languages) ? video.languages.length : 0} {Array.isArray(video.languages) && video.languages.length === 1 ? 'language' : 'languages'}
        </span>
      </div>

      {/* Watchlist button */}
      <Button
        variant="ghost"
        size="icon"
        className={`absolute top-2 right-2 h-8 w-8 rounded-full bg-black/50 ${isHovered ? 'opacity-100' : 'opacity-0'} transition-opacity`}
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          handleWatchlistToggle();
        }}
      >
        {isInWatchlist(video.id) ? (
          <Check className="h-4 w-4 text-green-500" />
        ) : (
          <Clock className="h-4 w-4" />
        )}
      </Button>
    </div>
  );

  // Render the video title
  const renderTitle = () => {
    // Safely get creator info with fallbacks
    const creatorName = video.creator?.username || 'Unknown';
    const creatorAvatar = video.creator?.avatar || '/placeholder.svg';
    const creatorId = video.creator?.id || 'unknown';

    // Safely format date
    let formattedDate = 'Unknown date';
    try {
      if (video.createdAt) {
        formattedDate = formatDistanceToNow(new Date(video.createdAt), { addSuffix: true });
      }
    } catch (error) {
      console.warn(`Failed to format date for video ${video.id}:`, error);
    }

    return (
      <div className="mt-2">
        <div className="flex gap-2">
          <Avatar className="h-8 w-8 rounded-full">
            <AvatarImage src={creatorAvatar} />
            <AvatarFallback>{creatorName[0] || '?'}</AvatarFallback>
          </Avatar>
          <div>
            <Link
              to={`/video/${video.url}`}
              className="font-medium line-clamp-2 text-sm leading-tight hover:text-lingstream-accent"
              state={{
                video: video.savedLocally ? video : undefined
              }}
            >
              {video.title}
            </Link>
            <div className="mt-1 text-xs text-lingstream-muted">
              <Link to={`/channel/${creatorId}`} className="hover:text-lingstream-accent">
                {creatorName}
              </Link>
              <div className="flex items-center gap-1">
                <span>{(video.views || 0).toLocaleString()} views</span>
                <span>•</span>
                <span>{formattedDate}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Use try-catch to handle any rendering errors
  try {
    return (
      <div className="group" onMouseEnter={() => setIsHovered(true)} onMouseLeave={() => setIsHovered(false)}>
        <Link
          to={`/video/${video.url}`}
          className="block overflow-hidden rounded-lg"
          state={{
            video: video.savedLocally ? video : undefined
          }}
        >
          {renderThumbnail()}
        </Link>
        {renderTitle()}
      </div>
    );
  } catch (error) {
    console.error(`Error rendering VideoCard for video ${video?.id || 'unknown'}:`, error);
    return (
      <div className="bg-gray-800 rounded-lg p-4">
        <div className="text-red-500">Error rendering video</div>
      </div>
    );
  }
}

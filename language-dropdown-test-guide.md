# Testing Language Dropdown Functionality

This document outlines the steps to test the updated language dropdown functionality.

## Test Plan

1. Verify language count display on video cards
2. Test adding videos with multiple languages
3. Verify language dropdown in video player
4. Test language switching during playback

## Step 1: Verify Language Count Display on Video Cards

1. Navigate to the Home page
2. Check that each video card shows the correct number of languages
3. Verify that the language count is displayed as "1 language" for videos with one language and "X languages" for videos with multiple languages

## Step 2: Test Adding Videos with Multiple Languages

1. Navigate to the Creator Studio
2. Click on "Add Video"
3. Enter the video details:
   - Title: "Test Video with Multiple Languages"
   - Description: "This is a test video with multiple languages"
   - Channel: Select any channel
   - Category: Select any category
   - Video ID: Use a valid Engaxe ID (e.g., "XLcMq2")
4. Add multiple languages:
   - Click "Add Language"
   - For the first language:
     - Language: English
     - URL: Use a valid Engaxe ID (e.g., "XLcMq2")
     - Set as default: Yes
   - Click "Add Language" again
   - For the second language:
     - Language: Hindi
     - URL: Use a valid Engaxe ID (e.g., "suZKhW")
     - Set as default: No
5. Click "Save Video"
6. Navigate to the Home page
7. Find the newly added video and verify that it shows "2 languages" on the video card

## Step 3: Verify Language Dropdown in Video Player

1. Click on the newly added video to open the video player
2. Check the browser console for language-related logs:
   - Look for "Available languages:" followed by a list of languages
   - Verify that all languages are listed with their names, codes, URLs, and default status
3. Click on the language dropdown button
4. Verify that the dropdown shows all languages for the video
5. Verify that the dropdown header shows "Select Language (X available)" where X is the number of languages
6. Verify that each language in the dropdown has:
   - A flag icon
   - The language name
   - A "Default" label for the default language
   - A dot indicator for the currently selected language

## Step 4: Test Language Switching During Playback

1. Note which language is currently selected (should be the default language)
2. Click on a different language in the dropdown
3. Verify in the console that:
   - The URL is properly extracted: `Extracted Engaxe ID from language URL: [ID] (original: [URL])`
   - The iframe source is updated: `Updating iframe source to: https://engaxe.com/e/[ID]`
4. Verify that the video source changes to the selected language
5. Verify that the language dropdown button now shows the selected language
6. Click on the language dropdown button again
7. Verify that the selected language has a dot indicator in the dropdown
8. Select another language and verify the same behavior

## Expected Results

- The language count should be correctly displayed on video cards
- Videos with multiple languages should show the correct count
- The language dropdown should show all available languages for the video
- The dropdown header should show the correct count of available languages
- Each language in the dropdown should have a flag icon and name
- The default language should be marked with "(Default)"
- The currently selected language should have a dot indicator
- When switching languages, the video source should change to the corresponding Engaxe URL


import { useLocation } from 'react-router-dom';
import { useVideos } from '@/context/VideoContext';
import Layout from '@/components/layout/Layout';
import VideoCard from '@/components/video/VideoCard';
import { useState, useEffect } from 'react';
import { Loader2 } from 'lucide-react';
import ErrorBoundary from '@/components/ErrorBoundary';

export default function HomePage() {
  const location = useLocation();
  const { trendingVideos, getVideosByCategory, refreshVideos, isLoading } = useVideos();
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  // Refresh videos when component mounts
  useEffect(() => {
    console.log('HomePage mounted - refreshing videos');
    refreshVideos();

    // Set up an interval to refresh videos periodically
    const refreshInterval = setInterval(() => {
      console.log('Periodic refresh of videos');
      refreshVideos();
    }, 30000); // Refresh every 30 seconds

    return () => {
      clearInterval(refreshInterval);
    };
  }, []);

  // Parse category from URL query params
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const category = params.get('category');

    if (category) {
      // Convert first letter to uppercase
      const formattedCategory = category.charAt(0).toUpperCase() + category.slice(1);
      setSelectedCategory(formattedCategory);
    } else {
      setSelectedCategory(null);
    }
  }, [location.search]);

  // Filter videos by selected category
  const displayVideos = selectedCategory
    ? getVideosByCategory(selectedCategory)
    : trendingVideos;

  const pageTitle = selectedCategory
    ? `${selectedCategory} Videos`
    : "Trending Videos";

  return (
    <Layout>
      <div className="space-y-8">
        {/* Only show featured section when no category is selected */}
        {!selectedCategory && (
          <section className="relative">
            <div className="aspect-[21/9] overflow-hidden rounded-xl relative">
              <img
                src="/placeholder.svg"
                alt="Featured video"
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-lingstream-background to-transparent" />
              <div className="absolute bottom-0 left-0 p-6 w-full">
                <div className="inline-block px-2 py-1 mb-2 bg-lingstream-live text-white text-xs font-medium rounded-full">
                  LIVE
                </div>
                <h1 className="text-2xl md:text-4xl font-bold mb-2">International Tech Conference 2025</h1>
                <p className="text-sm md:text-base text-lingstream-muted max-w-2xl">
                  Join tech leaders from around the world discussing the future of AI and machine learning in 12 different languages.
                </p>
                <div className="flex gap-3 mt-4">
                  <button className="px-4 py-2 bg-lingstream-accent hover:bg-opacity-90 text-white rounded-md font-medium transition-colors">
                    Watch Now
                  </button>
                  <button className="px-4 py-2 bg-gray-800 hover:bg-gray-700 text-white rounded-md font-medium transition-colors">
                    Add to Watchlist
                  </button>
                </div>
              </div>
            </div>
          </section>
        )}

        {/* Videos Section */}
        <section>
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-bold">{pageTitle}</h2>
            <div className="flex gap-2">
              <button
                onClick={() => {
                  console.log('Manual refresh triggered');
                  refreshVideos();
                }}
                className="px-3 py-1 bg-lingstream-accent hover:bg-opacity-90 text-white rounded-md text-sm font-medium transition-colors flex items-center"
                disabled={isLoading}
              >
                <Loader2 className={`h-4 w-4 mr-1 ${isLoading ? 'animate-spin' : ''}`} />
                Refresh
              </button>
              <button
                onClick={() => {
                  console.log('Force reload triggered');
                  window.location.reload();
                }}
                className="px-3 py-1 bg-gray-700 hover:bg-gray-600 text-white rounded-md text-sm font-medium transition-colors"
              >
                Force Reload
              </button>
            </div>
          </div>
          {isLoading ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-lingstream-accent" />
              <span className="ml-2">Loading videos...</span>
            </div>
          ) : (
            <>
              <div className="mb-4 text-sm text-gray-500">
                Found {displayVideos.length} videos to display
              </div>
              <ErrorBoundary
                fallback={
                  <div className="col-span-full p-4 bg-red-100 border border-red-400 text-red-700 rounded">
                    <h2 className="text-lg font-bold mb-2">Error loading videos</h2>
                    <p>There was a problem displaying the videos. Please try refreshing the page.</p>
                    <button
                      className="mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
                      onClick={() => window.location.reload()}
                    >
                      Reload page
                    </button>
                  </div>
                }
              >
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                  {displayVideos.map((video) => {
                    // Skip invalid videos
                    if (!video || !video.id) {
                      console.warn('Invalid video object in displayVideos:', video);
                      return null;
                    }

                    // Wrap each video card in its own error boundary
                    return (
                      <ErrorBoundary key={video.id} fallback={
                        <div className="bg-gray-800 rounded-lg p-4">
                          <div className="text-red-500">Error rendering video</div>
                        </div>
                      }>
                        <VideoCard video={video} />
                      </ErrorBoundary>
                    );
                  })}
                  {displayVideos.length === 0 && (
                    <p className="col-span-full text-center py-8 text-lingstream-muted">
                      No videos found in this category. Try refreshing the page.
                    </p>
                  )}
                </div>
              </ErrorBoundary>
            </>
          )}
        </section>
      </div>
    </Layout>
  );
}

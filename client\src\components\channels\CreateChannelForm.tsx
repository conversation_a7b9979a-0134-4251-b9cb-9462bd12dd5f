import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';
import { useNavigate } from 'react-router-dom';
import { channelAPI } from '@/services/api';

// Form validation schema
const formSchema = z.object({
  name: z.string()
    .min(3, 'Channel name must be at least 3 characters')
    .max(30, 'Channel name must be at most 30 characters')
    .regex(/^[a-zA-Z0-9_.-]+$/, 'Channel name can only contain letters, numbers, underscores, dots, and hyphens'),
  displayName: z.string()
    .min(1, 'Display name is required')
    .max(50, 'Display name must be at most 50 characters'),
  description: z.string()
    .min(1, 'Description is required')
    .max(5000, 'Description must be at most 5000 characters'),
  avatar: z.string().optional(),
  banner: z.string().optional(),
  category: z.string().optional(),
  tags: z.string().optional(),
});

type FormValues = z.infer<typeof formSchema>;

const CreateChannelForm: React.FC = () => {
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  // Initialize form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      displayName: '',
      description: '',
      avatar: '',
      banner: '',
      category: '',
      tags: '',
    },
  });

  // Form submission handler
  const onSubmit = async (values: FormValues) => {
    try {
      setIsSubmitting(true);
      
      // Convert tags string to array
      const tagsArray = values.tags 
        ? values.tags.split(',').map(tag => tag.trim()).filter(Boolean)
        : [];
      
      // Create channel
      const response = await channelAPI.createChannel({
        ...values,
        tags: tagsArray,
      });
      
      if (response.success) {
        toast.success('Channel created successfully');
        navigate(`/channel/${response.channel.name}`);
      } else {
        toast.error(response.message || 'Failed to create channel');
      }
    } catch (error: any) {
      console.error('Error creating channel:', error);
      toast.error(error.message || 'Failed to create channel');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Channel Name</FormLabel>
              <FormControl>
                <Input placeholder="my-channel-name" {...field} />
              </FormControl>
              <FormDescription>
                This will be used in your channel URL: lawengaxe.com/channel/{field.value || 'my-channel-name'}
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="displayName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Display Name</FormLabel>
              <FormControl>
                <Input placeholder="My Channel" {...field} />
              </FormControl>
              <FormDescription>
                This is how your channel name will appear to viewers
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea 
                  placeholder="Tell viewers about your channel..." 
                  className="min-h-[120px]"
                  {...field} 
                />
              </FormControl>
              <FormDescription>
                Describe what your channel is about
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="category"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Category</FormLabel>
              <FormControl>
                <Input placeholder="Education, Entertainment, etc." {...field} />
              </FormControl>
              <FormDescription>
                Choose a category that best describes your channel
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="tags"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Tags</FormLabel>
              <FormControl>
                <Input placeholder="law, education, legal" {...field} />
              </FormControl>
              <FormDescription>
                Add tags separated by commas to help viewers find your channel
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="avatar"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Avatar URL</FormLabel>
              <FormControl>
                <Input placeholder="https://example.com/avatar.jpg" {...field} />
              </FormControl>
              <FormDescription>
                URL to your channel avatar image (optional)
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="banner"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Banner URL</FormLabel>
              <FormControl>
                <Input placeholder="https://example.com/banner.jpg" {...field} />
              </FormControl>
              <FormDescription>
                URL to your channel banner image (optional)
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <Button type="submit" className="w-full" disabled={isSubmitting}>
          {isSubmitting ? 'Creating Channel...' : 'Create Channel'}
        </Button>
      </form>
    </Form>
  );
};

export default CreateChannelForm;

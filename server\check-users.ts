import { UserModel } from './src/models';
import { connectDB } from './src/config/database';

async function checkUsers() {
  try {
    await connectDB();
    const users = await UserModel.find({});
    console.log(`Found ${users.length} users:`);
    users.forEach(user => {
      console.log(` - ${user.username} (${user.email})`);
    });
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

checkUsers();

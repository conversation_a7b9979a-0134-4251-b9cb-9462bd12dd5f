import { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useVideos } from '@/context/VideoContext';
import { useLanguage } from '@/context/LanguageContext';
import { useToast } from '@/hooks/use-toast';
import Navbar from '@/components/layout/Navbar';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Plus, Link as LinkIcon, Languages, Trash2, Edit, Loader2 } from 'lucide-react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import VideoLinkForm from '@/components/creator/VideoLinkForm';
import AddVideoWizard from '@/components/creator/AddVideoWizard';
import { Video, Language } from '@/types';
import DashboardStats from '@/components/creator/DashboardStats';
import CreatorNavigation from '@/components/creator/CreatorNavigation';
import api, { videoAPI, channelAPI } from '@/services/api';
import { saveVideoLocally } from '@/utils/localVideoStorage';

export default function CreatorStudioPage() {
  const { currentUser } = useAuth();
  console.log('Current user:', currentUser);

  // Ensure we have a valid token for testing
  useEffect(() => {
    // This is a temporary solution for testing
    // In production, this would be handled by the login process
    const token = localStorage.getItem('lawengaxe-token');
    if (!token) {
      console.log('No token found, setting a test token');
      // This is a sample token - in production this would come from the login process
      localStorage.setItem('lawengaxe-token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************.Kv_B7h1qYGroD7rbxf7Ua058hehyA5qIO7dsa9YkYIY');
    }
  }, []);
  const { toast } = useToast();
  const { languages, videos, refreshVideos, isLoading } = useVideos();
  const { t } = useLanguage();
  const location = useLocation();
  const navigate = useNavigate();

  // Get active tab from URL or default to dashboard
  const [activeTab, setActiveTab] = useState('dashboard');

  // State for channel ID and channels
  const [channelId, setChannelId] = useState<string>('');
  const [channels, setChannels] = useState<any[]>([]);
  const [isLoadingChannels, setIsLoadingChannels] = useState(false);

  // Fetch user's channels
  useEffect(() => {
    const fetchChannels = async () => {
      if (!currentUser) {
        console.log('No current user, skipping channel fetch');
        return;
      }

      console.log('Current user:', currentUser);

      try {
        setIsLoadingChannels(true);
        console.log('Fetching channels for current user...');

        // Try to fetch channels from the database
        let channelsFound = false;
        try {
          const response = await channelAPI.getUserChannels();
          console.log('User channels response:', response);

          if (response.success && response.channels && response.channels.length > 0) {
            console.log(`Found ${response.channels.length} channels for user`);
            setChannels(response.channels);
            // Use the first channel as default
            setChannelId(response.channels[0].id);
            console.log(`Set default channel ID to: ${response.channels[0].id}`);
            channelsFound = true;
          } else {
            console.log('No channels found in the response');
          }
        } catch (fetchError) {
          console.error('Error in initial channel fetch:', fetchError);
        }

        // If no channels found, try to fetch by user ID directly
        if (!channelsFound) {
          try {
            console.log(`Trying to fetch channels by user ID: ${currentUser.id}`);
            const directResponse = await api.get(`/channels/user/${currentUser.id}`);
            console.log('Direct user channels response:', directResponse.data);

            if (directResponse.data.success && directResponse.data.channels && directResponse.data.channels.length > 0) {
              console.log(`Found ${directResponse.data.channels.length} channels for user via direct fetch`);
              setChannels(directResponse.data.channels);
              // Use the first channel as default
              setChannelId(directResponse.data.channels[0].id);
              console.log(`Set default channel ID to: ${directResponse.data.channels[0].id}`);
              channelsFound = true;
            }
          } catch (directFetchError) {
            console.error('Error in direct channel fetch:', directFetchError);
          }
        }

        // If still no channels found, create a default one
        if (!channelsFound) {
          console.log('No channels found, creating a default channel');
          try {
            // Prepare channel data with all required fields
            // Add a timestamp to ensure uniqueness
            const channelData = {
              name: `channel-${currentUser.id.substring(0, 8)}-${Date.now().toString().substring(9, 13)}`,
              displayName: 'My Default Channel',
              description: 'My default channel created automatically',
              visibility: 'public',
              tags: []
            };

            console.log('Creating default channel with data:', channelData);
            const createResponse = await channelAPI.createChannel(channelData);

            console.log('Create channel response:', createResponse);

            if (createResponse.success && createResponse.channel) {
              console.log('Default channel created:', createResponse.channel);
              setChannels([createResponse.channel]);
              setChannelId(createResponse.channel.id);
              console.log(`Set channel ID to newly created channel: ${createResponse.channel.id}`);

              toast({
                title: "Channel Created",
                description: "A default channel has been created for you.",
                variant: "default"
              });

              // Wait a moment to ensure the channel is fully created in the database
              await new Promise(resolve => setTimeout(resolve, 1000));

              // Verify the channel exists by fetching it directly
              try {
                const verifyResponse = await api.get(`/channels/${createResponse.channel.id}`);
                console.log('Channel verification response:', verifyResponse.data);
                if (!verifyResponse.data.success) {
                  console.warn('Channel verification returned unsuccessful response');
                }
              } catch (verifyError) {
                console.warn('Error verifying channel creation:', verifyError);
                // Continue anyway, as the channel might still be usable
              }
            } else {
              console.error('Failed to create default channel:', createResponse);
              toast({
                title: "Channel Creation Failed",
                description: "Could not create a default channel. Please try again later.",
                variant: "destructive"
              });
            }
          } catch (createError) {
            console.error('Error creating default channel:', createError);
            toast({
              title: "Channel Creation Failed",
              description: `Could not create a default channel: ${createError.message}`,
              variant: "destructive"
            });
          }
        }
      } catch (error) {
        console.error('Error in channel fetch process:', error);

        // Show detailed error message
        let errorMessage = "Could not fetch your channels.";
        if (error.response) {
          console.error('Response status:', error.response.status);
          console.error('Response data:', error.response.data);
          errorMessage += ` Server responded with: ${error.response.data?.message || error.response.statusText}`;
        } else if (error.message) {
          errorMessage += ` Error: ${error.message}`;
        }

        toast({
          title: "Channel Error",
          description: errorMessage,
          variant: "destructive"
        });

        // Create a temporary channel ID
        const tempChannelId = `temp-${Date.now()}`;
        console.log(`Using temporary channel ID: ${tempChannelId}`);
        setChannelId(tempChannelId);
      } finally {
        setIsLoadingChannels(false);
      }
    };

    fetchChannels();
  }, [currentUser, toast]);

  // Update active tab and channel ID based on URL query parameters
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const tab = params.get('tab');
    const channelIdParam = params.get('channelId');

    if (tab) {
      if (tab === 'home') {
        // Redirect to home page if home tab is selected
        navigate('/');
      } else if (['dashboard', 'videos', 'comments', 'series'].includes(tab)) {
        setActiveTab(tab);
      }
    }

    // Update channel ID if provided in URL
    if (channelIdParam) {
      setChannelId(channelIdParam);
    }
  }, [location.search, navigate]);

  // Dialog state
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // Primary creator language
  const [primaryCreatorLanguage, setPrimaryCreatorLanguage] = useState('en');

  // Sample categories from VideoContext
  const categories = [
    'Education', 'Business', 'Technology', 'Cooking', 'Travel',
    'Music', 'Sports', 'Gaming', 'Health', 'Science'
  ];

  // Filter videos to only show the current user's videos
  const creatorVideos = videos.filter(video => video.creator?.id === currentUser?.id);

  // Fallback mock video if no videos are found
  const mockCreatorVideo: Video =
    {
      id: 'creator1',
      title: 'How to Cook Italian Pasta - Multilingual Tutorial',
      thumbnail: '/placeholder.svg',
      creator: currentUser || { id: 'c1', username: 'You', avatar: '/placeholder.svg' },
      views: 15240,
      likes: 987,
      createdAt: new Date(Date.now() - 15 * 24 * 60 * 60000).toISOString(),
      languages: [
        { code: 'en', name: 'English', flag: '🇺🇸' },
        { code: 'es', name: 'Spanish', flag: '🇪🇸' },
        { code: 'it', name: 'Italian', flag: '🇮🇹' },
      ],
      category: 'Cooking'
    };

  // Handle cancel
  const handleCancel = () => {
    setIsDialogOpen(false);
  };

  // State for saving video
  const [isSavingVideo, setIsSavingVideo] = useState(false);

  // Handle save video
  const handleSaveVideo = async (data: {
    engaxeUrl: string;
    title: string;
    description: string;
    thumbnail: string | null;
    thumbnailFile: File | null;
    category: string;
    defaultLanguage: string;
    defaultLanguageUrl: string;
    additionalLanguages: {
      id: string;
      languageCode: string;
      url: string;
    }[];
  }) => {
    try {
      setIsSavingVideo(true);

      // Create language array starting with the default language
      const defaultLang = languages.find(lang => lang.code === data.defaultLanguage) || languages[0];
      const videoLanguages = [
        {
          ...defaultLang,
          url: data.defaultLanguageUrl || data.engaxeUrl, // Use the default language URL
          isDefault: true // Explicitly mark as default
        }
      ];

      // Add additional languages with their URLs
      data.additionalLanguages.forEach(entry => {
        const lang = languages.find(l => l.code === entry.languageCode);
        if (lang) {
          // Make sure each language has a valid URL
          if (!entry.url) {
            console.warn(`No URL provided for language ${entry.languageCode}, using default language URL`);
            videoLanguages.push({
              ...lang,
              url: data.defaultLanguageUrl || data.engaxeUrl,
              isDefault: false
            });
          } else {
            console.log(`Adding language ${lang.name} with URL: ${entry.url}`);
            videoLanguages.push({
              ...lang,
              url: entry.url, // Store the language-specific URL
              isDefault: false
            });
          }
        }
      });

      // Log the final language array for debugging
      console.log('Final video languages array:', JSON.stringify(videoLanguages, null, 2));

      console.log('Video languages with URLs:', videoLanguages);

      // Only Engaxe URLs are supported
      if (data.engaxeUrl.includes('youtube.com') || data.engaxeUrl.includes('youtu.be')) {
        throw new Error('YouTube URLs are not supported. Please use only Engaxe URLs.');
      }

      let savedToDatabase = false;
      let databaseVideoId = '';

      // Try to save to database first
      try {
        console.log('Attempting to save video to database');

        // Extract video ID if it's a URL
        const extractEngaxeVideoId = (url: string): string | null => {
          if (!url) return null;

          // Trim the input
          const trimmedUrl = url.trim();

          // If it's just a video ID (no slashes, dots, or protocol)
          if (!trimmedUrl.includes('/') && !trimmedUrl.includes('.') && !trimmedUrl.includes(':')) {
            if (trimmedUrl.length > 3 && trimmedUrl.length < 20 && /^[a-zA-Z0-9]+$/.test(trimmedUrl)) {
              return trimmedUrl;
            }
          }

          // Engaxe URL patterns
          const patterns = [
            // Format: engaxe.com/videos/[id]
            /(?:https?:\/\/)?(?:www\.)?engaxe\.com\/videos\/([^/?]+)/i,
            // Format: engaxe.com/v/[id]
            /(?:https?:\/\/)?(?:www\.)?engaxe\.com\/v\/([^/?]+)/i,
            // Format: engaxe.com/watch/[id]
            /(?:https?:\/\/)?(?:www\.)?engaxe\.com\/watch\/([^/?]+)/i,
            // Format: engaxe.com/embed/[id]
            /(?:https?:\/\/)?(?:www\.)?engaxe\.com\/embed\/([^/?]+)/i
          ];

          // Try each pattern
          for (const pattern of patterns) {
            const match = trimmedUrl.match(pattern);
            if (match && match[1]) {
              return match[1];
            }
          }

          // If no pattern matches, try a simple extraction as a fallback
          try {
            // Try to parse as URL first
            const urlObj = new URL(trimmedUrl.startsWith('http') ? trimmedUrl : `https://${trimmedUrl}`);
            const pathParts = urlObj.pathname.split('/').filter(part => part.length > 0);

            if (pathParts.length > 0) {
              const lastPart = pathParts[pathParts.length - 1];
              if (lastPart && lastPart.length > 0 && !lastPart.includes('.') && !lastPart.includes('?')) {
                return lastPart;
              }
            }
          } catch (error) {
            // If URL parsing fails, try simple string splitting
            const parts = trimmedUrl.split('/');
            const lastPart = parts[parts.length - 1];
            if (lastPart && lastPart.length > 0 && !lastPart.includes('.') && !lastPart.includes('?')) {
              return lastPart;
            }
          }

          return null;
        };

        // Extract video ID from URL
        const videoId = extractEngaxeVideoId(data.engaxeUrl);
        const processedUrl = videoId || data.engaxeUrl;

        if (videoId) {
          console.log(`Using extracted video ID: ${videoId} (original URL: ${data.engaxeUrl})`);
        }

        // Check if we have a valid channel ID
        if (!channelId) {
          console.error('No valid channel ID available');

          // Try to create a channel on-the-fly
          console.log('Attempting to create a channel on-the-fly');
          try {
            // Prepare channel data with all required fields
            // Add a timestamp to ensure uniqueness
            const channelData = {
              name: `channel-${currentUser.id.substring(0, 8)}-${Date.now().toString().substring(9, 13)}`,
              displayName: 'Auto-created Channel',
              description: 'Auto-created channel for video upload',
              visibility: 'public',
              tags: []
            };

            console.log('Creating channel on-the-fly with data:', channelData);
            const createResponse = await channelAPI.createChannel(channelData);

            if (createResponse.success && createResponse.channel) {
              console.log('Successfully created channel on-the-fly:', createResponse.channel);
              setChannels([...channels, createResponse.channel]);
              setChannelId(createResponse.channel.id);

              // Use the newly created channel ID
              const newChannelId = createResponse.channel.id;
              console.log(`Using newly created channel ID: ${newChannelId}`);

              // Wait a moment to ensure the channel is fully created in the database
              await new Promise(resolve => setTimeout(resolve, 1000));

              // Verify the channel exists by fetching it directly
              try {
                const verifyResponse = await api.get(`/channels/${newChannelId}`);
                console.log('Channel verification response:', verifyResponse.data);
                if (!verifyResponse.data.success) {
                  console.warn('Channel verification returned unsuccessful response');
                }
              } catch (verifyError) {
                console.warn('Error verifying channel creation:', verifyError);
                // Continue anyway, as the channel might still be usable
              }

              // Save to database using the videoAPI service with the new channel ID
              console.log(`Saving video to database with new channel ID: ${newChannelId}`);
              const response = await videoAPI.saveEngaxeVideo(processedUrl, newChannelId);
              return response;
            } else {
              throw new Error('Failed to create a channel automatically. Please create a channel first.');
            }
          } catch (channelError) {
            console.error('Error creating channel on-the-fly:', channelError);
            throw new Error('Could not create a channel automatically. Please try again or create a channel manually.');
          }
        }

        console.log(`Saving video to database with existing channel ID: ${channelId}`);

        // Save to database using the videoAPI service
        try {
          console.log(`Making API call to save video: videoId=${processedUrl}, channelId=${channelId}`);
          console.log('Video languages:', videoLanguages);
          const response = await videoAPI.saveEngaxeVideo(processedUrl, channelId, videoLanguages);
          console.log('API response:', response);

          if (response && response.success) {
            savedToDatabase = true;
            databaseVideoId = response.video.id;
            console.log('Successfully saved video to database:', response);

            // Show success message
            toast({
              title: "Video Saved to Database",
              description: `Your video "${data.title}" has been saved to the database with ID: ${databaseVideoId}`,
              variant: "default"
            });

            // Refresh videos to include the newly saved video
            await refreshVideos();

            // Close dialog and return early
            setIsDialogOpen(false);
            return;
          } else {
            console.error('API call succeeded but returned success=false:', response);
            throw new Error(response?.message || 'Unknown error saving video');
          }
        } catch (apiError) {
          console.error('Error in saveEngaxeVideo API call:', apiError);

          // Show detailed error message
          toast({
            title: "Error Saving Video",
            description: `API error: ${apiError.message || 'Unknown error'}`,
            variant: "destructive"
          });

          // Try again with a different approach
          console.log('Trying alternative approach...');

          // Wait a moment before retrying
          await new Promise(resolve => setTimeout(resolve, 1000));

          try {
            // Make a direct API call
            console.log('Making direct API call to /videos/save-engaxe');
            console.log('Request payload:', {
              videoId: processedUrl,
              channelId: channelId,
              languages: videoLanguages
            });

            // Ensure each language has all required fields
            const validatedLanguages = videoLanguages.map(lang => ({
              code: lang.code,
              name: lang.name,
              flag: lang.flag || '🌐',
              url: lang.url,
              isDefault: lang.isDefault === true
            }));

            console.log('Validated languages:', JSON.stringify(validatedLanguages, null, 2));

            const directResponse = await api.post('/videos/save-engaxe', {
              videoId: processedUrl,
              channelId: channelId,
              languages: validatedLanguages
            });

            console.log('Direct API response:', directResponse.data);

            if (directResponse.data && directResponse.data.success) {
              savedToDatabase = true;
              databaseVideoId = directResponse.data.video.id;

              toast({
                title: "Video Saved Successfully",
                description: `Your video was saved using an alternative method.`,
                variant: "default"
              });

              // Refresh videos
              await refreshVideos();

              // Close dialog and return early
              setIsDialogOpen(false);
              return;
            }
          } catch (directError) {
            console.error('Direct API call also failed:', directError);
          }

          // If we get here, both approaches failed
          throw new Error('All attempts to save the video failed');
        }
      } catch (dbError) {
        console.error('Error saving to database:', dbError);

        // Show error message
        toast({
          title: "Database Save Failed",
          description: "Could not save to database. Falling back to local storage.",
          variant: "destructive"
        });
      }

      // If database save failed, save to local storage as a fallback
      console.log('Using local storage as fallback');

      // Show a message to the user
      toast({
        title: "Using Local Storage",
        description: "Saving video to local storage as fallback.",
        variant: "default"
      });

      // Save the video to local storage
      const newVideo = saveVideoLocally({
        title: data.title,
        description: data.description || `Video about ${data.title}`,
        thumbnail: data.thumbnail,
        category: data.category,
        url: data.engaxeUrl,
        languages: videoLanguages,
        defaultLanguage: data.defaultLanguage,
        additionalLanguages: data.additionalLanguages
      }, currentUser, languages);

      // Refresh videos to include the newly saved video
      console.log('Refreshing videos to include newly saved video...');
      await refreshVideos();

      // Force a second refresh after a delay to ensure the new video is loaded
      setTimeout(async () => {
        console.log('Performing secondary refresh to ensure new video is loaded');
        await refreshVideos();
      }, 3000);

      toast({
        title: "Video Added Locally",
        description: `Your video "${data.title}" has been added to local storage only.`
      });

      // Log the final status for debugging
      console.log('Final save status:', {
        savedToDatabase,
        databaseVideoId,
        localVideoId: newVideo.id
      });

      // Close dialog
      setIsDialogOpen(false);
    } catch (error) {
      console.error('Error saving video:', error);

      // Show a detailed error message
      toast({
        title: "Error Saving Video",
        description: `Failed to save video: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive"
      });

      // Show a second toast with fallback information
      if (!savedToDatabase) {
        toast({
          title: "Fallback Storage Used",
          description: `The video was saved locally but could not be saved to the database. You can still view it in your library.`,
          variant: "default"
        });
      }
    } finally {
      setIsSavingVideo(false);
    }
  };

  if (!currentUser) {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <div className="container py-8 flex items-center justify-center">
          <Card>
            <CardHeader>
              <CardTitle>Please Sign In</CardTitle>
              <CardDescription>
                You need to be signed in to access the Creator Studio
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Link to="/signin">
                <Button>Sign In</Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <main className="container py-8">
        <div className="max-w-6xl mx-auto">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h1 className="text-2xl font-bold">
                {activeTab === 'dashboard' ? t('creator.dashboard') :
                 activeTab === 'videos' ? t('creator.your_videos') :
                 activeTab === 'comments' ? t('creator.comments') :
                 activeTab === 'series' ? t('creator.series') : t('creator.studio')}
              </h1>
              <div className="flex flex-col space-y-2 mt-1 text-sm">
                <div className="flex items-center">
                  <Languages className="h-4 w-4 mr-2 text-lingstream-accent" />
                  <span className="mr-2">Primary Language:</span>
                  <select
                    value={primaryCreatorLanguage}
                    onChange={(e) => setPrimaryCreatorLanguage(e.target.value)}
                    className="bg-gray-800 border border-gray-700 rounded px-2 py-1 text-xs"
                  >
                    {languages.map((lang) => (
                      <option key={lang.code} value={lang.code}>
                        {lang.flag} {lang.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="flex items-center">
                  <span className="mr-2">Channel:</span>
                  {isLoadingChannels ? (
                    <span className="text-xs italic">Loading channels...</span>
                  ) : channels.length > 0 ? (
                    <div className="flex items-center space-x-2">
                      <select
                        value={channelId}
                        onChange={(e) => setChannelId(e.target.value)}
                        className="bg-gray-800 border border-gray-700 rounded px-2 py-1 text-xs"
                      >
                        {channels.map((channel) => (
                          <option key={channel.id} value={channel.id}>
                            {channel.name || channel.displayName}
                          </option>
                        ))}
                      </select>
                      <button
                        onClick={async () => {
                          try {
                            const channelName = prompt('Enter a name for your new channel:');
                            if (!channelName) return;

                            setIsLoadingChannels(true);
                            // Prepare channel data with all required fields
                            const channelData = {
                              name: channelName.toLowerCase().replace(/\s+/g, '-'),
                              displayName: channelName,
                              description: `${channelName} - Created by ${currentUser.username || currentUser.email}`,
                              visibility: 'public',
                              tags: []
                            };

                            console.log('Creating channel with data:', channelData);
                            const response = await channelAPI.createChannel(channelData);

                            if (response.success && response.channel) {
                              setChannels([...channels, response.channel]);
                              setChannelId(response.channel.id);
                              toast({
                                title: "Channel Created",
                                description: `Your channel "${channelName}" has been created successfully.`,
                                variant: "default"
                              });
                            }
                          } catch (error) {
                            console.error('Error creating channel:', error);
                            toast({
                              title: "Channel Creation Failed",
                              description: `Could not create channel: ${error.message}`,
                              variant: "destructive"
                            });
                          } finally {
                            setIsLoadingChannels(false);
                          }
                        }}
                        className="bg-lingstream-accent text-white rounded px-2 py-1 text-xs"
                      >
                        + New Channel
                      </button>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-2">
                      <span className="text-xs italic">No channels available</span>
                      <button
                        onClick={async () => {
                          try {
                            const channelName = prompt('Enter a name for your new channel:');
                            if (!channelName) return;

                            setIsLoadingChannels(true);
                            // Prepare channel data with all required fields
                            const channelData = {
                              name: channelName.toLowerCase().replace(/\s+/g, '-'),
                              displayName: channelName,
                              description: `${channelName} - Created by ${currentUser.username || currentUser.email}`,
                              visibility: 'public',
                              tags: []
                            };

                            console.log('Creating channel with data:', channelData);
                            const response = await channelAPI.createChannel(channelData);

                            if (response.success && response.channel) {
                              setChannels([response.channel]);
                              setChannelId(response.channel.id);
                              toast({
                                title: "Channel Created",
                                description: `Your channel "${channelName}" has been created successfully.`,
                                variant: "default"
                              });
                            }
                          } catch (error) {
                            console.error('Error creating channel:', error);
                            toast({
                              title: "Channel Creation Failed",
                              description: `Could not create channel: ${error.message}`,
                              variant: "destructive"
                            });
                          } finally {
                            setIsLoadingChannels(false);
                          }
                        }}
                        className="bg-lingstream-accent text-white rounded px-2 py-1 text-xs"
                      >
                        Create Channel
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>

            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Video
                </Button>
              </DialogTrigger>
              <DialogContent
                className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto"
                aria-describedby="video-dialog-description"
              >
                <DialogHeader>
                  <DialogTitle>Add a New Video</DialogTitle>
                  <DialogDescription id="video-dialog-description">
                    Fill in the details to add a new video to your channel.
                  </DialogDescription>
                </DialogHeader>

                <AddVideoWizard
                  languages={languages}
                  categories={categories}
                  onSave={handleSaveVideo}
                  onCancel={handleCancel}
                  isSaving={isSavingVideo}
                />
              </DialogContent>
            </Dialog>
          </div>

          <CreatorNavigation activeTab={activeTab} />

          {activeTab === 'dashboard' && (
            <DashboardStats />
          )}

          {activeTab === 'videos' && (
            <Card>
              <CardHeader>
                <CardTitle>Your Videos</CardTitle>
                <CardDescription>
                  Manage your multilingual videos and language versions
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="flex justify-center items-center py-12">
                    <Loader2 className="h-8 w-8 animate-spin text-lingstream-accent" />
                    <span className="ml-2">Loading your videos...</span>
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Video</TableHead>
                        <TableHead>Language Versions</TableHead>
                        <TableHead className="text-right">Views</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {creatorVideos.map((video) => (
                        <TableRow key={video.id}>
                          <TableCell className="font-medium">
                            <div className="flex items-center space-x-3">
                              <img
                                src={video.thumbnail}
                                alt={video.title}
                                className="h-12 w-20 rounded object-cover"
                              />
                              <div>
                                <div className="font-medium">{video.title}</div>
                                <div className="text-xs text-lingstream-muted">{video.category}</div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex flex-wrap gap-1">
                              {video.languages.map((lang, index) => (
                                <div
                                  key={`${video.id}-${lang.code}-${index}`}
                                  className="flex items-center rounded bg-lingstream-card px-1.5 py-0.5 text-xs"
                                >
                                  <span className="mr-1">{lang.flag}</span>
                                  {lang.name}
                                </div>
                              ))}
                              <VideoLinkForm videoId={video.id} />
                            </div>
                          </TableCell>
                          <TableCell className="text-right">{video.views.toLocaleString()}</TableCell>
                          <TableCell>
                            <div className="flex space-x-2">
                              <Link to={`/video/${video.id}`}>
                                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                  <LinkIcon className="h-4 w-4" />
                                </Button>
                              </Link>
                              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="sm" className="h-8 w-8 p-0 text-red-500 hover:text-red-600">
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}

                      {creatorVideos.length === 0 && !isLoading && (
                        <TableRow>
                          <TableCell colSpan={4} className="text-center py-8">
                            <p className="text-lingstream-muted">No videos yet. Click "Add Video" to create one.</p>
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
            </Card>
          )}

          {activeTab === 'comments' && (
            <Card>
              <CardHeader>
                <CardTitle>{t('creator.comments')}</CardTitle>
                <CardDescription>
                  {t('creator.manage_comments')}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-center py-8 text-lingstream-muted">{t('creator.no_comments')}</p>
              </CardContent>
            </Card>
          )}

          {activeTab === 'series' && (
            <Card>
              <CardHeader>
                <CardTitle>{t('creator.series')}</CardTitle>
                <CardDescription>
                  {t('creator.organize_series')}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-center py-8 text-lingstream-muted">{t('creator.no_series')}</p>
              </CardContent>
            </Card>
          )}

          {/* Home tab removed as it now redirects to the main home page */}
        </div>
      </main>
    </div>
  );
}

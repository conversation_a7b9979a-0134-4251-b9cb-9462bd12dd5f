import api from './api';

/**
 * Channel API service
 */
export const channelAPI = {
  /**
   * Create a new channel
   */
  createChannel: async (channelData: {
    name: string;
    displayName: string;
    description: string;
    visibility?: 'public' | 'private' | 'unlisted';
    avatar?: string;
    banner?: string;
    category?: string;
    tags?: string[];
  }) => {
    try {
      console.log('Creating channel with data:', channelData);

      // Ensure all required fields are present
      if (!channelData.name) {
        throw new Error('Channel name is required');
      }

      if (!channelData.displayName) {
        // Use name as displayName if not provided
        channelData.displayName = channelData.name;
      }

      if (!channelData.description) {
        throw new Error('Channel description is required');
      }

      if (!channelData.tags) {
        channelData.tags = [];
      }

      const response = await api.post('/channels', channelData);
      console.log('Create channel response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error creating channel:', error);
      if (error.response) {
        console.error('Response status:', error.response.status);
        console.error('Response data:', error.response.data);

        // Extract error message from response if available
        const errorMessage = error.response.data?.error?.message ||
                            error.response.data?.message ||
                            'Failed to create channel';

        throw new Error(errorMessage);
      }
      throw error;
    }
  },

  /**
   * Update an existing channel
   */
  updateChannel: async (channelId: string, channelData: {
    name?: string;
    displayName?: string;
    description?: string;
    avatar?: string;
    banner?: string;
    category?: string;
    tags?: string[];
    socialLinks?: {
      website?: string;
      facebook?: string;
      twitter?: string;
      instagram?: string;
      linkedin?: string;
      youtube?: string;
    };
    settings?: {
      defaultCommentsEnabled?: boolean;
      moderateComments?: boolean;
      showSubscriberCount?: boolean;
    };
  }) => {
    const response = await api.put(`/channels/${channelId}`, channelData);
    return response.data;
  },

  /**
   * Get a channel by ID
   */
  getChannelById: async (channelId: string) => {
    const response = await api.get(`/channels/${channelId}`);
    return response.data;
  },

  /**
   * Get a channel by name
   */
  getChannelByName: async (channelName: string) => {
    const response = await api.get(`/channels/by-name/${channelName}`);
    return response.data;
  },

  /**
   * Get channels with pagination and filtering
   */
  getChannels: async (params?: {
    page?: number;
    limit?: number;
    sort?: 'newest' | 'popular' | 'trending';
    category?: string;
    featured?: boolean;
    search?: string;
  }) => {
    const response = await api.get('/channels', { params });
    return response.data;
  },

  /**
   * Get channels for the current authenticated user
   */
  getUserChannels: async () => {
    try {
      console.log('Fetching channels for current user from /channels/user/me');
      const token = localStorage.getItem('lawengaxe-token');
      console.log('Using token:', token ? 'Token exists' : 'No token');

      const response = await api.get('/channels/user/me');
      console.log('User channels API response:', response);
      return response.data;
    } catch (error) {
      console.error('Error fetching user channels:', error);
      if (error.response) {
        console.error('Response status:', error.response.status);
        console.error('Response data:', error.response.data);

        // If unauthorized or bad request, try to get channels by user ID as fallback
        if (error.response.status === 401 || error.response.status === 400) {
          console.log(`${error.response.status} error. Trying to fetch channels by user ID as fallback`);
          try {
            // Get current user from localStorage
            const userJson = localStorage.getItem('lawengaxe-user');
            if (userJson) {
              const user = JSON.parse(userJson);
              if (user && user.id) {
                console.log(`Fetching channels for user ID: ${user.id}`);
                const fallbackResponse = await api.get(`/channels/user/${user.id}`);
                console.log('Fallback response:', fallbackResponse);
                return fallbackResponse.data;
              }
            }
          } catch (fallbackError) {
            console.error('Fallback attempt also failed:', fallbackError);
          }
        }
      }

      // Return a default response structure instead of throwing an error
      // This allows the client to continue even if the API call fails
      console.log('Returning empty channels array as fallback');
      return {
        success: false,
        message: error.message || 'Failed to fetch channels',
        channels: []
      };
    }
  },

  /**
   * Delete a channel
   */
  deleteChannel: async (channelId: string) => {
    const response = await api.delete(`/channels/${channelId}`);
    return response.data;
  },

  /**
   * Subscribe or unsubscribe from a channel
   */
  toggleSubscription: async (channelId: string) => {
    const response = await api.post(`/channels/${channelId}/subscribe`);
    return response.data;
  },
};

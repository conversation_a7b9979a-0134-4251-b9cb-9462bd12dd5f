<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Engaxe Direct Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2 {
            color: #333;
        }
        .video-container {
            width: 100%;
            aspect-ratio: 16/9;
            margin: 20px 0;
            background-color: #000;
        }
        .controls {
            margin: 20px 0;
            padding: 15px;
            background-color: #f5f5f5;
            border-radius: 5px;
        }
        button {
            padding: 8px 16px;
            background-color: #4a90e2;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        input {
            padding: 8px;
            width: 200px;
            margin-right: 10px;
        }
        .method-selector {
            margin-bottom: 15px;
        }
        .method-selector label {
            margin-right: 15px;
        }
    </style>
</head>
<body>
    <h1>Engaxe Direct Test</h1>
    
    <div class="controls">
        <div class="method-selector">
            <label>
                <input type="radio" name="method" value="script" checked> Use Script Tag
            </label>
            <label>
                <input type="radio" name="method" value="iframe"> Use Iframe
            </label>
        </div>
        
        <input type="text" id="videoId" placeholder="Enter Engaxe video ID">
        <button id="loadBtn">Load Video</button>
    </div>
    
    <div id="video-container" class="video-container"></div>
    
    <h2>Instructions</h2>
    <ol>
        <li>Enter your Engaxe video ID in the input field</li>
        <li>Select whether to use the script tag method or iframe method</li>
        <li>Click "Load Video" to load the video</li>
        <li>Check if the video controls, timeline, and audio are working properly</li>
    </ol>
    
    <script>
        // Get elements
        const videoIdInput = document.getElementById('videoId');
        const loadBtn = document.getElementById('loadBtn');
        const videoContainer = document.getElementById('video-container');
        const methodRadios = document.querySelectorAll('input[name="method"]');
        
        // Load video function
        function loadVideo() {
            const videoId = videoIdInput.value.trim();
            if (!videoId) {
                alert('Please enter a video ID');
                return;
            }
            
            // Clear container
            videoContainer.innerHTML = '';
            
            // Get selected method
            let selectedMethod = 'script';
            methodRadios.forEach(radio => {
                if (radio.checked) {
                    selectedMethod = radio.value;
                }
            });
            
            if (selectedMethod === 'script') {
                // Method 1: Using script tag and ngxEmbed function
                loadWithScript(videoId);
            } else {
                // Method 2: Using iframe
                loadWithIframe(videoId);
            }
        }
        
        // Load with script method
        function loadWithScript(videoId) {
            // Create container div
            const embedContainer = document.createElement('div');
            embedContainer.id = 'engaxe-player';
            embedContainer.style.width = '100%';
            embedContainer.style.height = '100%';
            videoContainer.appendChild(embedContainer);
            
            // Load script if not already loaded
            if (!window.ngxEmbed) {
                const script = document.createElement('script');
                script.src = 'https://engaxe.com/embed.js';
                script.onload = function() {
                    // Initialize player once script is loaded
                    window.ngxEmbed('engaxe-player', videoId);
                };
                document.head.appendChild(script);
            } else {
                // Script already loaded, just initialize player
                window.ngxEmbed('engaxe-player', videoId);
            }
        }
        
        // Load with iframe method
        function loadWithIframe(videoId) {
            const iframe = document.createElement('iframe');
            iframe.src = `https://engaxe.com/embed/${videoId}`;
            iframe.width = '100%';
            iframe.height = '100%';
            iframe.style.border = 'none';
            iframe.allowFullscreen = true;
            iframe.allow = 'autoplay; encrypted-media; picture-in-picture';
            
            videoContainer.appendChild(iframe);
        }
        
        // Event listeners
        loadBtn.addEventListener('click', loadVideo);
        videoIdInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                loadVideo();
            }
        });
    </script>
</body>
</html>

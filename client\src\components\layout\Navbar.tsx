
import { useState } from 'react';
import { Link } from 'react-router-dom';


import { Bell, MessageSquare, Search, User, LogOut, Video as VideoIcon, Settings, ChevronDown, PlusCircle, SwitchCamera, Sun, Moon, Monitor, LayoutDashboard } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useAuth } from '@/context/AuthContext';
import { useNotifications } from '@/context/NotificationContext';
import { useLanguage } from '@/context/LanguageContext';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import NotificationDropdown from '../notifications/NotificationDropdown';
import LanguageSwitcher from '../language/LanguageSwitcher';
import AddAccountDialog from '../profile/AddAccountDialog';
import AccountSwitcher from '../profile/AccountSwitcher';
import ThemeSwitcher from '../theme/ThemeSwitcher';

export default function Navbar() {

  const { currentUser, userAccounts, switchAccount, logout, isCreator, isAdmin } = useAuth();

  const { unreadCount } = useNotifications();
  const { t } = useLanguage();
  const [showNotifications, setShowNotifications] = useState(false);
  const [showAddAccountDialog, setShowAddAccountDialog] = useState(false);

  return (
    <div className="sticky top-0 z-50 w-full border-b border-border bg-lingstream-background py-2">
      <div className="container flex items-center justify-between">
        <div className="flex items-center gap-8">
          <Link to="/" className="flex items-center gap-2">
            <span className="text-xl font-bold tracking-tight">
              <span className="text-lingstream-accent">Legal</span>
              <span className="text-lingstream-text">Aid</span>
            </span>
          </Link>

          <nav className="hidden md:flex items-center space-x-4">
            <Link to="/" className="text-sm font-medium transition-colors hover:text-lingstream-accent">
              {t('nav.home')}
            </Link>
            <Link to="/trending" className="text-sm font-medium transition-colors hover:text-lingstream-accent">
              {t('nav.trending')}
            </Link>
            <Link to="/channels" className="text-sm font-medium transition-colors hover:text-lingstream-accent">
              Channels
            </Link>
          </nav>
        </div>

        <div className="hidden md:flex items-center justify-center flex-1 max-w-md">
          <div className="relative w-full">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={t('nav.search_placeholder')}
              className="w-full pl-8 bg-lingstream-card border-border"
            />
          </div>
        </div>

        <div className="flex items-center gap-4">
          <ThemeSwitcher />
          <LanguageSwitcher />
          {currentUser ? (
            <>
              <div className="relative">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setShowNotifications(!showNotifications)}
                  className="relative"
                >
                  <Bell className="h-5 w-5" />
                  {unreadCount > 0 && (
                    <span className="absolute -top-1 -right-1 flex h-4 w-4 items-center justify-center rounded-full bg-lingstream-accent text-[10px] text-white">
                      {unreadCount}
                    </span>
                  )}
                </Button>
                {showNotifications && <NotificationDropdown onClose={() => setShowNotifications(false)} />}
              </div>

              <Link to="/messages">
                <Button variant="ghost" size="icon">
                  <MessageSquare className="h-5 w-5" />
                </Button>
              </Link>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={currentUser.avatar} alt={currentUser.username} />
                      <AvatarFallback>{currentUser.username.charAt(0)}</AvatarFallback>
                    </Avatar>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56 bg-lingstream-card border-border p-0">
                  <div className="px-2 py-1.5">
                    <div className="flex items-center gap-2 cursor-pointer p-2 rounded hover:bg-lingstream-card/50">
                      <div className="flex flex-col">
                        <span className="font-medium">{currentUser.email || currentUser.username}</span>
                      </div>
                    </div>
                  </div>

                  <DropdownMenuSeparator className="my-1" />

                  {/* Switch Account Button */}
                  <div className="px-2 py-1.5">
                    <Button
                      variant="outline"
                      className="w-full flex items-center justify-center gap-2"
                    >
                      <SwitchCamera className="h-4 w-4" />
                      <span>{t('profile.switch_account') || 'Switch Account'}</span>
                    </Button>
                  </div>

                  <DropdownMenuSeparator className="my-1" />

                  {/* User Accounts */}
                  {userAccounts && userAccounts.map((account) => (
                    <DropdownMenuItem
                      key={account.id}
                      className={`flex items-center gap-2 ${account.id === currentUser.id ? 'bg-lingstream-accent/20' : ''}`}
                      onClick={() => {
                        if (account.id !== currentUser.id) {
                          switchAccount(account.id);
                        }
                      }}
                    >
                      <Avatar className="h-6 w-6">
                        <AvatarImage src={account.avatar} alt={account.username} />
                        <AvatarFallback>{account.username.charAt(0)}</AvatarFallback>
                      </Avatar>
                      <div className="flex flex-col">
                        <span className="font-medium">{account.username}</span>
                        <span className="text-xs text-muted-foreground">{account.email}</span>
                      </div>
                    </DropdownMenuItem>
                  ))}

                  {/* Add Account Button */}
                  <div className="px-2 py-1.5">
                    <Button
                      variant="outline"
                      className="w-full flex items-center justify-center gap-2"
                      onClick={() => setShowAddAccountDialog(true)}
                    >
                      <PlusCircle className="h-4 w-4" />
                      <span>{t('profile.add_account') || 'Add Account'}</span>
                    </Button>
                  </div>

                  <DropdownMenuSeparator className="my-1" />

                  <DropdownMenuItem className="flex items-center gap-2" asChild>
                    <Link to="/profile">
                      <User className="h-4 w-4 mr-2" />
                      {t('nav.profile')}
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem className="flex items-center gap-2" asChild>
                    <Link to="/creator-studio">
                      <VideoIcon className="h-4 w-4 mr-2" />
                      {t('nav.creator_studio')}
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem className="flex items-center gap-2" asChild>
                    <Link to="/create-channel">
                      <PlusCircle className="h-4 w-4 mr-2" />
                      Create Channel
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem className="flex items-center gap-2" asChild>
                    <Link to="/profile?tab=settings">
                      <Settings className="h-4 w-4 mr-2" />
                      {t('profile.settings')}
                    </Link>
                  </DropdownMenuItem>
                  {isAdmin && (
                    <DropdownMenuItem className="flex items-center gap-2" asChild>
                      <Link to="/admin">
                        <LayoutDashboard className="h-4 w-4 mr-2" />
                        Admin Panel
                      </Link>
                    </DropdownMenuItem>
                  )}
                  <DropdownMenuItem
                    className="flex items-center gap-2 text-destructive focus:text-destructive"
                    onClick={logout}
                  >
                    <LogOut className="h-4 w-4 mr-2" />
                    {t('nav.logout')}
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </>
          ) : (
            <>
              <Link to="/signin">
                <Button variant="outline" size="sm">{t('auth.sign_in')}</Button>
              </Link>
              <Link to="/signup">
                <Button size="sm">{t('auth.sign_up')}</Button>
              </Link>
            </>
          )}
        </div>
      </div>

      {/* Add Account Dialog */}
      <AddAccountDialog
        open={showAddAccountDialog}
        onOpenChange={setShowAddAccountDialog}
      />
    </div>
  );
}

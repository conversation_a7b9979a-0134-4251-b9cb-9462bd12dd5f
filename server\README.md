# LawEngaxe Server

This is the backend server for the LawEngaxe application, built with Fastify, Mongoose, Redis, and Typesense.

## Technologies

- **Fastify**: Fast and low overhead web framework for Node.js
- **Mongoose**: MongoDB object modeling for Node.js
- **Redis**: In-memory data structure store, used for caching and session management
- **Typesense**: Fast, typo-tolerant search engine

## Project Structure

```
server/
├── src/
│   ├── config/           # Configuration files
│   ├── controllers/      # Request handlers
│   ├── middleware/       # Custom middleware
│   ├── models/           # Mongoose models
│   ├── routes/           # API routes
│   ├── seeders/          # Database seeders
│   ├── services/         # Business logic
│   ├── utils/            # Utility functions
│   └── index.ts          # Application entry point
├── .env.example          # Example environment variables
├── package.json          # Project dependencies
├── tsconfig.json         # TypeScript configuration
└── README.md             # Project documentation
```

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- MongoDB
- Redis
- Typesense

### Installation

1. Clone the repository
2. Navigate to the server directory:
   ```
   cd LawEngaxe/server
   ```
3. Install dependencies:
   ```
   npm install
   ```
4. Copy the example environment file and update it with your configuration:
   ```
   cp .env.example .env
   ```
5. Seed the database with initial data (optional):
   ```
   npm run seed
   ```
6. Start the development server:
   ```
   npm run dev
   ```

## Database Seeding

The server includes a database seeder to populate the database with initial data for development and testing purposes. The seeder creates sample users, roles, permissions, videos, and other entities.

### Available Seeders

- **Permission Seeder**: Creates basic permissions for the RBAC system
- **Role Seeder**: Creates roles with assigned permissions
- **User Seeder**: Creates sample users with different roles
- **SystemConfig Seeder**: Sets up the system configuration
- **NotificationTemplate Seeder**: Creates notification templates
- **Video Seeder**: Creates sample video content

### Running the Seeder

To seed the database with sample data:

```
npm run seed
```

For development with auto-reload:

```
npm run seed:dev
```

## API Documentation

Once the server is running, you can access the API documentation at:
```
http://localhost:3000/documentation
```

## Database Models

The server uses Mongoose to define and interact with MongoDB. The following models are available:

- **User**: User accounts and profiles
- **Permission**: Individual permissions for RBAC
- **Role**: Role-based access control
- **Video**: Video content
- **NotificationTemplate**: Templates for notifications
- **Notification**: Individual notifications
- **SystemConfig**: Global system configuration

## Environment Variables

The following environment variables are used by the server:

- `PORT`: Server port (default: 3000)
- `NODE_ENV`: Environment (development, production, test)
- `API_PREFIX`: API route prefix
- `MONGODB_URI`: MongoDB connection string
- `JWT_SECRET`: Secret for JWT token generation
- `REDIS_HOST`: Redis server host
- `REDIS_PORT`: Redis server port
- `REDIS_PASSWORD`: Redis server password
- `TYPESENSE_HOST`: Typesense server host
- `TYPESENSE_PORT`: Typesense server port
- `TYPESENSE_API_KEY`: Typesense API key

## License

This project is licensed under the MIT License.

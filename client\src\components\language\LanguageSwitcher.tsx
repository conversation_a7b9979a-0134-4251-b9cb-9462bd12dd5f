import React from 'react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuSeparator
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { Globe } from 'lucide-react';
import { useLanguage, availableLanguages, SupportedLanguage } from '@/context/LanguageContext';

export default function LanguageSwitcher() {
  const { currentLanguage, setLanguage, t } = useLanguage();

  // Debug current language
  console.log('Current language:', currentLanguage);

  // Group languages by region
  const indianLanguages = availableLanguages.filter(lang =>
    ['hi', 'mr', 'gu'].includes(lang.code)
  );

  const foreignLanguages = availableLanguages.filter(lang =>
    !['hi', 'mr', 'gu', 'en'].includes(lang.code)
  );

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="flex items-center gap-2">
          <Globe className="h-4 w-4" />
          <span className="text-base mr-1">{currentLanguage.flag}</span>
          <span className="hidden md:inline">{currentLanguage.nativeName}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56 bg-lingstream-card border-border">
        <DropdownMenuLabel>{t('language.select')}</DropdownMenuLabel>

        {/* English */}
        <DropdownMenuItem
          className={currentLanguage.code === 'en' ? 'bg-lingstream-hover' : ''}
          onClick={() => {
              console.log('Switching to English language');
              setLanguage('en');
            }}
        >
          <span className="text-base mr-2">🇺🇸</span>
          English
          {currentLanguage.code === 'en' && (
            <span className="ml-2 h-1.5 w-1.5 rounded-full bg-lingstream-accent" />
          )}
        </DropdownMenuItem>

        <DropdownMenuSeparator />
        <DropdownMenuLabel>{t('language.indian')}</DropdownMenuLabel>

        {/* Indian languages */}
        {indianLanguages.map((lang) => (
          <DropdownMenuItem
            key={lang.code}
            className={currentLanguage.code === lang.code ? 'bg-lingstream-hover' : ''}
            onClick={() => {
              console.log(`Switching to language: ${lang.code}`);
              setLanguage(lang.code as SupportedLanguage);
            }}
          >
            <span className="text-base mr-2">{lang.flag}</span>
            <span>{lang.name}</span>
            <span className="ml-2 text-xs text-lingstream-muted">({lang.nativeName})</span>
            {currentLanguage.code === lang.code && (
              <span className="ml-2 h-1.5 w-1.5 rounded-full bg-lingstream-accent" />
            )}
          </DropdownMenuItem>
        ))}

        <DropdownMenuSeparator />
        <DropdownMenuLabel>{t('language.foreign')}</DropdownMenuLabel>

        {/* Foreign languages */}
        {foreignLanguages.map((lang) => (
          <DropdownMenuItem
            key={lang.code}
            className={currentLanguage.code === lang.code ? 'bg-lingstream-hover' : ''}
            onClick={() => {
              console.log(`Switching to language: ${lang.code}`);
              setLanguage(lang.code as SupportedLanguage);
            }}
          >
            <span className="text-base mr-2">{lang.flag}</span>
            <span>{lang.name}</span>
            <span className="ml-2 text-xs text-lingstream-muted">({lang.nativeName})</span>
            {currentLanguage.code === lang.code && (
              <span className="ml-2 h-1.5 w-1.5 rounded-full bg-lingstream-accent" />
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

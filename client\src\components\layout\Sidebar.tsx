import { Link, useLocation } from 'react-router-dom';
import { useVideos } from '@/context/VideoContext';
import { cn } from '@/lib/utils';
import { Home, TrendingUp, Bookmark, History, Settings, Video, Flag } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';

export default function Sidebar() {
  const { categories } = useVideos();
  const { isCreator } = useAuth();
  const location = useLocation();
  const currentPath = location.pathname;

  // Map of category icons
  const categoryIcons: Record<string, string> = {
    Education: '🎓',
    Business: '💼',
    Technology: '💻',
    Cooking: '🍳',
    Travel: '✈️',
    Music: '🎵',
    Sports: '⚽',
    Gaming: '🎮',
    Health: '🩺',
    Science: '🔬'
  };

  return (
    <div className="w-64 h-[calc(100vh-60px)] overflow-y-auto border-r border-border py-4 px-2 flex-shrink-0 hidden md:block">
      <div className="space-y-6">
        {/* Main Navigation */}
        <div className="space-y-1">
          <Link
            to="/"
            className={cn(
              "flex items-center gap-3 px-3 py-2 rounded-md text-sm transition-colors",
              currentPath === "/"
                ? "bg-lingstream-accent/10 text-lingstream-accent"
                : "hover:bg-lingstream-hover"
            )}
          >
            <Home className="h-4 w-4" />
            <span>Home</span>
          </Link>

          <Link
            to="/trending"
            className={cn(
              "flex items-center gap-3 px-3 py-2 rounded-md text-sm transition-colors",
              currentPath === "/trending"
                ? "bg-lingstream-accent/10 text-lingstream-accent"
                : "hover:bg-lingstream-hover"
            )}
          >
            <TrendingUp className="h-4 w-4" />
            <span>Trending</span>
          </Link>

          <Link
            to="/report-issue"
            className={cn(
              "flex items-center gap-3 px-3 py-2 rounded-md text-sm transition-colors",
              currentPath === "/report-issue"
                ? "bg-lingstream-accent/10 text-lingstream-accent"
                : "hover:bg-lingstream-hover"
            )}
          >
            <Flag className="h-4 w-4" />
            <span>Report Issue</span>
          </Link>
        </div>

        {/* Categories */}
        <div>
          <h3 className="px-3 text-xs uppercase text-lingstream-muted font-medium mb-2">
            Categories
          </h3>
          <div className="space-y-1">
            {categories.map((category) => (
              <Link
                key={category}
                to={`/?category=${category.toLowerCase()}`}
                className={cn(
                  "flex items-center gap-3 px-3 py-2 rounded-md text-sm transition-colors",
                  location.search === `?category=${category.toLowerCase()}`
                    ? "bg-lingstream-accent/10 text-lingstream-accent"
                    : "hover:bg-lingstream-hover"
                )}
              >
                <span className="text-lg">{categoryIcons[category] || '📺'}</span>
                <span>{category}</span>
              </Link>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

import { FastifyRequest, FastifyReply } from 'fastify';
import userService from '../services/user.service';
import { UserModel } from '../models';
import { AuthenticatedUser } from '../types/user';
import { engaxeApiService } from '../services/engaxe-api.service';
import { createBadRequestError, createUnauthorizedError } from '../utils/errors/AppError';
import { ErrorCodes } from '../utils/errors/errorCodes';

// Extend FastifyInstance to include custom JWT methods
declare module 'fastify' {
  interface FastifyInstance {
    generateAccessToken: (payload: any) => string;
    generateRefreshToken: (payload: any) => string;
    verifyRefreshToken: (token: string) => Promise<any>;
    getCurrentUser: (request: FastifyRequest) => Promise<any>;
  }
}

// Define custom JWT payload type
interface JwtPayload {
  id: string;
  email: string;
  type?: string;
  roles?: string[];
}

/**
 * Authentication controller for handling auth-related requests
 */
export class AuthController {
  /**
   * Login with Engaxe credentials
   */
  async loginWithEngaxe(
    request: FastifyRequest<{
      Body: {
        username: string;
        password: string;
      };
    }>,
    reply: FastifyReply
  ) {
    try {
      const { username, password } = request.body;

      // Login to Engaxe
      const engaxeAuth = await engaxeApiService.login(username, password);

      // Check if user exists in our system
      let user = await UserModel.findOne({
        'engaxe.userId': engaxeAuth.userId,
        deletedAt: null,
      });

      if (!user) {
        // Create a new user if not found
        user = new UserModel({
          username: `engaxe_${engaxeAuth.userId}`,
          email: `${engaxeAuth.userId}@engaxe.user`,
          password: Math.random().toString(36).substring(2, 15), // Random password
          firstName: 'Engaxe',
          lastName: 'User',
          displayName: `Engaxe User ${engaxeAuth.userId}`,
          status: 'active',
          emailVerified: true,
          roles: ['user'],
          engaxe: {
            userId: engaxeAuth.userId,
            sessionId: engaxeAuth.sessionId,
            deviceId: engaxeAuth.deviceId,
            expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
          },
          createdBy: 'system',
          updatedBy: 'system',
        });
      } else {
        // Update Engaxe session info
        user.engaxe = {
          userId: engaxeAuth.userId,
          sessionId: engaxeAuth.sessionId,
          deviceId: engaxeAuth.deviceId,
          expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
        };
        user.updatedBy = user.id;
      }

      // Save user
      await user.save();

      // Generate tokens
      const accessToken = request.server.generateAccessToken({
        id: user.id,
        email: user.email,
        roles: user.roles,
      });

      const refreshToken = request.server.generateRefreshToken({
        id: user.id,
        email: user.email,
      });

      // Remove sensitive data
      const userResponse = {
        id: user.id,
        username: user.username,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        displayName: user.displayName,
        roles: user.roles,
        engaxeUserId: user.engaxe?.userId,
      };

      return reply.code(200).send({
        success: true,
        message: 'Engaxe login successful',
        accessToken,
        refreshToken,
        user: userResponse,
      });
    } catch (error: any) {
      request.log.error(error);
      return reply.code(401).send({
        success: false,
        message: error.message || 'Engaxe login failed',
      });
    }
  }
  /**
   * Login user and generate tokens
   */
  async login(
    request: FastifyRequest<{
      Body: {
        email: string;
        password: string;
      };
    }>,
    reply: FastifyReply
  ) {
    try {
      const { email, password } = request.body;
      const { user, isValid } = await userService.loginUser(email, password);

      if (!user || !isValid) {
        return reply.code(401).send({
          success: false,
          message: 'Invalid email or password',
        });
      }

      // Generate tokens
      const accessToken = request.server.generateAccessToken({
        id: user.id,
        email: user.email,
        roles: user.roles,
      });

      const refreshToken = request.server.generateRefreshToken({
        id: user.id,
        email: user.email,
      });

      // Remove sensitive data
      const userResponse = {
        id: user.id,
        username: user.username,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        displayName: user.displayName,
        roles: user.roles,
      };

      return reply.code(200).send({
        success: true,
        message: 'Login successful',
        accessToken,
        refreshToken,
        user: userResponse,
      });
    } catch (error: any) {
      request.log.error(error);
      return reply.code(401).send({
        success: false,
        message: error.message || 'Login failed',
      });
    }
  }

  /**
   * Refresh access token using refresh token
   */
  async refreshToken(
    request: FastifyRequest<{
      Body: {
        refreshToken: string;
      };
    }>,
    reply: FastifyReply
  ) {
    try {
      const { refreshToken } = request.body;

      // Verify refresh token
      const userData = await request.server.verifyRefreshToken(refreshToken);

      // Generate new access token
      const accessToken = request.server.generateAccessToken({
        id: userData.id,
        email: userData.email,
        roles: userData.roles,
      });

      return reply.code(200).send({
        success: true,
        accessToken,
      });
    } catch (error: any) {
      request.log.error(error);
      return reply.code(401).send({
        success: false,
        message: error.message || 'Token refresh failed',
      });
    }
  }

  /**
   * Get current user profile
   */
  async me(request: FastifyRequest, reply: FastifyReply) {
    try {
      // Get current user
      const user = await request.server.getCurrentUser(request);

      // Remove sensitive data
      const userResponse = {
        id: user.id,
        username: user.username,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        displayName: user.displayName,
        avatar: user.avatar,
        roles: user.roles,
        permissions: user.permissions,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      };

      return reply.code(200).send({
        success: true,
        user: userResponse,
      });
    } catch (error: any) {
      request.log.error(error);
      return reply.code(401).send({
        success: false,
        message: error.message || 'Authentication failed',
      });
    }
  }

  /**
   * Verify email with token
   */
  async verifyEmail(
    request: FastifyRequest<{
      Params: {
        token: string;
      };
    }>,
    reply: FastifyReply
  ) {
    try {
      const { token } = request.params;

      // Verify token
      const decoded = request.server.jwt.verify(token) as JwtPayload;

      // Check if it's an email verification token
      if (decoded.type !== 'email_verification') {
        throw new Error('Invalid token type');
      }

      // Get user
      const user = await UserModel.findOne({
        id: decoded.id,
        deletedAt: { $exists: false },
      });

      if (!user) {
        throw new Error('User not found');
      }

      // Update user
      user.emailVerified = true;
      user.status = 'active';
      user.updatedBy = user.id;
      await user.save();

      return reply.code(200).send({
        success: true,
        message: 'Email verified successfully',
      });
    } catch (error: any) {
      request.log.error(error);
      return reply.code(400).send({
        success: false,
        message: error.message || 'Email verification failed',
      });
    }
  }

  /**
   * Request password reset
   */
  async requestPasswordReset(
    request: FastifyRequest<{
      Body: {
        email: string;
      };
    }>,
    reply: FastifyReply
  ) {
    try {
      const { email } = request.body;

      // Find user
      const user = await UserModel.findOne({
        email,
        deletedAt: { $exists: false },
      });

      if (!user) {
        // Don't reveal that the user doesn't exist
        return reply.code(200).send({
          success: true,
          message: 'If your email is registered, you will receive a password reset link',
        });
      }

      // Generate password reset token
      const resetToken = request.server.jwt.sign(
        {
          id: user.id,
          email: user.email,
          type: 'password_reset',
        },
        { expiresIn: '1h' }
      );

      // In a real application, you would send an email with the reset link
      // For now, we'll just return the token in the response
      return reply.code(200).send({
        success: true,
        message: 'If your email is registered, you will receive a password reset link',
        // Include the token for testing purposes
        resetToken: resetToken,
      });
    } catch (error: any) {
      request.log.error(error);
      return reply.code(400).send({
        success: false,
        message: error.message || 'Password reset request failed',
      });
    }
  }

  /**
   * Reset password with token
   */
  async resetPassword(
    request: FastifyRequest<{
      Body: {
        token: string;
        newPassword: string;
        confirmPassword: string;
      };
    }>,
    reply: FastifyReply
  ) {
    try {
      const { token, newPassword, confirmPassword } = request.body;

      // Check if passwords match
      if (newPassword !== confirmPassword) {
        throw new Error('Passwords do not match');
      }

      // Verify token
      const decoded = request.server.jwt.verify(token) as JwtPayload;

      // Check if it's a password reset token
      if (decoded.type !== 'password_reset') {
        throw new Error('Invalid token type');
      }

      // Get user
      const user = await UserModel.findOne({
        id: decoded.id,
        email: decoded.email,
        deletedAt: { $exists: false },
      });

      if (!user) {
        throw new Error('User not found');
      }

      // Update password
      user.password = newPassword; // Will be hashed by pre-save hook
      user.updatedBy = user.id;
      await user.save();

      return reply.code(200).send({
        success: true,
        message: 'Password reset successful',
      });
    } catch (error: any) {
      request.log.error(error);
      return reply.code(400).send({
        success: false,
        message: error.message || 'Password reset failed',
      });
    }
  }
}

export default new AuthController();

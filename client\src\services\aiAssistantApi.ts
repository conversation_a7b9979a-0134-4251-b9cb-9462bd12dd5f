import api from './api';
import { AIAssistantProvider } from '@/context/ChatbotContext';

/**
 * AI Assistant API service
 * Handles all API calls related to AI Assistant
 */
export const aiAssistantAPI = {
  /**
   * Send a message to the AI Assistant
   */
  sendMessage: async (data: {
    message: string;
    provider: AIAssistantProvider;
    apiKey: string;
    endpoint?: string;
    conversationId?: string;
    creatorId: string;
    creatorName?: string; // Added creator name for personalized responses
    videoId?: string;
    chatHistory?: Array<{
      role: 'user' | 'assistant';
      content: string;
    }>;
  }) => {
    try {
      // In a real implementation, we would send this to the backend
      // For now, we'll simulate different AI providers with mock responses

      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Use creator name if provided, otherwise use a generic fallback
      const creatorName = data.creatorName || 'Video Creator';

      let response = '';

      // Generate a response that appears to come from the creator
      // No mention of AI in any of the responses
      const responses = [
        `I'm happy to help! ${simulateResponse(data.message)}`,
        `Thanks for your message about my video! ${simulateResponse(data.message)}`,
        `Thank you for reaching out about my content! ${simulateResponse(data.message)}`,
        `I appreciate your interest in my video! ${simulateResponse(data.message)}`,
        `Thanks for watching! ${simulateResponse(data.message)}`
      ];

      // Select a random response style for variety
      const randomIndex = Math.floor(Math.random() * responses.length);
      response = responses[randomIndex];

      return {
        success: true,
        data: {
          response,
          conversationId: data.conversationId || `conv_${Date.now()}`,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      console.error('Error sending message to creator:', error);
      return {
        success: false,
        error: 'Failed to deliver your message. Please try again.'
      };
    }
  }
};

/**
 * Helper function to simulate AI responses
 */
function simulateResponse(message: string): string {
  // Simple response generation based on message content
  if (message.toLowerCase().includes('hello') || message.toLowerCase().includes('hi')) {
    return "Hello! How can I help you with this video today?";
  }

  if (message.toLowerCase().includes('thank')) {
    return "You're welcome! Feel free to ask if you have any other questions.";
  }

  if (message.toLowerCase().includes('how') && message.toLowerCase().includes('work')) {
    return "That's a great question about how this works. In the video, I explain the process step by step, but essentially it involves understanding the core concepts and applying them correctly.";
  }

  if (message.toLowerCase().includes('explain')) {
    return "I'd be happy to explain that further. The concept I covered in the video is based on several key principles that work together to achieve the result.";
  }

  if (message.toLowerCase().includes('when') || message.toLowerCase().includes('time')) {
    return "The timing depends on several factors that I mentioned in the video. Generally, it takes about 2-3 weeks to see results, but it can vary based on your specific situation.";
  }

  // Default response for other messages
  return "That's an interesting point! In my video, I cover several aspects of this topic. Is there something specific you'd like me to elaborate on?";
}

export default aiAssistantAPI;

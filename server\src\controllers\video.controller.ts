import { FastifyRequest, FastifyReply, RouteGenericInterface } from 'fastify';
import { AuthenticatedUser } from '../types/user';
import { VideoService, videoService, videoIdMappingService } from '../services';
import { extractEngaxeVideoId } from '../utils/video.utils';
import { VideoModel } from '../models';
import { isValidEngaxeId, generateEngaxeId } from '../utils/id';
import { handleControllerError } from '../utils/error.utils';
import { hashIdToEngaxeId } from '../utils/id-mapping.utils';

// Helper function to get a flag emoji for a language code
function getLanguageFlag(code: string): string {
  if (!code) return '🌐';

  switch (code.toLowerCase()) {
    case 'en': return '🇺🇸';
    case 'hi': return '🇮🇳';
    case 'es': return '🇪🇸';
    case 'fr': return '🇫🇷';
    case 'de': return '🇩🇪';
    case 'ja': return '🇯🇵';
    case 'zh': return '🇨🇳';
    case 'ru': return '🇷🇺';
    case 'ar': return '🇸🇦';
    case 'pt': return '🇵🇹';
    case 'it': return '🇮🇹';
    case 'nl': return '🇳🇱';
    case 'ko': return '🇰🇷';
    case 'tr': return '🇹🇷';
    // Add more Indian languages
    case 'bn': return '🇮🇳'; // Bengali
    case 'ta': return '🇮🇳'; // Tamil
    case 'te': return '🇮🇳'; // Telugu
    case 'mr': return '🇮🇳'; // Marathi
    case 'gu': return '🇮🇳'; // Gujarati
    case 'kn': return '🇮🇳'; // Kannada
    case 'ml': return '🇮🇳'; // Malayalam
    case 'pa': return '🇮🇳'; // Punjabi
    case 'or': return '🇮🇳'; // Odia
    case 'as': return '🇮🇳'; // Assamese
    case 'ur': return '🇵🇰'; // Urdu
    default: return '🌐';
  }
}

// Define a custom request type with authenticated user
interface AuthenticatedRequest<T extends RouteGenericInterface = RouteGenericInterface> extends FastifyRequest<T> {
  user: AuthenticatedUser;
}

/**
 * Controller for video-related endpoints
 */
export class VideoController {
  private videoService = videoService;

  constructor() {}



  /**
   * Upload a new video
   */
  uploadVideo = async (request: AuthenticatedRequest<{
    Body: {
      title: string;
      description: string;
      channelId: string;
      category: string;
      tags: string[];
      visibility: 'public' | 'unlisted' | 'private' | 'scheduled';
      scheduledPublishTime?: string;
      contentRating: 'general' | 'teen' | 'mature' | 'explicit';
      commentsEnabled?: boolean;
      ratingsEnabled?: boolean;
      embeddingEnabled?: boolean;
      chapters?: Array<{
        title: string;
        startTime: number;
        endTime: number;
      }>;
      location?: {
        latitude: number;
        longitude: number;
        name: string;
      };
      copyright?: {
        owner: string;
        license: string;
        allowReuse: boolean;
        allowCommercialUse: boolean;
        allowModification: boolean;
        attributionRequired: boolean;
      };
      file: {
        originalName: string;
        size: number;
        mimeType: string;
      };
      thumbnailUrl: string;
    }
  }>, reply: FastifyReply) => {
    try {
      // Get user ID from authenticated request
      const userId = request.user.id;

      // Upload video
      const video = await this.videoService.uploadVideo(userId, request.body);

      return reply.code(201).send({
        success: true,
        message: 'Video uploaded successfully',
        video: {
          id: video.id,
          title: video.title,
          description: video.description,
          thumbnailUrl: video.thumbnailUrl,
          url: video.url,
          duration: video.duration,
          userId: video.userId,
          channelId: video.channelId,
          visibility: video.visibility,
          processingStatus: video.processingStatus,
          createdAt: video.createdAt,
        },
      });
    } catch (error: any) {
      request.log.error(error);

      // Handle specific error types
      if (error.statusCode === 404) {
        return reply.code(404).send({
          success: false,
          message: error.message || 'Channel not found',
          error: {
            code: error.errorCode || 'NOT_FOUND',
            message: error.message,
          },
        });
      } else if (error.statusCode === 403) {
        return reply.code(403).send({
          success: false,
          message: error.message || 'Permission denied',
          error: {
            code: error.errorCode || 'FORBIDDEN',
            message: error.message,
          },
        });
      }

      return reply.code(400).send({
        success: false,
        message: error.message || 'Failed to upload video',
        error: {
          code: error.errorCode || 'BAD_REQUEST',
          message: error.message,
        },
      });
    }
  };

  /**
   * Import a video from an external source
   */
  importVideo = async (request: AuthenticatedRequest<{
    Body: {
      title: string;
      description: string;
      channelId: string;
      category: string;
      tags: string[];
      visibility: 'public' | 'unlisted' | 'private' | 'scheduled';
      scheduledPublishTime?: string;
      contentRating: 'general' | 'teen' | 'mature' | 'explicit';
      commentsEnabled?: boolean;
      ratingsEnabled?: boolean;
      embeddingEnabled?: boolean;
      chapters?: Array<{
        title: string;
        startTime: number;
        endTime: number;
      }>;
      source: {
        type: 'import' | 'embed';
        originalUrl: string;
        platform: string;
        externalId?: string;
      };
      thumbnailUrl?: string;
      duration?: number;
      languages?: Array<{
        code: string;
        name: string;
        flag?: string;
        url?: string;
        isDefault?: boolean;
      }>;
    }
  }>, reply: FastifyReply) => {
    try {
      // Get user ID from authenticated request
      const userId = request.user.id;

      // Import video
      const video = await this.videoService.importVideo(userId, request.body);

      return reply.code(201).send({
        success: true,
        message: 'Video imported successfully',
        video: {
          id: video.id,
          title: video.title,
          description: video.description,
          thumbnailUrl: video.thumbnailUrl,
          url: video.url,
          duration: video.duration,
          userId: video.userId,
          channelId: video.channelId,
          visibility: video.visibility,
          processingStatus: video.processingStatus,
          source: video.source,
          languages: video.languages || [],
          createdAt: video.createdAt,
        },
      });
    } catch (error: any) {
      request.log.error(error);

      // Handle specific error types
      if (error.statusCode === 404) {
        return reply.code(404).send({
          success: false,
          message: error.message || 'Channel not found',
          error: {
            code: error.errorCode || 'NOT_FOUND',
            message: error.message,
          },
        });
      } else if (error.statusCode === 403) {
        return reply.code(403).send({
          success: false,
          message: error.message || 'Permission denied',
          error: {
            code: error.errorCode || 'FORBIDDEN',
            message: error.message,
          },
        });
      } else if (error.statusCode === 422) {
        return reply.code(422).send({
          success: false,
          message: error.message || 'Invalid source URL',
          error: {
            code: error.errorCode || 'UNPROCESSABLE_ENTITY',
            message: error.message,
          },
        });
      }

      return reply.code(400).send({
        success: false,
        message: error.message || 'Failed to import video',
        error: {
          code: error.errorCode || 'BAD_REQUEST',
          message: error.message,
        },
      });
    }
  };

  /**
   * Update an existing video
   */
  updateVideo = async (request: AuthenticatedRequest<{
    Params: {
      id: string;
    };
    Body: {
      title?: string;
      description?: string;
      channelId?: string;
      category?: string;
      tags?: string[];
      visibility?: 'public' | 'unlisted' | 'private' | 'scheduled';
      scheduledPublishTime?: string;
      contentRating?: 'general' | 'teen' | 'mature' | 'explicit';
      commentsEnabled?: boolean;
      ratingsEnabled?: boolean;
      embeddingEnabled?: boolean;
      chapters?: Array<{
        title: string;
        startTime: number;
        endTime: number;
      }>;
      location?: {
        latitude: number;
        longitude: number;
        name: string;
      };
      copyright?: {
        owner: string;
        license: string;
        allowReuse: boolean;
        allowCommercialUse: boolean;
        allowModification: boolean;
        attributionRequired: boolean;
      };
      thumbnailUrl?: string;
      languages?: Array<{
        code: string;
        name: string;
        flag?: string;
        url?: string;
        isDefault?: boolean;
      }>;
    }
  }>, reply: FastifyReply) => {
    try {
      // Get user ID from authenticated request
      const userId = request.user.id;
      let videoId = request.params.id;

      console.log(`Updating video with ID: ${videoId}`);

      // Check if the ID is a valid Engaxe ID (6-7 alphanumeric characters)
      const isValidEngaxeIdFormat = /^[a-zA-Z0-9]{6,7}$/.test(videoId);

      // If it's not a valid Engaxe ID, try to find the video by ID first
      if (!isValidEngaxeIdFormat) {
        console.log(`Video ID ${videoId} is not a valid Engaxe ID format, trying to find the video`);
        try {
          // Try to find the video by ID
          const existingVideo = await VideoModel.findOne({ id: videoId, deletedAt: null });
          if (existingVideo && existingVideo.url) {
            console.log(`Found video with ID ${videoId}, using its URL ${existingVideo.url} for the update`);
            videoId = existingVideo.url;
          } else {
            console.log(`Could not find video with ID ${videoId}, continuing with original ID`);
          }
        } catch (findError) {
          console.error(`Error finding video with ID ${videoId}:`, findError);
          // Continue with the original ID
        }
      }

      // Process languages if provided
      if (request.body.languages && request.body.languages.length > 0) {
        console.log(`Processing ${request.body.languages.length} languages for update:`);

        // Process each language to ensure URLs are valid
        request.body.languages = request.body.languages.map(lang => {
          const processedLang = { ...lang };

          // Ensure each language has a URL
          if (!processedLang.url) {
            console.log(`Language ${processedLang.name || processedLang.code} has no URL, using video ID: ${videoId}`);
            processedLang.url = videoId;
          }

          // Extract Engaxe ID from language URL if it's a full URL
          if (processedLang.url && (processedLang.url.includes('/') || processedLang.url.includes('.'))) {
            const extractedLangId = extractEngaxeVideoId(processedLang.url);
            if (extractedLangId) {
              console.log(`Extracted ID from language URL: ${extractedLangId} (original: ${processedLang.url})`);
              processedLang.url = extractedLangId;
            } else {
              console.warn(`Failed to extract Engaxe ID from language URL: ${processedLang.url}`);
              // Try a more aggressive extraction for URLs like https://engaxe.com/v/XjKFqQ
              const engaxeIdMatch = processedLang.url.match(/\/([a-zA-Z0-9]{6,7})(?:\/|$)/);
              if (engaxeIdMatch && engaxeIdMatch[1]) {
                console.log(`Extracted Engaxe ID using fallback method: ${engaxeIdMatch[1]} (original: ${processedLang.url})`);
                processedLang.url = engaxeIdMatch[1];
              }
            }
          }

          // Ensure language has a flag
          if (!processedLang.flag) {
            processedLang.flag = getLanguageFlag(processedLang.code);
            console.log(`Added flag ${processedLang.flag} for language ${processedLang.name || processedLang.code}`);
          }

          return processedLang;
        });

        // Check if we have at least one default language
        const hasDefaultLanguage = request.body.languages.some(lang => lang.isDefault);
        if (!hasDefaultLanguage && request.body.languages.length > 0) {
          console.log(`No default language found, setting ${request.body.languages[0].name || request.body.languages[0].code} as default`);
          request.body.languages[0].isDefault = true;
        }
      }

      // Update video
      const video = await this.videoService.updateVideo(userId, videoId, request.body);

      return reply.code(200).send({
        success: true,
        message: 'Video updated successfully',
        video: {
          id: video.id,
          title: video.title,
          description: video.description,
          thumbnailUrl: video.thumbnailUrl,
          url: video.url,
          duration: video.duration,
          userId: video.userId,
          channelId: video.channelId,
          visibility: video.visibility,
          category: video.category,
          tags: video.tags,
          contentRating: video.contentRating,
          commentsEnabled: video.commentsEnabled,
          ratingsEnabled: video.ratingsEnabled,
          embeddingEnabled: video.embeddingEnabled,
          processingStatus: video.processingStatus,
          languages: video.languages || [],
          createdAt: video.createdAt,
          updatedAt: video.updatedAt,
        },
      });
    } catch (error: any) {
      request.log.error(error);

      // Handle specific error types
      if (error.statusCode === 404) {
        return reply.code(404).send({
          success: false,
          message: error.message || 'Video or channel not found',
          error: {
            code: error.errorCode || 'NOT_FOUND',
            message: error.message,
          },
        });
      } else if (error.statusCode === 403) {
        return reply.code(403).send({
          success: false,
          message: error.message || 'Permission denied',
          error: {
            code: error.errorCode || 'FORBIDDEN',
            message: error.message,
          },
        });
      }

      return reply.code(400).send({
        success: false,
        message: error.message || 'Failed to update video',
        error: {
          code: error.errorCode || 'BAD_REQUEST',
          message: error.message,
        },
      });
    }
  };

  /**
   * Get a video by ID
   */
  getVideoById = async (request: FastifyRequest<{
    Params: {
      id: string;
    };
    Querystring: {
      view?: boolean;
    }
  }>, reply: FastifyReply) => {
    try {
      const videoId = request.params.id;
      const incrementViews = request.query.view === true;

      const videoWithChannel = await this.videoService.getVideoById(videoId, incrementViews);

      // Log the languages for debugging
      if (videoWithChannel.languages && videoWithChannel.languages.length > 0) {
        console.log(`Video ${videoId} has ${videoWithChannel.languages.length} languages:`);
        videoWithChannel.languages.forEach((lang, idx) => {
          console.log(`Language ${idx + 1}: ${lang.name} (${lang.code}), URL: ${lang.url || 'none'}, isDefault: ${lang.isDefault || false}, flag: ${lang.flag || 'none'}`);
        });
      } else {
        console.log(`Video ${videoId} has no languages`);
      }

      return reply.code(200).send({
        success: true,
        video: videoWithChannel,
      });
    } catch (error: any) {
      request.log.error(error);

      if (error.statusCode === 404) {
        return reply.code(404).send({
          success: false,
          message: error.message || 'Video not found',
          error: {
            code: error.errorCode || 'NOT_FOUND',
            message: error.message,
          },
        });
      }

      return reply.code(400).send({
        success: false,
        message: error.message || 'Failed to get video',
        error: {
          code: error.errorCode || 'BAD_REQUEST',
          message: error.message,
        },
      });
    }
  };

  /**
   * Get videos with pagination and filtering
   */
  getVideos = async (request: FastifyRequest<{
    Querystring: {
      page?: number | string;
      limit?: number | string;
      sort?: 'newest' | 'popular' | 'trending';
      category?: string;
      channelId?: string;
      userId?: string;
      search?: string;
      tags?: string[] | string;
      contentRating?: 'general' | 'teen' | 'mature' | 'explicit';
    }
  }>, reply: FastifyReply) => {
    // Convert string parameters to their proper types
    const queryParams = {
      ...request.query,
      page: request.query.page ? Number(request.query.page) : undefined,
      limit: request.query.limit ? Number(request.query.limit) : undefined,
      tags: typeof request.query.tags === 'string' ? [request.query.tags] : request.query.tags,
    };
    try {
      const { videos, pagination } = await this.videoService.getVideos(queryParams);

      return reply.code(200).send({
        success: true,
        videos: videos,
        pagination,
      });
    } catch (error: any) {
      request.log.error(error);

      return reply.code(400).send({
        success: false,
        message: error.message || 'Failed to get videos',
        error: {
          code: error.errorCode || 'BAD_REQUEST',
          message: error.message,
        },
      });
    }
  };

  /**
   * Get videos by channel
   */
  getChannelVideos = async (request: FastifyRequest<{
    Params: {
      channelId: string;
    };
    Querystring: {
      page?: number | string;
      limit?: number | string;
      sort?: 'newest' | 'popular' | 'oldest';
    }
  }>, reply: FastifyReply) => {
    // Convert string parameters to their proper types
    const queryParams = {
      ...request.query,
      page: request.query.page ? Number(request.query.page) : undefined,
      limit: request.query.limit ? Number(request.query.limit) : undefined,
    };
    try {
      const channelId = request.params.channelId;
      const { videos, pagination, channel } = await this.videoService.getChannelVideos(channelId, queryParams);

      return reply.code(200).send({
        success: true,
        videos: videos,
        pagination,
        channel,
      });
    } catch (error: any) {
      request.log.error(error);

      if (error.statusCode === 404) {
        return reply.code(404).send({
          success: false,
          message: error.message || 'Channel not found',
          error: {
            code: error.errorCode || 'NOT_FOUND',
            message: error.message,
          },
        });
      }

      return reply.code(400).send({
        success: false,
        message: error.message || 'Failed to get channel videos',
        error: {
          code: error.errorCode || 'BAD_REQUEST',
          message: error.message,
        },
      });
    }
  };

  /**
   * Get Engaxe video details
   */
  getEngaxeVideoDetails = async (request: FastifyRequest<{
    Querystring: {
      videoId: string;
    }
  }>, reply: FastifyReply) => {
    try {
      let { videoId } = request.query;

      // Validate inputs
      if (!videoId) {
        return reply.code(400).send({
          success: false,
          message: 'Video ID is required',
          error: {
            code: 'INVALID_INPUT',
            message: 'Video ID is required',
          },
        });
      }

      // Extract video ID if a full URL was provided
      const extractedId = extractEngaxeVideoId(videoId);

      if (extractedId) {
        console.log(`Extracted video ID from input: ${extractedId} (original: ${videoId})`);
        videoId = extractedId;
      } else {
        console.log(`Using original video ID: ${videoId}`);
      }

      // Fetch video details using AJAX endpoint
      const videoDetails = await this.videoService.getEngaxeVideoDetails(videoId);

      return reply.code(200).send({
        success: true,
        video: videoDetails,
      });
    } catch (error: any) {
      request.log.error(error);

      return reply.code(400).send({
        success: false,
        message: error.message || 'Failed to fetch Engaxe video details',
        error: {
          code: error.errorCode || 'BAD_REQUEST',
          message: error.message,
        },
      });
    }
  };

  /**
   * Get video metadata from Engaxe
   * This endpoint is used by the client to fetch video details for the language selector
   */
  getVideoMetadata = async (request: FastifyRequest<{
    Params: {
      videoId: string;
    }
  }>, reply: FastifyReply) => {
    try {
      let { videoId } = request.params;

      // Validate inputs
      if (!videoId) {
        return reply.code(400).send({
          success: false,
          message: 'Video ID is required',
          error: {
            code: 'INVALID_INPUT',
            message: 'Video ID is required',
          },
        });
      }

      // Extract video ID if a full URL was provided
      const extractedId = extractEngaxeVideoId(videoId);

      if (extractedId) {
        console.log(`Extracted video ID from input: ${extractedId} (original: ${videoId})`);
        videoId = extractedId;
      } else {
        console.log(`Using original video ID: ${videoId}`);
      }

      // Validate that the ID is a valid 6-7 character Engaxe ID
      const isValidEngaxeIdFormat = /^[a-zA-Z0-9]{6,7}$/.test(videoId);
      if (!isValidEngaxeIdFormat) {
        console.error(`Invalid Engaxe ID format: ${videoId}. Must be 6-7 alphanumeric characters.`);
        return reply.code(400).send({
          success: false,
          message: 'Invalid Engaxe ID format. Must be 6-7 alphanumeric characters.',
          error: {
            code: 'INVALID_INPUT',
            message: 'Invalid Engaxe ID format. Must be 6-7 alphanumeric characters.',
          },
        });
      }

      // Fetch video details using AJAX endpoint
      const videoDetails = await this.videoService.getEngaxeVideoDetails(videoId);

      return reply.code(200).send({
        success: true,
        status: 'success',
        data: {
          id: videoId,
          title: videoDetails.title || 'Untitled Video',
          description: videoDetails.description || '',
          duration: videoDetails.duration || 0,
          thumbnail: videoDetails.thumbnail || '',
          url: videoId
        }
      });
    } catch (error: any) {
      request.log.error(error);

      return reply.code(400).send({
        success: false,
        status: 'error',
        message: error.message || 'Failed to fetch video metadata',
        error: {
          code: error.errorCode || 'BAD_REQUEST',
          message: error.message,
        },
      });
    }
  };

  /**
   * Save Engaxe video to database
   */
  saveEngaxeVideo = async (request: AuthenticatedRequest<{
    Body: {
      videoId: string;
      channelId: string;
      metadata?: any;
      languages?: Array<{
        code: string;
        name: string;
        flag?: string;
        url?: string;
        isDefault?: boolean;
      }>;
    }
  }>, reply: FastifyReply) => {
    try {
      console.log('=== SAVE ENGAXE VIDEO DEBUG ===');
      console.log('Request body:', JSON.stringify(request.body, null, 2));
      console.log('Request headers:', JSON.stringify(request.headers, null, 2));

      // Get user ID from authenticated request
      const userId = request.user?.id;
      console.log('User ID from request:', userId);

      if (!userId) {
        console.error('No user ID found in request');
        return reply.code(401).send({
          success: false,
          message: 'Authentication required',
          error: {
            code: 'UNAUTHORIZED',
            message: 'You must be logged in to save videos',
          },
        });
      }

      let { videoId, channelId } = request.body;

      // Validate inputs
      if (!videoId) {
        return reply.code(400).send({
          success: false,
          message: 'Video ID is required',
          error: {
            code: 'INVALID_INPUT',
            message: 'Video ID is required',
          },
        });
      }

      if (!channelId) {
        return reply.code(400).send({
          success: false,
          message: 'Channel ID is required',
          error: {
            code: 'INVALID_INPUT',
            message: 'Channel ID is required',
          },
        });
      }

      // List of known valid Engaxe video IDs (only 6-7 character IDs)
      const validEngaxeIds = [
        'XLcMq2', 'xW36l7', 'suZKhW', 'wollzl', 'axHkJa', 'KxyzuN', '4OE4QR', 'gu99XD',
        'fgp97y', 'X4eW1I', 'c1AObf', '99BXqa', 'Hs7Qzd', '7mXOfb', 'dBVslb'
      ];

      // Extract the ID if it's a URL
      const extractedId = extractEngaxeVideoId(videoId);
      if (extractedId) {
        console.log(`Extracted video ID from input: ${extractedId} (original: ${videoId})`);
        videoId = extractedId;
      }

      // Debug languages if provided
      if (request.body.languages) {
        console.log(`Video has ${request.body.languages.length} languages:`);

        // Log each language in detail
        request.body.languages.forEach((lang, idx) => {
          console.log(`Language ${idx + 1}: ${lang.name} (${lang.code}), URL: ${lang.url || 'none'}, isDefault: ${lang.isDefault || false}, flag: ${lang.flag || 'none'}`);
        });

        // Check if we have at least one default language
        const hasDefaultLanguage = request.body.languages.some(lang => lang.isDefault);

        request.body.languages.forEach((lang, idx) => {
          console.log(`Language ${idx + 1}: ${lang.name} (${lang.code}), URL: ${lang.url || 'none'}, isDefault: ${lang.isDefault || false}`);

          // Ensure each language has a URL (use videoId if missing)
          if (!lang.url) {
            console.log(`Setting missing URL for language ${lang.name} to video ID: ${videoId}`);
            lang.url = videoId;
          }

          // Extract Engaxe ID from language URL if it's a full URL
          if (lang.url && (lang.url.includes('/') || lang.url.includes('.'))) {
            const extractedLangId = extractEngaxeVideoId(lang.url);
            if (extractedLangId) {
              console.log(`Extracted ID from language URL: ${extractedLangId} (original: ${lang.url})`);
              lang.url = extractedLangId;
            } else {
              console.warn(`Failed to extract Engaxe ID from language URL: ${lang.url}`);
              // Try a more aggressive extraction for URLs like https://engaxe.com/v/XjKFqQ
              const engaxeIdMatch = lang.url.match(/\/([a-zA-Z0-9]{6,7})(?:\/|$)/);
              if (engaxeIdMatch && engaxeIdMatch[1]) {
                console.log(`Extracted Engaxe ID using fallback method: ${engaxeIdMatch[1]} (original: ${lang.url})`);
                lang.url = engaxeIdMatch[1];
              }
            }
          }

          // Ensure language has a flag
          if (!lang.flag) {
            // Add flag based on language code
            switch (lang.code) {
              case 'en': lang.flag = '🇺🇸'; break;
              case 'hi': lang.flag = '🇮🇳'; break;
              case 'es': lang.flag = '🇪🇸'; break;
              case 'fr': lang.flag = '🇫🇷'; break;
              case 'de': lang.flag = '🇩🇪'; break;
              case 'ja': lang.flag = '🇯🇵'; break;
              case 'zh': lang.flag = '🇨🇳'; break;
              case 'ru': lang.flag = '🇷🇺'; break;
              case 'ar': lang.flag = '🇸🇦'; break;
              case 'pt': lang.flag = '🇵🇹'; break;
              case 'it': lang.flag = '🇮🇹'; break;
              case 'nl': lang.flag = '🇳🇱'; break;
              case 'ko': lang.flag = '🇰🇷'; break;
              case 'tr': lang.flag = '🇹🇷'; break;
              default: lang.flag = '🌐'; break;
            }
            console.log(`Added flag ${lang.flag} for language ${lang.name}`);
          }

          // Set isDefault if not already set and no default language exists
          if (typeof lang.isDefault === 'undefined') {
            // If no default language yet, make the first one default
            // or make English the default if it exists
            if (!hasDefaultLanguage && (idx === 0 || lang.code === 'en')) {
              console.log(`Setting language ${lang.name} as default`);
              lang.isDefault = true;
            } else {
              lang.isDefault = false;
            }
          }
        });

        // If no default language was set, make the first one default
        if (!hasDefaultLanguage && request.body.languages.length > 0) {
          console.log(`No default language found, setting ${request.body.languages[0].name} as default`);
          request.body.languages[0].isDefault = true;
        }
      } else {
        console.log('No languages provided, will create default English language with video ID as URL');
        request.body.languages = [{
          code: 'en',
          name: 'English',
          flag: '🇺🇸',
          isDefault: true,
          url: videoId
        }];
      }

      // Store the original ID for reference
      const originalId = videoId;

      // Check if the ID is a valid 6-7 character Engaxe ID
      const isValidEngaxeIdFormat = /^[a-zA-Z0-9]{6,7}$/.test(videoId);
      if (!isValidEngaxeIdFormat) {
        console.error(`Invalid Engaxe ID format: ${videoId}. Must be 6-7 alphanumeric characters.`);
        return reply.code(400).send({
          success: false,
          message: 'Invalid Engaxe ID format. Must be 6-7 alphanumeric characters.',
          error: {
            code: 'INVALID_INPUT',
            message: 'Invalid Engaxe ID format. Must be 6-7 alphanumeric characters.',
          },
        });
      }

      // Check if the provided ID is already a valid Engaxe ID
      let isValidEngaxeId = validEngaxeIds.includes(videoId);

      // If not in our list, add it to the list of valid IDs
      if (!isValidEngaxeId) {
        console.log(`Adding new ID to valid Engaxe IDs list: ${videoId}`);
        validEngaxeIds.push(videoId);
        isValidEngaxeId = true;
      }

      console.log(`Using Engaxe ID: ${videoId}`);

      // Fetch video details using AJAX endpoint - we'll use the valid ID
      const videoDetails = await this.videoService.getEngaxeVideoDetails(videoId);

      // Save video to database
      console.log(`Calling saveEngaxeVideoToDatabase with userId=${userId}, channelId=${channelId}, videoId=${videoId}`);

      // Log languages in detail before saving
      if (request.body.languages && request.body.languages.length > 0) {
        console.log(`Request contains ${request.body.languages.length} languages:`);
        request.body.languages.forEach((lang, idx) => {
          console.log(`Language ${idx + 1}: ${lang.name} (${lang.code}), URL: ${lang.url || 'none'}, isDefault: ${lang.isDefault || false}, flag: ${lang.flag || 'none'}`);
        });

        // Validate languages before saving
        request.body.languages = request.body.languages.map(lang => {
          // Ensure each language has required properties
          const processedLang = {
            code: lang.code || 'en',
            name: lang.name || 'Unknown',
            flag: lang.flag || getLanguageFlag(lang.code || 'en'),
            url: lang.url || videoId,
            isDefault: !!lang.isDefault
          };

          // Validate URL is a valid Engaxe ID
          if (!/^[a-zA-Z0-9]{6,7}$/.test(processedLang.url)) {
            console.warn(`Language ${processedLang.name} has invalid URL format: ${processedLang.url}. Using video ID instead.`);
            processedLang.url = videoId;
          }

          return processedLang;
        });

        // Ensure at least one language is marked as default
        const hasDefaultLanguage = request.body.languages.some(lang => lang.isDefault);
        if (!hasDefaultLanguage && request.body.languages.length > 0) {
          console.log(`No default language found, setting ${request.body.languages[0].name} as default`);
          request.body.languages[0].isDefault = true;
        }

        console.log(`Processed ${request.body.languages.length} languages for saving:`);
        request.body.languages.forEach((lang, idx) => {
          console.log(`Processed language ${idx + 1}: ${lang.name} (${lang.code}), URL: ${lang.url}, isDefault: ${lang.isDefault}, flag: ${lang.flag}`);
        });
      } else {
        console.log('No languages in request body, creating default English language');
        // Create a default English language
        request.body.languages = [{
          code: 'en',
          name: 'English',
          flag: '🇺🇸',
          url: videoId,
          isDefault: true
        }];
      }

      const video = await this.videoService.saveEngaxeVideoToDatabase(
        userId,
        channelId,
        videoId,
        videoDetails,
        request.body.languages
      );

      console.log('Video saved successfully:', {
        id: video.id,
        title: video.title,
        url: video.url,
        channelId: video.channelId
      });

      // Check if the video was actually saved to the database
      try {
        const savedVideo = await VideoModel.findOne({ id: video.id });
        if (savedVideo) {
          console.log('Verified: Video exists in database with ID:', video.id);
        } else {
          console.error('ERROR: Video not found in database after save!');
        }
      } catch (verifyError) {
        console.error('Error verifying video in database:', verifyError);
      }

      return reply.code(201).send({
        success: true,
        message: 'Engaxe video saved to database successfully',
        video: {
          id: video.id,
          title: video.title,
          description: video.description,
          thumbnailUrl: video.thumbnailUrl,
          url: video.url,
          duration: video.duration,
          userId: video.userId,
          channelId: video.channelId,
          visibility: video.visibility,
          processingStatus: video.processingStatus,
          source: video.source,
          languages: video.languages || [],
          createdAt: video.createdAt,
        },
      });
    } catch (error: any) {
      request.log.error(error);

      // Handle specific error types
      if (error.statusCode === 404) {
        return reply.code(404).send({
          success: false,
          message: error.message || 'Channel not found',
          error: {
            code: error.errorCode || 'NOT_FOUND',
            message: error.message,
          },
        });
      } else if (error.statusCode === 403) {
        return reply.code(403).send({
          success: false,
          message: error.message || 'Permission denied',
          error: {
            code: error.errorCode || 'FORBIDDEN',
            message: error.message,
          },
        });
      }

      return reply.code(400).send({
        success: false,
        message: error.message || 'Failed to save Engaxe video',
        error: {
          code: error.errorCode || 'BAD_REQUEST',
          message: error.message,
        },
      });
    }
  };



  /**
   * Get a video by URL
   * CRITICAL FIX: This method allows the client to get a video by its URL field
   * instead of its internal database ID
   */
  getVideoByUrl = async (request: FastifyRequest<{
    Params: {
      url: string;
    };
    Querystring: {
      view?: boolean;
    }
  }>, reply: FastifyReply) => {
    try {
      const videoUrl = request.params.url;
      const incrementViews = request.query.view === true;

      console.log(`Looking up video by URL: ${videoUrl}`);

      // Find the video by URL
      const video = await VideoModel.findOne({ url: videoUrl, deletedAt: null });

      if (!video) {
        console.log(`No video found with URL: ${videoUrl}`);
        return reply.code(404).send({
          success: false,
          message: 'Video not found',
          error: {
            code: 'VIDEO_NOT_FOUND',
            message: 'Video not found',
          },
        });
      }

      console.log(`Found video with URL ${videoUrl}: ${video.id} - ${video.title}`);

      // Use the existing getVideoById method to get the full video with channel
      const videoWithChannel = await this.videoService.getVideoById(video.id, incrementViews);

      // Log the languages for debugging
      if (videoWithChannel.languages && videoWithChannel.languages.length > 0) {
        console.log(`Video with URL ${videoUrl} has ${videoWithChannel.languages.length} languages:`);
        videoWithChannel.languages.forEach((lang, idx) => {
          console.log(`Language ${idx + 1}: ${lang.name} (${lang.code}), URL: ${lang.url || 'none'}, isDefault: ${lang.isDefault || false}, flag: ${lang.flag || 'none'}`);
        });
      } else {
        console.log(`Video with URL ${videoUrl} has no languages`);
      }

      return reply.code(200).send({
        success: true,
        video: videoWithChannel,
      });
    } catch (error: any) {
      request.log.error(error);

      if (error.statusCode === 404) {
        return reply.code(404).send({
          success: false,
          message: error.message || 'Video not found',
          error: {
            code: error.errorCode || 'NOT_FOUND',
            message: error.message,
          },
        });
      }

      return reply.code(400).send({
        success: false,
        message: error.message || 'Failed to get video',
        error: {
          code: error.errorCode || 'BAD_REQUEST',
          message: error.message,
        },
      });
    }
  };

  /**
   * Fix all video URLs in the database
   * CRITICAL FIX: This method ensures that all videos have valid 6-7 character Engaxe IDs
   * and that the ID and URL fields are consistent
   */
  fixVideoUrls = async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      // Only allow admins to run this operation
      const user = (request as any).user;
      if (!user || !user.roles || !user.roles.includes('admin')) {
        return reply.code(403).send({
          success: false,
          message: 'Only admins can fix video URLs',
          error: {
            code: 'FORBIDDEN',
            message: 'Only admins can fix video URLs',
          },
        });
      }

      // Get all videos
      const videos = await VideoModel.find({ deletedAt: null });
      console.log(`Found ${videos.length} videos in the database`);

      let fixedCount = 0;

      // Fix each video
      for (const video of videos) {
        let needsUpdate = false;
        let newEngaxeId = '';

        // Case 1: ID is already a valid Engaxe ID
        if (isValidEngaxeId(video.id)) {
          console.log(`Video ID ${video.id} is already a valid Engaxe ID`);
          newEngaxeId = video.id;

          // Ensure URL matches ID
          if (video.url !== video.id) {
            console.log(`Video URL ${video.url} doesn't match ID ${video.id}, updating URL`);
            needsUpdate = true;
          }
        }
        // Case 2: ID is not a valid Engaxe ID (legacy hash ID)
        else {
          console.log(`Video ID ${video.id} is not a valid Engaxe ID (legacy hash ID)`);

          // Check if URL is a valid Engaxe ID
          if (isValidEngaxeId(video.url)) {
            console.log(`Using existing valid URL ${video.url} as the new Engaxe ID`);
            newEngaxeId = video.url;
            needsUpdate = true;
          }
          // Neither ID nor URL is a valid Engaxe ID, generate a new one
          else {
            newEngaxeId = generateEngaxeId();
            console.log(`Generated new Engaxe ID: ${newEngaxeId}`);
            needsUpdate = true;
          }

          // Create a mapping for backward compatibility
          await videoIdMappingService.createMapping(video.id, newEngaxeId, video.title || 'Untitled Video');
          console.log(`Created mapping for legacy ID ${video.id} -> ${newEngaxeId}`);
        }

        if (needsUpdate) {
          // Update the video URL to match the Engaxe ID
          video.url = newEngaxeId;

          // If the ID is not a valid Engaxe ID, update it too (for new videos)
          if (!isValidEngaxeId(video.id)) {
            // For new videos, we'll update the ID to be the Engaxe ID
            // This is a breaking change, but it's the right approach going forward
            // We've created a mapping for backward compatibility
            console.log(`Updating video ID from ${video.id} to ${newEngaxeId}`);
            video.id = newEngaxeId;
          }

          // Also update the URL in all languages
          if (video.languages && video.languages.length > 0) {
            for (const lang of video.languages) {
              console.log(`Setting language ${lang.code} URL to video's Engaxe ID: ${newEngaxeId}`);
              lang.url = newEngaxeId;
            }
          }

          // Update the source.originalUrl and source.externalId if they exist
          if (video.source) {
            if (video.source.originalUrl) {
              console.log(`Setting source.originalUrl to video's Engaxe ID: ${newEngaxeId}`);
              video.source.originalUrl = newEngaxeId;
            }
            if (video.source.externalId) {
              console.log(`Setting source.externalId to video's Engaxe ID: ${newEngaxeId}`);
              video.source.externalId = newEngaxeId;
            }
          }

          // Save the video
          await video.save();
          console.log(`Successfully updated video ${video.id}`);
          fixedCount++;
        }
      }

      // Verify that all videos now have valid Engaxe IDs
      const verifiedVideos = await VideoModel.find({ deletedAt: null });
      let validCount = 0;
      for (const video of verifiedVideos) {
        if (isValidEngaxeId(video.url)) {
          validCount++;
        } else {
          console.error(`Video ${video.id} still has an invalid URL: ${video.url}`);
        }
      }

      console.log(`Verification result: ${validCount} out of ${verifiedVideos.length} videos have valid Engaxe IDs`);

      // Return the result
      return reply.code(200).send({
        success: true,
        message: `Fixed ${fixedCount} out of ${videos.length} videos`,
        fixedCount,
        totalCount: videos.length,
      });
    } catch (error: any) {
      request.log.error(error);
      return reply.code(500).send({
        success: false,
        message: error.message || 'Failed to fix video URLs',
        error: {
          code: error.errorCode || 'INTERNAL_SERVER_ERROR',
          message: error.message,
        },
      });
    }
  };

  /**
   * Update languages for all existing videos
   * This endpoint will add default languages to videos that don't have any
   * and ensure all language URLs are valid Engaxe IDs
   */
  updateAllVideoLanguages = async (request: AuthenticatedRequest, reply: FastifyReply) => {
    try {
      // Get user ID from authenticated request
      const userId = request.user.id;

      // Check if user is admin (in a real app, you would have proper role checks)
      // For now, we'll allow any authenticated user to run this
      console.log(`User ${userId} is updating all video languages`);

      // Get all videos
      const videos = await VideoModel.find({ deletedAt: null });
      console.log(`Found ${videos.length} videos to update`);

      let updatedCount = 0;
      let skippedCount = 0;
      let errorCount = 0;

      // Update each video
      for (const video of videos) {
        try {
          let needsUpdate = false;

          // Ensure video has a valid URL
          if (video.url && !isValidEngaxeId(video.url)) {
            const oldUrl = video.url;
            video.url = await hashIdToEngaxeId(video.url);
            console.log(`Updated video URL from ${oldUrl} to ${video.url}`);
            needsUpdate = true;
          }

          // Ensure video has languages
          if (!video.languages || video.languages.length === 0) {
            console.log(`Video ${video.id} has no languages, adding default English language`);
            video.languages = [{
              code: 'en',
              name: 'English',
              flag: '🇺🇸',
              isDefault: true,
              url: video.url || video.id
            }];
            needsUpdate = true;
          } else {
            // Process each language
            let languagesUpdated = false;

            // Process languages one by one to handle async operations
            for (let i = 0; i < video.languages.length; i++) {
              const lang = video.languages[i];
              const processedLang = { ...lang };

              // Ensure language has a URL
              if (!processedLang.url) {
                console.log(`Language ${processedLang.name} for video ${video.id} has no URL, using video URL: ${video.url}`);
                processedLang.url = video.url;
                languagesUpdated = true;
              }
              // Ensure URL is a valid Engaxe ID
              else if (!isValidEngaxeId(processedLang.url)) {
                const oldUrl = processedLang.url;
                processedLang.url = await hashIdToEngaxeId(processedLang.url);
                console.log(`Updated language ${processedLang.name} URL from ${oldUrl} to ${processedLang.url} for video ${video.id}`);
                languagesUpdated = true;
              }

              // Ensure language has a flag
              if (!processedLang.flag) {
                processedLang.flag = getLanguageFlag(processedLang.code);
                console.log(`Added flag ${processedLang.flag} for language ${processedLang.name} (${processedLang.code}) for video ${video.id}`);
                languagesUpdated = true;
              }

              // Update the language in the array
              video.languages[i] = processedLang;
            }

            if (languagesUpdated) {
              needsUpdate = true;
            }

            // Check if we have at least one default language
            const hasDefaultLanguage = video.languages.some(lang => lang.isDefault);
            if (!hasDefaultLanguage && video.languages.length > 0) {
              console.log(`No default language found for video ${video.id}, setting ${video.languages[0].name} as default`);
              video.languages[0].isDefault = true;
              needsUpdate = true;
            }
          }

          // Save the video if it needs updating
          if (needsUpdate) {
            await video.save();
            updatedCount++;
            console.log(`Updated video ${video.id}`);
          } else {
            skippedCount++;
            console.log(`Video ${video.id} already has valid languages, skipping`);
          }
        } catch (videoError) {
          errorCount++;
          console.error(`Error updating video ${video.id}:`, videoError);
        }
      }

      return reply.code(200).send({
        success: true,
        message: `Language update complete. Updated ${updatedCount} videos, skipped ${skippedCount} videos, encountered ${errorCount} errors.`,
        stats: {
          total: videos.length,
          updated: updatedCount,
          skipped: skippedCount,
          errors: errorCount
        }
      });
    } catch (error) {
      console.error('Error updating video languages:', error);
      return handleControllerError(error, reply);
    }
  };

  /**
   * Delete a video
   */
  deleteVideo = async (request: AuthenticatedRequest<{
    Params: {
      id: string;
    }
  }>, reply: FastifyReply) => {
    try {
      // Get user ID from authenticated request
      const userId = request.user.id;
      const videoId = request.params.id;

      // Delete video
      await this.videoService.deleteVideo(userId, videoId);

      return reply.code(200).send({
        success: true,
        message: 'Video deleted successfully',
      });
    } catch (error: any) {
      request.log.error(error);

      // Handle specific error types
      if (error.statusCode === 404) {
        return reply.code(404).send({
          success: false,
          message: error.message || 'Video or channel not found',
          error: {
            code: error.errorCode || 'NOT_FOUND',
            message: error.message,
          },
        });
      } else if (error.statusCode === 403) {
        return reply.code(403).send({
          success: false,
          message: error.message || 'Permission denied',
          error: {
            code: error.errorCode || 'FORBIDDEN',
            message: error.message,
          },
        });
      }

      return reply.code(400).send({
        success: false,
        message: error.message || 'Failed to delete video',
        error: {
          code: error.errorCode || 'BAD_REQUEST',
          message: error.message,
        },
      });
    }
  };

  /**
   * Delete all videos from the database
   * This is an admin-only endpoint for development purposes
   */
  deleteAllVideos = async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      // Only allow admins to run this operation
      const user = (request as any).user;
      if (!user || !user.roles || !user.roles.includes('admin')) {
        return reply.code(403).send({
          success: false,
          message: 'Only admins can delete all videos',
          error: {
            code: 'FORBIDDEN',
            message: 'Only admins can delete all videos',
          },
        });
      }

      // Get the count of videos before deletion
      const count = await VideoModel.countDocuments({ deletedAt: null });
      console.log(`Found ${count} videos to delete`);

      // Delete all videos (soft delete by setting deletedAt)
      const result = await VideoModel.updateMany(
        { deletedAt: null },
        {
          deletedAt: new Date(),
          updatedAt: new Date(),
          updatedBy: user.id
        }
      );

      console.log(`Deleted ${result.modifiedCount} videos`);

      return reply.code(200).send({
        success: true,
        message: `Deleted ${result.modifiedCount} videos`,
        count: result.modifiedCount,
      });
    } catch (error: any) {
      request.log.error(error);
      return reply.code(500).send({
        success: false,
        message: error.message || 'Failed to delete all videos',
        error: {
          code: error.errorCode || 'INTERNAL_SERVER_ERROR',
          message: error.message,
        },
      });
    }
  };

  /**
   * Create mappings for all videos
   * This is an admin-only endpoint to ensure all videos have mappings
   */
  createAllMappings = async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      // Only allow admins to run this operation
      const user = (request as any).user;
      if (!user || !user.roles || !user.roles.includes('admin')) {
        return reply.code(403).send({
          success: false,
          message: 'Only admins can create mappings',
          error: {
            code: 'FORBIDDEN',
            message: 'Only admins can create mappings',
          },
        });
      }

      // Create mappings for all videos
      const result = await this.videoService.ensureAllVideosMapped();
      console.log(`Created mappings for ${result.createdCount} videos, ${result.existingCount} already had mappings`);

      return reply.code(200).send({
        success: true,
        message: `Created mappings for ${result.createdCount} videos, ${result.existingCount} already had mappings`,
        data: result,
      });
    } catch (error: any) {
      request.log.error(error);
      return reply.code(500).send({
        success: false,
        message: error.message || 'Failed to create mappings',
        error: {
          code: error.errorCode || 'INTERNAL_SERVER_ERROR',
          message: error.message,
        },
      });
    }
  };
}

import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { useTheme } from '@/context/ThemeContext';
import { Bell, Search, User, Settings, LogOut, Moon, Sun } from 'lucide-react';

export default function AdminNavbar() {
  const { currentUser, logout } = useAuth();
  const { theme, toggleTheme } = useTheme();
  const [isProfileOpen, setIsProfileOpen] = useState(false);

  const toggleProfile = () => {
    setIsProfileOpen(!isProfileOpen);
  };

  return (
    <div className={`w-full ${theme === 'dark' ? 'bg-[#1A1A1A] text-white' : 'bg-white text-gray-800'} shadow-sm`}>
      <div className="container mx-auto px-4 py-3">
        <div className="flex justify-between items-center">
          {/* Logo - Only visible on mobile when sidebar is hidden */}
          <div className="md:hidden">
            <Link to="/" className="flex items-center">
              <span className={`${theme === 'dark' ? 'text-white' : 'text-gray-900'} text-2xl font-bold`}>
                <span className="text-[#FF5722]">Legal</span>
                <span className={theme === 'dark' ? 'text-white' : 'text-gray-900'}>Aid</span>
              </span>
            </Link>
          </div>

          {/* Search Bar */}
          <div className="flex-1 max-w-xl mx-4 hidden md:block">
            <div className="relative">
              <input
                type="text"
                placeholder="Search..."
                className={`w-full py-2 pl-10 pr-4 rounded-full ${
                  theme === 'dark' ? 'bg-gray-800 text-gray-200' : 'bg-gray-100 text-gray-800'
                } focus:outline-none`}
              />
              <div className="absolute left-3 top-2.5">
                <Search size={18} className={theme === 'dark' ? 'text-gray-400' : 'text-gray-500'} />
              </div>
            </div>
          </div>

          {/* Right Side Icons */}
          <div className="flex items-center space-x-4">
            {/* Theme Toggle */}
            <button
              onClick={toggleTheme}
              className={`p-2 rounded-full ${
                theme === 'dark' ? 'hover:bg-gray-800' : 'hover:bg-gray-200'
              } transition-colors`}
            >
              {theme === 'dark' ? (
                <Sun size={20} className="text-gray-300" />
              ) : (
                <Moon size={20} className="text-gray-600" />
              )}
            </button>

            {/* Notifications */}
            <button
              className={`p-2 rounded-full ${
                theme === 'dark' ? 'hover:bg-gray-800' : 'hover:bg-gray-200'
              } transition-colors relative`}
            >
              <Bell size={20} className={theme === 'dark' ? 'text-gray-300' : 'text-gray-600'} />
              <span className="absolute top-0 right-0 h-4 w-4 bg-red-500 rounded-full text-xs text-white flex items-center justify-center">
                3
              </span>
            </button>

            {/* Profile Dropdown */}
            <div className="relative">
              <button
                onClick={toggleProfile}
                className="flex items-center space-x-2 focus:outline-none"
              >
                <div className="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center overflow-hidden">
                  {currentUser?.photoURL ? (
                    <img src={currentUser.photoURL} alt="Profile" className="h-full w-full object-cover" />
                  ) : (
                    <User size={20} className="text-gray-600" />
                  )}
                </div>
                <span className={`hidden md:block ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}`}>
                  {currentUser?.displayName || 'Admin User'}
                </span>
              </button>

              {/* Dropdown Menu */}
              {isProfileOpen && (
                <div
                  className={`absolute right-0 mt-2 w-48 rounded-md shadow-lg py-1 z-10 ${
                    theme === 'dark' ? 'bg-gray-800' : 'bg-white'
                  } ring-1 ring-black ring-opacity-5`}
                >
                  <Link
                    to="/profile"
                    className={`block px-4 py-2 text-sm ${
                      theme === 'dark' ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    <div className="flex items-center">
                      <User size={16} className="mr-2" />
                      Your Profile
                    </div>
                  </Link>
                  <Link
                    to="/settings"
                    className={`block px-4 py-2 text-sm ${
                      theme === 'dark' ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    <div className="flex items-center">
                      <Settings size={16} className="mr-2" />
                      Settings
                    </div>
                  </Link>
                  <button
                    onClick={logout}
                    className={`block w-full text-left px-4 py-2 text-sm ${
                      theme === 'dark' ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    <div className="flex items-center">
                      <LogOut size={16} className="mr-2" />
                      Sign out
                    </div>
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

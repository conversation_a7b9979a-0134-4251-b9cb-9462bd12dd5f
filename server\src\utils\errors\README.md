# Error Handling System

This directory contains the centralized error handling system for the LawEngaxe server.

## Components

### AppError

`AppError` is a custom error class that extends the built-in `Error` class with additional properties:

- `statusCode`: HTTP status code for the error
- `errorCode`: Error code for client-side error handling
- `isOperational`: Whether this is an operational error (expected error)
- `data`: Additional data to include in the error response

It also includes factory functions for creating common error types:

- `createBadRequestError`: 400 Bad Request
- `createUnauthorizedError`: 401 Unauthorized
- `createForbiddenError`: 403 Forbidden
- `createNotFoundError`: 404 Not Found
- `createConflictError`: 409 Conflict
- `createValidationError`: 422 Validation Error
- `createRateLimitError`: 429 Too Many Requests
- `createInternalServerError`: 500 Internal Server Error
- `createServiceUnavailableError`: 503 Service Unavailable

### Error Codes

`errorCodes.ts` contains a list of error codes for client-side error handling. These codes are used to identify specific error types and provide more context to the client.

### Error Handler

`errorHandler.ts` contains the central error handler for the application. It handles different types of errors and formats them consistently.

### Error Logger

`errorLogger.ts` contains a utility for logging errors to a file and/or console. It sanitizes sensitive information and formats the logs for easier debugging.

### Database Error Handler

`databaseErrorHandler.ts` contains utilities for handling database-specific errors, such as validation errors, duplicate key errors, and cast errors.

## Usage

### Creating Errors

```typescript
import { createNotFoundError, ErrorCodes } from '../utils/errors';

// Create a not found error
throw createNotFoundError('User not found', ErrorCodes.USER_NOT_FOUND);
```

### Handling Errors

The error handler is registered as a Fastify plugin and will automatically handle errors thrown in route handlers.

```typescript
// In a route handler
async function getUser(request, reply) {
  const user = await UserModel.findById(request.params.id);
  
  if (!user) {
    throw createNotFoundError('User not found', ErrorCodes.USER_NOT_FOUND);
  }
  
  return user;
}
```

### Logging Errors

```typescript
import { errorLogger } from '../utils/errors';

try {
  // Some code that might throw an error
} catch (error) {
  errorLogger.logError(error, request);
  throw error; // Rethrow to let the error handler handle it
}
```

## Error Response Format

All errors are formatted consistently in the response:

```json
{
  "success": false,
  "error": {
    "message": "Error message",
    "code": "ERROR_CODE",
    "data": {
      // Additional error data (optional)
    }
  }
}
```

## Best Practices

1. Always use the `AppError` class or its factory functions for throwing errors
2. Always include an error code from `errorCodes.ts`
3. Include additional data when it helps the client understand the error
4. Use operational errors for expected errors (e.g., validation errors)
5. Use non-operational errors for unexpected errors (e.g., database connection errors)
6. Log all errors, but be careful not to log sensitive information

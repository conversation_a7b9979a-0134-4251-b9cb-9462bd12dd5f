<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Video URLs</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #333;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 0;
        }
        button:hover {
            background-color: #45a049;
        }
        #result {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
            white-space: pre-wrap;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(0,0,0,.3);
            border-radius: 50%;
            border-top-color: #000;
            animation: spin 1s ease-in-out infinite;
            margin-left: 10px;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <h1>Fix Video URLs in Database</h1>
    <p>This tool will fix all video URLs in the database to ensure they play properly. It will:</p>
    <ul>
        <li>Convert full Engaxe URLs to just the video ID</li>
        <li>Add missing language URLs</li>
        <li>Ensure all videos have proper language settings</li>
    </ul>
    
    <button id="fixBtn">Fix All Videos</button>
    <span id="loading" class="loading" style="display: none;"></span>
    
    <div id="result"></div>
    
    <script>
        document.getElementById('fixBtn').addEventListener('click', async () => {
            const resultDiv = document.getElementById('result');
            const loadingSpinner = document.getElementById('loading');
            
            try {
                resultDiv.textContent = 'Starting migration...';
                loadingSpinner.style.display = 'inline-block';
                
                // Make the API call to fix videos
                const response = await fetch('http://localhost:3003/api/v1/videos/fix-urls', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer ' + localStorage.getItem('token')
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.textContent = `Migration completed successfully!\n\nFixed ${data.data.fixedCount} out of ${data.data.totalCount} videos.\n\nDetails: ${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.textContent = `Error: ${data.message || 'Unknown error'}\n\nDetails: ${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.textContent = `Error: ${error.message}\n\nMake sure the server is running and you're logged in as an admin.`;
            } finally {
                loadingSpinner.style.display = 'none';
            }
        });
        
        // Check if user is logged in
        window.addEventListener('DOMContentLoaded', () => {
            const token = localStorage.getItem('token');
            if (!token) {
                document.getElementById('result').textContent = 'You need to be logged in as an admin to use this tool. Please log in first.';
                document.getElementById('fixBtn').disabled = true;
            }
        });
    </script>
</body>
</html>

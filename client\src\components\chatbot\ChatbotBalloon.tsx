import { useState, useEffect, useRef } from 'react';
import { MessageCircle, X, Minimize2, Maximize2, Phone, PhoneOff, Mic, MicOff, Volume2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardFooter, CardHeader } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useChatbot } from '@/context/ChatbotContext';
import { useAuth } from '@/context/AuthContext';
import VoiceCallChat from './VoiceCallChat';

interface ChatbotBalloonProps {
  isOpen?: boolean;
  onClose?: () => void;
}

export default function ChatbotBalloon({ isOpen = false, onClose }: ChatbotBalloonProps) {
  const { isVoiceCallingEnabled, voiceCallingApiKey } = useChatbot();
  const { currentUser } = useAuth();
  const [isExpanded, setIsExpanded] = useState(isOpen);
  const [message, setMessage] = useState('');
  const [chatHistory, setChatHistory] = useState<{ sender: 'user' | 'bot'; text: string }[]>([
    { sender: 'bot', text: 'Hello! I can help you understand this video. What would you like to know?' },
  ]);

  // Show voice call interface instead of regular chatbot
  const [showVoiceCall, setShowVoiceCall] = useState(false);

  // Add message about voice calling status only when it's specifically enabled
  useEffect(() => {
    if (isVoiceCallingEnabled) {
      setChatHistory(prev => [
        ...prev,
        { sender: 'bot', text: 'Voice calling is available! Click the phone button to start a voice conversation.' }
      ]);
    }
  }, [isVoiceCallingEnabled]);

  // Voice calling states
  const [isCallActive, setIsCallActive] = useState(false);
  const [isRinging, setIsRinging] = useState(false);

  useEffect(() => {
    setIsExpanded(isOpen);
  }, [isOpen]);

  const handleSendMessage = () => {
    if (!message.trim()) return;

    // Add user message to chat
    setChatHistory([...chatHistory, { sender: 'user', text: message }]);

    // Clear input
    setMessage('');

    // Simulate bot response (in a real app, this would call your API)
    setTimeout(() => {
      setChatHistory(prev => [
        ...prev,
        {
          sender: 'bot',
          text: `I understand you're asking about "${message}". This is a simulated response since we're just demonstrating the UI.`
        }
      ]);
    }, 1000);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSendMessage();
    }
  };

  // Voice calling functions
  const startCall = async () => {
    // Switch to voice call interface
    setShowVoiceCall(true);
  };

  const endCall = () => {
    setShowVoiceCall(false);
    setIsCallActive(false);
  };

  if (!isExpanded) {
    return (
      <div className="fixed bottom-6 right-6 z-50">
        <Button
          className="h-14 w-14 rounded-full bg-primary shadow-lg hover:bg-primary/90 text-white"
          onClick={() => setIsExpanded(true)}
        >
          <MessageCircle className="h-6 w-6" />
        </Button>
      </div>
    );
  }

  // If voice call is active, show the voice call interface
  if (showVoiceCall) {
    return <VoiceCallChat isOpen={isExpanded} onClose={() => setShowVoiceCall(false)} />;
  }

  return (
    <div className="fixed bottom-6 right-6 z-50">
      <Card className="w-80 h-96 shadow-xl flex flex-col">
        <CardHeader className="p-3 border-b">
          {/* WhatsApp-style header with call button */}
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <Avatar className="h-8 w-8">
                <AvatarImage src="/creator-avatar.png" alt="Creator" />
                <AvatarFallback>👤</AvatarFallback>
              </Avatar>
              <div>
                <span className="font-medium">AI Assistant</span>
                <div className="text-xs text-muted-foreground">Online</div>
              </div>
            </div>

            <div className="flex items-center gap-2">
              {/* Voice calling button in header - only show when voice calling is specifically enabled */}
              {isVoiceCallingEnabled && (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={startCall}
                  title="Start Voice Call"
                  className="text-green-500 hover:text-green-600 hover:bg-green-100/10"
                >
                  <Phone className="h-5 w-5" />
                </Button>
              )}

              <Button variant="ghost" size="icon" onClick={() => setIsExpanded(false)}>
                <Minimize2 className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="icon" onClick={onClose}>
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="flex-1 overflow-y-auto p-3 space-y-4">
          {/* Normal chat history */}
          <>
            {chatHistory.map((msg, index) => (
              <div
                key={index}
                className={`flex ${msg.sender === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-[80%] p-2 rounded-lg ${
                    msg.sender === 'user'
                      ? 'bg-primary text-primary-foreground'
                      : 'bg-muted'
                  }`}
                >
                  {msg.text}
                </div>
              </div>
            ))}
          </>
        </CardContent>

        <CardFooter className="p-3 border-t">
          <div className="flex w-full gap-2">
            <Input
              placeholder="Ask about this video..."
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyDown={handleKeyPress}
              className="flex-1"
            />
            <Button onClick={handleSendMessage}>Send</Button>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}

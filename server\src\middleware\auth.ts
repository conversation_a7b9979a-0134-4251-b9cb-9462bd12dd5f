import { FastifyRequest, FastifyReply } from 'fastify';
import { UserModel } from '../models';
import { AuthenticatedUser } from '../types/user';

// Extend FastifyRequest to include user property
interface RequestWithUser extends FastifyRequest {
  user: AuthenticatedUser;
}

// Extend FastifyInstance to include mongoose property
declare module 'fastify' {
  interface FastifyInstance {
    mongoose: any;
  }
}

/**
 * Authentication middleware
 * Verifies JWT token and attaches user to request
 */
export async function authenticate(request: FastifyRequest, reply: FastifyReply): Promise<void> {
  try {
    // Verify JWT token
    await request.jwtVerify();

    // Get user from database
    const user = await UserModel.findOne({
      id: (request.user as AuthenticatedUser).id,
      deletedAt: { $exists: false },
    });

    if (!user) {
      throw new Error('User not found');
    }

    // Check if user is active
    if (user.status !== 'active') {
      throw new Error(`Account is ${user.status}. Please contact support.`);
    }

    // Attach full user object to request
    request.user = {
      ...(request.user as AuthenticatedUser),
      roles: user.roles,
      permissions: user.permissions,
    };
  } catch (error) {
    reply.code(401).send({
      success: false,
      message: (error as Error).message || 'Authentication failed',
    });
  }
}

/**
 * Optional authentication middleware
 * Verifies JWT token if present, but doesn't require it
 */
export async function optionalAuthenticate(request: FastifyRequest, reply: FastifyReply): Promise<void> {
  try {
    // Check if Authorization header exists
    const authHeader = request.headers.authorization;
    if (!authHeader) {
      // No token provided, continue without authentication
      return;
    }

    // Verify JWT token
    await request.jwtVerify();

    // Get user from database
    const user = await UserModel.findOne({
      id: (request.user as AuthenticatedUser).id,
      deletedAt: { $exists: false },
    });

    if (!user) {
      // Invalid token, but we don't throw an error since authentication is optional
      return;
    }

    // Check if user is active
    if (user.status !== 'active') {
      // Inactive user, but we don't throw an error since authentication is optional
      return;
    }

    // Attach full user object to request
    request.user = {
      ...(request.user as AuthenticatedUser),
      roles: user.roles,
      permissions: user.permissions,
    };
  } catch (error) {
    // Authentication failed, but we don't throw an error since authentication is optional
    // Just continue without authenticated user
  }
}

/**
 * Admin authentication middleware
 * Verifies JWT token and checks if user has admin role
 */
export async function authenticateAdmin(request: FastifyRequest, reply: FastifyReply): Promise<void> {
  try {
    // First authenticate the user
    await authenticate(request, reply);

    // If we got here, authentication was successful
    // Now check if user has admin role
    const user = await UserModel.findOne({
      id: (request.user as AuthenticatedUser).id,
      deletedAt: { $exists: false },
    });

    // Get admin role
    const adminRole = await request.server.mongoose.model('Role').findOne({
      code: 'admin',
      deletedAt: { $exists: false },
    });

    if (!adminRole) {
      throw new Error('Admin role not found');
    }

    // Check if user has admin role
    if (!user || !user.roles || !user.roles.includes(adminRole.id)) {
      throw new Error('Admin access required');
    }
  } catch (error) {
    reply.code(403).send({
      success: false,
      message: (error as Error).message || 'Admin authentication failed',
    });
  }
}

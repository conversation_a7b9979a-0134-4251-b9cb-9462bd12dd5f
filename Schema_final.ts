/**
 * Base interface for all entities in the system
 */
interface BaseEntity {
  /** Unique identifier for the entity, typically a UUID */
  id: string;

  /** Timestamp when the entity was created */
  createdAt: Date;

  /** Timestamp when the entity was last updated */
  updatedAt: Date;

  /**
   * Timestamp when the entity was soft deleted
   * When set, the entity is considered deleted but data is preserved in the database
   * Optional field as most entities are not deleted initially
   */
  deletedAt?: Date;

  /**
   * Version number for optimistic concurrency control
   * Incremented on each update to prevent conflicts when multiple users edit simultaneously
   */
  version: number;

  /**
   * Flexible storage for additional data that doesn't fit the schema
   * Allows for extensibility without schema changes
   */
  metadata: Record<string, any>;

  /** User ID of the person who created this entity - for audit trail */
  createdBy: string;

  /** User ID of the person who last updated this entity - for audit trail */
  updatedBy: string;

  /** User ID of the person who soft deleted this entity - for audit trail */
  deletedBy?: string;
}

/**
 * Role Based Access Control (RBAC) System
 * Provides flexible permission management through roles and granular permissions
 */

/**
 * Permission - Represents a single granular permission in the system
 * Permissions are the building blocks of the RBAC system
 */
interface Permission extends BaseEntity {
  /** Human-readable name of the permission */
  name: string;

  /** Detailed description explaining what this permission allows */
  description: string;

  /**
   * Unique permission code used for programmatic access
   * Example: 'video:create', 'user:delete', 'settings:read'
   */
  code: string;

  /**
   * Category for grouping related permissions
   * Examples: 'content', 'users', 'settings', 'billing'
   */
  category: string;

  /**
   * Whether this permission is currently active
   * Inactive permissions are not enforced even if assigned
   */
  isActive: boolean;

  /**
   * Other permissions that are required for this permission to work
   * Example: 'video:publish' might depend on 'video:create'
   */
  dependencies?: string[];

  /** Additional custom data for this permission */
  metadata: Record<string, any>;
}

/**
 * Role - Collection of permissions assigned to users
 * Roles simplify permission management by grouping permissions
 */
interface Role extends BaseEntity {
  /** Human-readable name of the role */
  name: string;

  /** Detailed description of what this role represents and allows */
  description: string;

  /**
   * Array of permission codes granted to this role
   * Users with this role will have all these permissions
   */
  permissions: string[];

  /**
   * Whether this is a default role for new users
   * Default roles are automatically assigned to new users
   */
  isDefault: boolean;

  /**
   * Whether this is a system role that cannot be modified/deleted
   * System roles are essential for platform operation
   */
  isSystem: boolean;

  /**
   * Hierarchy level for role inheritance and overrides
   * Higher numbers indicate higher privilege levels
   * Example: Admin (100) > Moderator (50) > User (10)
   */
  //level: number;

  /**
   * Specific operations allowed for this role beyond permissions
   * Used for special system operations that don't fit the permission model
   */
  allowedOperations: string[];
  restrictedOperations: string[];

  /** Additional configuration for this role */
  metadata: {
    /** Maximum number of users that can have this role (for licensing/limits) */
    maxUsers?: number;

    /** Specific features this role has access to */
    allowedFeatures?: string[];

    /** Limitations or restrictions applied to this role */
    restrictions?: Record<string, any>;
  };
}

/**
 * User Management System
 * Core entity for managing user accounts and profiles
 */
interface User extends BaseEntity {
  /**
   * Unique username for the user
   * Used for login and @mentions
   */
  username: string;

  /**
   * User's email address
   * Used for login, notifications, and account recovery
   */
  email: string;

  /**
   * Hashed password for authentication
   * Never stored in plain text for security
   */
  password: string;

  /** User's first/given name */
  firstName: string;

  /** User's last/family name */
  lastName: string;

  /**
   * User's display name shown publicly
   * May be different from first+last name for privacy or preference
   */
  displayName: string;

  /**
   * URL to user's profile picture
   * Optional as users may not upload a profile picture
   */
  avatar?: string;

  /**
   * URL to user's profile header/banner image
   * Optional decorative element for user profiles
   */
  coverPhoto?: string;

  /**
   * User's contact phone number
   * Optional as not all users provide phone numbers
   */
  phoneNumber?: string;

  /**
   * Whether two-factor authentication is enabled for this account
   * Adds an extra security layer beyond password
   */
  twoFactorEnabled: boolean;

  /**
   * Secret key for two-factor authentication
   * Only present when 2FA is enabled
   */
  twoFactorSecret?: string;

  /**
   * Whether user is currently online
   * Used for presence indicators in UI
   */
  isOnline: boolean;

  /** Timestamp of user's most recent login */
  lastLoginAt: Date;

  /** IP address from which user last logged in */
  lastLoginIp: string;

  /**
   * Compound unique keys to handle uniqueness with soft delete
   * Prevents conflicts when recreating users with same identifiers
   */
  // uniqueKeys: {
  //   /** Compound key: username_deletedAt */
  //   username: string;

  //   /** Compound key: email_deletedAt */
  //   email: string;

  //   /** Compound key: phone_deletedAt (if phone exists) */
  //   phone?: string;
  // };

  /**
   * Third-party API keys owned by this user
   * For integrations with external services
   */
  apiKeys?: Array<{
    /** Name of the external service (e.g., 'twitter', 'github') */
    provider: string;

    /** API key or access token */
    key: string;

    /** API secret (if applicable) */
    secret?: string;

    /** Permission scopes granted to this key */
    scopes: string[];

    /** When this key/token expires (if applicable) */
    expiresAt?: Date;
  }>;

  /**
   * Webhook configurations for this user
   * For receiving notifications about events
   */
  webhooks?: Array<{
    /** Endpoint URL where webhooks will be sent */
    url: string;

    /** Event types this webhook subscribes to */
    events: string[];

    /** Secret used to sign webhook payloads for verification */
    secret: string;

    /** Whether this webhook is currently active */
    active: boolean;

    /** When this webhook was last triggered */
    lastTriggered?: Date;
  }>;

  /**
   * Whether user's email has been verified
   * Verified users have confirmed ownership of their email
   */
  isVerified: boolean;

  /** Token sent to user for email verification */
  verificationToken?: string;

  /** Token sent to user for password reset */
  passwordResetToken?: string;

  /** When the password reset token expires */
  passwordResetExpires?: Date;

  /**
   * Current status of the user account
   * Controls whether user can access the system
   *
   * Possible values:
   * - active: User has full access to the system
   * - banned: User is permanently blocked from the system due to violations
   * - pending: User account is awaiting approval or verification
   * - suspended: User is temporarily blocked, may be reinstated later
   */
  status: 'active' | 'banned' | 'pending' | 'suspended';

  /**
   * role ID assigned to this user
   * Determines user's permissions in the system
   * Replaces hardcoded role with flexible RBAC
   */
  role: string;

  /**
   * Custom fields for extensibility
   * Allows storing additional user attributes without schema changes
   */
  customFields: Record<string, any>;

  /** User preferences for personalization */
  preferences: {
    /** Preferred language code (e.g., 'en-US', 'fr') */
    language: string;

    /**
     * UI theme preference
     * - light: Light mode UI
     * - dark: Dark mode UI
     * - system: Follow system preference
     */
    theme: 'light' | 'dark' | 'system';

    /** Notification channel preferences */
    notifications: {
      /** Whether to receive email notifications */
      email: boolean;

      /** Whether to receive push notifications */
      push: boolean;

      /** Whether to receive SMS notifications */
      sms: boolean;

      /** Whether to receive in-app notifications */
      inApp: boolean;
    };

    /** Preferred timezone (e.g., 'America/New_York') */
    timezone: string;

    /** Preferred date format (e.g., 'MM/DD/YYYY') */
    dateFormat: string;

    /** Preferred currency code (e.g., 'USD', 'EUR') */
    currency: string;

    /** Accessibility preferences */
    accessibility: {
      /** Whether to use high contrast mode */
      highContrast: boolean;

      /** Base font size preference */
      fontSize: number;

      /** Whether to reduce motion in animations */
      reduceMotion: boolean;
    };
  };

  /** Social media profile links */
  social: {
    /** Facebook profile URL */
    facebook?: string;

    /** Twitter/X profile URL */
    twitter?: string;

    /** LinkedIn profile URL */
    linkedin?: string;

    /** YouTube channel URL */
    youtube?: string;

    /** Instagram profile URL */
    instagram?: string;
  };

  /** User activity statistics */
  stats: {
    /** Number of videos uploaded by this user */
    videosUploaded: number;

    /** Total views across all user's content */
    totalViews: number;

    /** Number of users following this user */
    followers: number;

    /** Number of users this user is following */
    following: number;

    /** Reputation/karma points in the system */
    reputation: number;
  };

  /** Resource usage and limits */
  limits?: {
    /** Current storage space used in bytes */
    storageUsed: number;

    /** Maximum storage space allowed in bytes */
    storageLimit: number;

    /** Maximum number of videos user can upload */
    videoUploadLimit: number;
  };
}

/**
 * Session Management System
 * Tracks user authentication sessions across devices
 */
interface Session extends BaseEntity {
  /** ID of the user this session belongs to */
  userId: string;

  /** Unique identifier for the device used */
  deviceId: string;

  /** Authentication token (JWT or similar) */
  token: string;

  /** Token used to obtain new access tokens without re-authentication */
  refreshToken: string;

  /** When this session expires and requires re-authentication */
  expiresAt: Date;

  /** Timestamp of the last activity in this session */
  lastActivity: Date;

  /** IP address from which this session originated */
  ipAddress: string;

  /** User agent string from the browser/app */
  userAgent: string;

  /** Detailed information about the device */
  deviceInfo: {
    /**
     * Device type
     * Examples: 'mobile', 'tablet', 'desktop', 'tv'
     */
    type: string;

    /**
     * Operating system
     * Examples: 'iOS', 'Android', 'Windows', 'macOS'
     */
    os: string;

    /**
     * Browser or app name
     * Examples: 'Chrome', 'Safari', 'Firefox', 'Native App'
     */
    browser: string;

    /** Version number of the browser/app */
    version: string;
  };

  /** Geolocation information (if available) */
  location?: {
    /** Country code (ISO) */
    country: string;

    /** City name */
    city: string;

    /** Latitude coordinate */
    latitude: number;

    /** Longitude coordinate */
    longitude: number;
  };

  /**
   * Whether this session is currently valid
   * Invalid sessions cannot be used for authentication
   */
  isValid: boolean;

  /** When this session was manually revoked (if applicable) */
  revokedAt?: Date;

  /**
   * Reason for session revocation
   * Examples: 'user_logout', 'security_concern', 'password_change'
   */
  revokedReason?: string;

  /**
   * ID of the previous session if this is a continuation
   * Used for tracking session chains across refreshes
   */
  previousSessionId?: string;
}

/**
 * Notification System
 * Manages notifications across multiple channels (email, push, SMS, in-app)
 */

/**
 * NotificationTemplate - Reusable templates for sending notifications
 * Templates define the structure and content for different notification types
 */
interface NotificationTemplate extends BaseEntity {
  /** Human-readable name of the template */
  name: string;

  /** Detailed description of when and how this template is used */
  description: string;

  /**
   * Type identifier for this notification
   * Examples: 'welcome', 'password_reset', 'comment_reply', 'payment_received'
   */
  type: string;

  /**
   * Delivery channels supported by this template
   * A single notification can be delivered through multiple channels
   */
  channels: ('email' | 'push' | 'sms' | 'in-app')[];

  /** Channel-specific content templates */
  templates: {
    /** Email-specific template */
    email?: {
      /** Email subject line */
      subject: string;

      /** Plain text email body */
      body: string;

      /** HTML formatted email body */
      html: string;
    };

    /** Push notification template */
    push?: {
      /** Push notification title */
      title: string;

      /** Push notification body text */
      body: string;
    };

    /** SMS message template */
    sms?: {
      /** SMS message content */
      message: string;
    };

    /** In-app notification template */
    inApp?: {
      /** In-app notification content */
      content: string;
    };
  };

  /**
   * Variables that can be used in the template
   * These are placeholders that get replaced with actual values when sending
   */
  variables: Array<{
    /** Variable name (e.g., 'user_name', 'reset_link') */
    name: string;

    /**
     * Expected data type
     * Examples: 'string', 'number', 'date', 'url'
     */
    type: string;

    /** Whether this variable must be provided when sending */
    required: boolean;

    /** Fallback value if the variable is not provided */
    defaultValue?: string;
  }>;

  /** Delivery settings and preferences */
  settings: {
    /**
     * Priority level affecting delivery speed and prominence
     *
     * Possible values:
     * - low: Non-urgent, can be batched or delayed
     * - normal: Standard priority, delivered in regular queue
     * - high: Important notifications, delivered with higher priority
     * - urgent: Critical notifications, delivered immediately with high visibility
     */
    priority: 'low' | 'normal' | 'high' | 'urgent';

    /** Rate limiting settings to prevent notification spam */
    throttling?: {
      /** Whether throttling is enabled for this template */
      enabled: boolean;

      /** Maximum number of notifications allowed in the time window */
      limit: number;

      /** Time window in seconds for the throttling limit */
      window: number;
    };

    /** Time-based delivery settings */
    scheduling?: {
      /** Timezone to use for scheduling decisions */
      timezone: string;

      /**
       * Hours during which notifications should not be sent
       * Used to avoid disturbing users during sleep hours
       */
      quietHours?: {
        /** Start time of quiet hours (format: 'HH:MM') */
        start: string;

        /** End time of quiet hours (format: 'HH:MM') */
        end: string;
      };
    };
  };
}

/**
 * Notification - Individual notification instance sent to a user
 * Created from templates and tracks delivery status
 */
interface Notification extends BaseEntity {
  /** ID of the user receiving this notification */
  userId: string;

  /** ID of the template this notification is based on */
  templateId: string;

  /**
   * Channel through which this notification is being sent
   * A single notification template may generate multiple notification records,
   * one for each channel
   */
  channel: 'email' | 'push' | 'sms' | 'in-app';

  /** Title/subject of the notification */
  title: string;

  /** Main content of the notification */
  content: string;

  /**
   * Additional structured data related to this notification
   * Can include context information or data needed for deep linking
   */
  data: Record<string, any>;

  /**
   * Current delivery status of the notification
   *
   * Possible values:
   * - pending: Notification is queued for delivery
   * - sent: Notification has been sent to the delivery service
   * - failed: Delivery attempt failed
   * - delivered: Successfully delivered to the user's device
   * - read: User has viewed/read the notification
   */
  status: 'pending' | 'sent' | 'failed' | 'delivered' | 'read';

  /** When the user read/viewed this notification */
  readAt?: Date;

  /** When this notification was delivered to the user's device */
  deliveredAt?: Date;

  /**
   * Priority level for this specific notification instance
   * Can override the template's default priority
   *
   * Possible values:
   * - low: Non-urgent, can be batched or delayed
   * - normal: Standard priority, delivered in regular queue
   * - high: Important notifications, delivered with higher priority
   * - urgent: Critical notifications, delivered immediately with high visibility
   */
  priority: 'low' | 'normal' | 'high' | 'urgent';

  /** When this notification should expire and no longer be shown */
  expiresAt?: Date;

  /** Action to be performed when the user interacts with the notification */
  action?: {
    /**
     * Type of action
     * Examples: 'open_url', 'open_app', 'open_screen'
     */
    type: string;

    /** URL to open (if applicable) */
    url?: string;

    /** Additional data needed for the action */
    data?: Record<string, any>;
  };

  /** Additional metadata for rendering and tracking */
  metadata: {
    /** Icon to display with the notification */
    icon?: string;

    /** Image to display with the notification */
    image?: string;

    /** Sound to play when notification is delivered */
    sound?: string;

    /** Badge count to display (for mobile apps) */
    badge?: number;

    /** Source that triggered this notification */
    source: string;

    /** Contextual information about the notification */
    context: Record<string, any>;
  };
}

/**
 * Chat and Messaging System
 * Enables real-time communication between users and creators
 * Note: The system only supports direct messaging between users and creators,
 * not between multiple users or between multiple creators
 */

/**
 * Conversation - Container for messages between a user and a creator
 * Represents a direct support or inquiry chat thread
 * Note: The system only supports user-to-creator messaging, not multi-user conversations
 */
interface Conversation extends BaseEntity {
  /**
   * Type of conversation - always 'direct' as the system only supports
   * direct messaging between users and creators
   */
 
  /**
   * ID of the creator receiving messages
   * This is the content creator who is being contacted by the user
   */
  creatorLanguage: string;
  userLanguage: string;
  creatorId: string;

  /**
   * ID of the user initiating the conversation
   * This is the regular user contacting the creator
   */
  userId: string;
 
  /**
   * Subject or topic of the conversation
   * Helps creators organize and prioritize user inquiries
   */
  subject?: string;

  /**
   * URL to the creator's avatar
   * Used for displaying the conversation in the user's inbox
   */
  creatorAvatar?: string;

  /** Timestamp of the most recent message */
  lastMessageAt: Date;

  /** Text preview of the most recent message (truncated) */
  lastMessagePreview: string;

  /**
   * Current status of the conversation
   *
   * Possible values:
   * - open: Conversation is active and awaiting responses
   * - resolved: The inquiry has been addressed and conversation is complete
   * - archived: Conversation has been archived by the creator
   */
  status: 'open' | 'resolved' | 'archived';

  /** Conversation settings and preferences */
  settings: {
    /** Whether participants receive notifications for new messages */
    notifications: boolean;

    /** Whether messages are end-to-end encrypted */
    encryption: boolean;

    /**
     * Message retention period in days
     * How long messages are kept before automatic deletion (0 = forever)
     */
    retention: number;

    /** Whether read receipts are enabled for this conversation */
    readReceipts: boolean;
  };

  /** Additional metadata about the conversation */
  metadata: {
    /** When the conversation was marked as resolved (if applicable) */
    resolvedAt?: Date;

    /** User ID of the person who resolved the conversation */
    resolvedBy?: string;

    /** Category or department this conversation relates to */
    category?: string;

    /** Priority level assigned by the creator */
    priority?: 'low' | 'normal' | 'high' | 'urgent';

    /** Whether this conversation is pinned by the creator */
    isPinned?: boolean;

    /** Custom application-specific data */
    customData?: Record<string, any>;
  };
}

/**
 * Message - Individual message within a user-to-creator conversation
 * Core unit of communication in the direct messaging system
 * Note: The system only supports messaging between users and creators, not between multiple users
 */
interface Message extends BaseEntity {
  /** ID of the conversation this message belongs to */
  conversationId: string;

  /** ID of the user or creator who sent this message */
  senderId: string;

  /**
   * Sender type - identifies whether the message is from a user or creator
   * Used for UI display and permission checks
   */
  senderType: 'user' | 'creator' | 'system';

  /**
   * Type of message content
   *
   * Possible values:
   * - text: Plain text message
   * - image: Image attachment with optional caption
   * - video: Video attachment with optional caption
   * - audio: Audio/voice message
   * - file: Document or other file attachment
   * - location: Shared geographic location
   * - system: System-generated message (e.g., "Conversation marked as resolved")
   */
  type: 'text' | 'image' | 'video' | 'audio' | 'file' | 'location' | 'system';

  /**
   * Message content
   * For text messages: the text content
   * For media messages: optional caption
   * For system messages: system-generated text
   */
  content: string;

  /** Media or file attachments */
  attachments?: Array<{
    /** Unique identifier for the attachment */
    id: string;

    /**
     * MIME type or attachment type
     * Examples: 'image/jpeg', 'video/mp4', 'application/pdf'
     */
    type: string;

    /** URL where the attachment can be accessed */
    url: string;

    /** Original filename of the attachment */
    name: string;

    /** File size in bytes */
    size: number;

    /**
     * Additional metadata specific to the attachment type
     * For images: dimensions, orientation
     * For videos: duration, thumbnail
     * For audio: duration, waveform
     */
    metadata?: Record<string, any>;
  }>;

  /**
   * ID of the message this message is replying to
   * Enables threaded replies within the conversation
   */
  replyTo?: string;

  /**
   * Whether this message is an internal note
   * Internal notes are only visible to creators, not to users
   * Used for creators to add private context to conversations
   */
  isInternalNote?: boolean;

  /**
   * Read status
   * For user messages: whether the creator has read it
   * For creator messages: whether the user has read it
   */
  isRead: boolean;

  /** When the message was read by the recipient */
  readAt?: Date;

  /** When the message was delivered to the recipient's device */
  deliveredAt?: Date;

  /** Timestamp when this message was last edited (if applicable) */
  editedAt?: Date;

  /**
   * Timestamp when this message was deleted (if applicable)
   * Deleted messages may still be stored but shown differently in UI
   */
  deletedAt?: Date;

  /**
   * Whether this message has been flagged for review
   * Used for content moderation
   */
  isFlagged?: boolean;

  /** Additional metadata about the message */
  metadata: {
    /**
     * Client-generated ID for optimistic updates
     * Helps match pending messages with server responses
     */
    clientId?: string;

    /**
     * Tags applied to this message by the creator
     * Used for categorizing and searching messages
     */
    tags?: string[];

    /**
     * Whether this message contains an answer to the user's question
     * Used for knowledge base and FAQ generation
     */
    isAnswer?: boolean;

    /** Geographic location data (for location messages) */
    location?: {
      /** Latitude coordinate */
      latitude: number;

      /** Longitude coordinate */
      longitude: number;

      /** Human-readable address of the location */
      address?: string;
    };
  };
}

/**
 * Voice Call System
 * Enables audio and video communication between users and creators
 * Note: The system only supports calls between users and creators, not between multiple users
 */
interface Call extends BaseEntity {
  /**
   * Type of call
   *
   * Possible values:
   * - audio: Voice-only call
   * - video: Video call with audio
   */
  type: 'audio' | 'video';

  /**
   * The two participants in this call
   * Always contains exactly two IDs: [userId, creatorId]
   */
  participants: [string, string];

  /**
   * ID of the creator in the call
   * This is the content creator being contacted
   */
  creatorId: string;

  /**
   * ID of the user in the call
   * This is the regular user contacting the creator
   */
  userId: string;

  /** User ID of the person who initiated the call */
  initiatorId: string;

  /**
   * Current status of the call
   *
   * Possible values:
   * - ringing: Call is being initiated, waiting for recipient to answer
   * - ongoing: Call is currently active and connected
   * - ended: Call has completed normally
   * - missed: Recipient did not answer the call
   * - rejected: Recipient explicitly declined the call
   */
  status: 'ringing' | 'ongoing' | 'ended' | 'missed' | 'rejected';

  /**
   * Purpose of the call
   *
   * Possible values:
   * - support: User seeking help or support from creator
   * - consultation: Scheduled consultation or coaching session
   * - feedback: Discussion about content or services
   * - general: General conversation
   */
  purpose: 'support' | 'consultation' | 'feedback' | 'general';

  /** When the call was answered and began (if applicable) */
  startedAt?: Date;

  /** When the call ended (if applicable) */
  endedAt?: Date;

  /** Duration of the call in seconds (if completed) */
  duration?: number;

  /** URL to the call recording (if call was recorded) */
  recordingUrl?: string;

  /** Whether the call was scheduled in advance */
  isScheduled: boolean;

  /** Scheduled start time (if applicable) */
  scheduledAt?: Date;

  /** Additional technical and contextual information */
  metadata: {
    /**
     * Quality rating of the call
     * Examples: 'excellent', 'good', 'fair', 'poor'
     */
    quality?: string;

    /** Information about devices used in the call */
    deviceInfo?: Record<string, any>;

    /** Network performance statistics */
    networkStats?: {
      /** Data transfer rate in kbps */
      bitrate?: number;

      /** Percentage of data packets lost during transmission */
      packetLoss?: number;

      /** Round-trip time in milliseconds */
      latency?: number;
    };

    /** Whether screen sharing was used during the call */
    screenSharing?: boolean;

    /** Notes about the call (visible only to the creator) */
    creatorNotes?: string;

    /** Tags applied to this call by the creator */
    tags?: string[];

    /**
     * Reason why the call ended
     *
     * Possible values:
     * - completed: Call ended normally by a participant
     * - dropped: Call ended due to connection issues
     * - rejected: Recipient declined the call
     * - busy: Recipient was on another call
     * - no_answer: Call timed out without answer
     */
    endReason?: 'completed' | 'dropped' | 'rejected' | 'busy' | 'no_answer';
  };
}

/**
 * Chatbot System
 * Provides conversational AI capabilities for automated interactions
 */

/**
 * ChatbotSession - Represents a conversation session with the chatbot
 * Tracks the state and context of an ongoing interaction
 */
interface ChatbotSession extends BaseEntity {
  /** ID of the user interacting with the chatbot */
  userId: string;

  /**
   * Optional context identifier for multi-turn conversations
   * Used to link related sessions or track conversation flows
   */
  contextId?: string;

  /** Language code for this conversation (e.g., 'en-US', 'es') */
  language: string;

  /**
   * Current status of the session
   *
   * Possible values:
   * - active: Session is currently ongoing
   * - ended: Session has been completed or terminated
   */
  status: 'active' | 'ended';

  /** When this session started */
  startedAt: Date;

  /** When this session ended (if applicable) */
  endedAt?: Date;

  /** Performance and usage metrics for this session */
  metrics: {
    /** Total number of messages exchanged in this session */
    messageCount: number;

    /** Number of voice/audio interactions in this session */
    voiceInteractions: number;

    /** Average time in milliseconds for bot to respond */
    averageResponseTime: number;

    /**
     * Percentage of successful interactions
     * Measures how often the bot correctly understood and responded
     */
    successRate: number;
  };

  /**
   * Conversation context data
   * Stores information about the conversation state and user preferences
   */
  context?: Record<string, any>;
}

/**
 * ChatbotMessage - Individual message in a chatbot conversation
 * Can be from the user, the bot, or a system notification
 */
interface ChatbotMessage extends BaseEntity {
  /** ID of the session this message belongs to */
  sessionId: string;

  /**
   * Source of the message
   *
   * Possible values:
   * - user: Message sent by the human user
   * - bot: Response generated by the chatbot
   * - system: System notification or status message
   */
  type: 'user' | 'bot' | 'system';

  /** Content of the message */
  content: string;

  /**
   * Format of the message content
   *
   * Possible values:
   * - text: Plain text message
   * - voice: Audio/speech message
   * - structured: JSON or structured data (for rich interactions)
   */
  format: 'text' | 'voice' | 'structured';

  /**
   * Detected intent of the message
   * References an intent name from the chatbot configuration
   */
  intent?: string;

  /**
   * Confidence score for intent detection
   * Value between 0 and 1, where 1 is highest confidence
   */
  confidence?: number;

  /**
   * Context data for this specific message
   * Includes entities extracted and slot values
   */
  context?: Record<string, any>;

  /** Additional technical metadata */
  metadata: {
    /** Time taken to process this message in milliseconds */
    processingTime?: number;

    /** URL to audio file for voice messages */
    audioUrl?: string;

    /** Translations of the message in different languages */
    translations?: Record<string, string>;

    /** Suggested quick replies or follow-up options */
    suggestions?: string[];
  };
}

/**
 * ChatbotIntent - Definition of a conversational intent
 * Maps user inputs to specific bot responses and actions
 */
interface ChatbotIntent extends BaseEntity {
  /** Name of the intent (e.g., 'book_appointment', 'check_weather') */
  name: string;

  /** Human-readable description of what this intent handles */
  description: string;

  /**
   * Example phrases that trigger this intent
   * Used for training the natural language understanding model
   */
  trainingPhrases: string[];

  /** Possible responses for this intent */
  responses: Array<{
    /**
     * Type of response
     *
     * Possible values:
     * - text: Plain text response
     * - rich: Rich media response (images, cards, etc.)
     * - action: Triggers a backend action or API call
     */
    type: 'text' | 'rich' | 'action';

    /**
     * Content of the response
     * For text: the message text with optional variable placeholders
     * For rich: JSON structure defining the rich content
     * For action: Action identifier or function name to execute
     */
    content: string;

    /**
     * Conditional logic for when to use this response
     * Allows for context-sensitive responses based on entities or session data
     */
    conditions?: Record<string, any>;
  }>;

  /** Parameters extracted from user input */
  parameters: Array<{
    /** Name of the parameter (e.g., 'date', 'location') */
    name: string;

    /**
     * Data type of the parameter
     * Examples: 'string', 'number', 'date', 'location', 'currency'
     */
    type: string;

    /** Whether this parameter must be collected for the intent to be fulfilled */
    required: boolean;

    /** Default value if parameter is not provided */
    defaultValue?: any;

    /**
     * Phrases to ask the user when the parameter is missing
     * Used for slot-filling in multi-turn conversations
     */
    prompts: string[];
  }>;

  /** Context management for multi-turn conversations */
  contexts: {
    /** Contexts that must be active for this intent to be matched */
    input: string[];

    /** Contexts that will be set when this intent is matched */
    output: string[];
  };

  /** Whether this intent is currently active in the system */
  enabled: boolean;
}



/**
 * Video Platform
 * Core entity for managing video content
 */
interface Video extends BaseEntity {
  /** Title of the video */
  title: string;

  /** Full description of the video content */
  description: string;

  /** Abbreviated description for previews and cards */
  shortDescription: string;

  /**
   * URL-friendly identifier for the video
   * Used in video URLs and must be unique
   */
  slug: string;

  /**
   * Short alphanumeric identifier for the video
   * Used for short URLs and sharing
   */
  shortId: string;

  /** Duration of the video in seconds */
  duration: number;

  /** User ID of the video creator/uploader */
  creatorId: string;

  /**
   * Moderation status of the video
   *
   * Possible values:
   * - pending: Awaiting review by moderators
   * - rejected: Failed moderation review
   * - approved: Passed moderation review
   */
  approvalStatus: 'pending' | 'rejected' | 'approved';

  /**
   * Visibility setting for the video
   *
   * Possible values:
   * - public: Visible to everyone, appears in search and feeds
   * - private: Only visible to the creator and specified users
   * - unlisted: Accessible via direct link but not listed in search or feeds
   * - scheduled: Will become public at a scheduled time
   */
  visibility: 'public' | 'private' | 'unlisted' | 'scheduled';

  /**
   * When the video will be automatically published
   * Only applicable when visibility is 'scheduled'
   */
  scheduledPublishDate?: Date;

  /** Monetization settings for the video */
  monetization: {
    /** Whether monetization is enabled for this video */
    enabled: boolean;

    /**
     * Monetization model
     *
     * Possible values:
     * - subscription: Available to subscribers only
     * - ppv: Pay-per-view (one-time purchase)
     * - free: Available at no cost
     */
    type: 'subscription' | 'ppv' | 'free';

    /** Price for pay-per-view access (if applicable) */
    price?: number;

    /** Currency code for the price (e.g., 'USD', 'EUR') */
    currency?: string;
  };

  /** Primary category of the video */
  category: string;

  /** Secondary, more specific category */
  subcategory?: string;

  /** Array of tags/keywords for the video */
  tags: string[];

  /** Language versions and translations */
  languages: Array<{
    /** Language code (e.g., 'en-US', 'es') */
    code: string;

    /** URL to the video in this language */
    url: string;

    /** Whether this is the original/default language */
    isDefault: boolean;

    /** URL to subtitle file for this language */
    subtitles?: string;

    /** URL to audio track in this language */
    audioTrack?: string;
  }>;

  /** Video chapter markers */
  // chapters: Array<{
  //   /** Chapter title */
  //   title: string;

  //   /** Start time in seconds */
  //   startTime: number;

  //   /** End time in seconds */
  //   endTime: number;

  //   /** URL to thumbnail image for this chapter */
  //   thumbnail?: string;
  // }>;

  /** Viewer engagement metrics */
  engagement: {
    /** Total number of views */
    views: number;

    /** Number of unique viewers */
    uniqueViews: number;

    /** Number of likes/upvotes */
    likes: number;

    /** Number of dislikes/downvotes */
    dislikes: number;

    /** Number of times shared */
    shares: number;

    /** Number of comments */
    comments: number;

    /** Total watch time in seconds across all views */
    watchTime: number;

    /** Average view duration in seconds */
    averageWatchDuration: number;

    /**
     * Array of retention percentages at different points in the video
     * Each value represents the percentage of viewers still watching at that point
     * Example: [100, 90, 85, 70, 65, 60] for retention at 0%, 20%, 40%, 60%, 80%, 100% of video
     */
    retention: number[];
  };

  /** Search engine optimization data */
  seo: {
    /** SEO-optimized title (may differ from display title) */
    title: string;

    /** SEO-optimized description */
    description: string;

    /** SEO keywords */
    keywords: string[];

    /** Open Graph image URL for social sharing */
    ogImage: string;

    /** Whether search engines should index this video */
    allowIndexing: boolean;
  };

  /** Access restrictions */
  restrictions: {
    /** Minimum age required to view (in years) */
    ageRestriction?: number;

    /** Country codes where this video is blocked */
    geoRestriction?: string[];

    /** Whether this video requires a password to view */
    passwordProtected?: boolean;

    /** Password for accessing the video (if protected) */
    password?: string;
  };
}

/**
 * Analytics System
 * Tracks and stores metrics for reporting and analysis
 */
interface Analytics extends BaseEntity {
  /**
   * Category of the analytics data
   *
   * Possible values:
   * - video: Metrics related to video performance
   * - user: Metrics related to user behavior
   * - platform: System-wide metrics
   * - revenue: Financial metrics
   * - performance: Technical performance metrics
   */
  type: 'video' | 'user' | 'platform' | 'revenue' | 'performance';

  /**
   * Specific metric being tracked
   * Examples: 'views', 'sign_ups', 'revenue', 'load_time'
   */
  metric: string;

  /** Numeric value of the metric */
  value: number;

  /**
   * Dimensions for slicing the data
   * Key-value pairs for categorizing the metric
   * Examples: { 'country': 'US', 'device': 'mobile', 'plan': 'premium' }
   */
  dimension: Record<string, string>;

  /** Timestamp when this data point was recorded */
  date: Date;

  /**
   * Time granularity of the data point
   *
   * Possible values:
   * - 1m: One minute interval
   * - 5m: Five minute interval
   * - 1h: One hour interval
   * - 1d: One day interval
   * - 1w: One week interval
   * - 1M: One month interval
   */
  interval: '1m' | '5m' | '1h' | '1d' | '1w' | '1M';

  /** Additional contextual information */
  metadata: {
    /** Source of the data (e.g., 'web', 'mobile_app', 'api') */
    source: string;

    /** Additional context data specific to this metric */
    context: Record<string, any>;

    /** Segment identifiers for grouping related metrics */
    segments: string[];
  };

  /** Pre-calculated aggregations for this metric */
  aggregations?: {
    /** Sum of values over the aggregation period */
    sum?: number;

    /** Average value over the aggregation period */
    avg?: number;

    /** Minimum value in the aggregation period */
    min?: number;

    /** Maximum value in the aggregation period */
    max?: number;

    /** Count of data points in the aggregation */
    count?: number;
  };
}

/**
 * Content Management System
 * Manages various types of content beyond videos
 */
interface Content extends BaseEntity {
  /**
   * Type of content
   *
   * Possible values:
   * - article: Blog post or news article
   * - page: Static website page
   * - faq: Frequently asked question
   * - announcement: System or platform announcement
   */
  type: 'article' | 'page' | 'faq' | 'announcement';

  /** Title of the content */
  title: string;

  /** Main content body (typically HTML or Markdown) */
  content: string;

  /** Short summary for previews and listings */
  excerpt: string;

  /** URL-friendly identifier (must be unique) */
  slug: string;

  /** User ID of the content author */
  author: string;

  /** Primary category for the content */
  category: string;

  /** Array of tags/keywords */
  tags: string[];

  /**
   * Publication status
   *
   * Possible values:
   * - published: Content is live and publicly accessible
   * - draft: Content is in progress and not publicly visible
   * - archived: Content is no longer actively displayed but preserved
   * - scheduled: Content will be published at a future date
   */
  status: 'published' | 'draft' | 'archived' | 'scheduled';

  /** When the content was or will be published */
  publishedAt?: Date;

  /** When the content should be automatically unpublished */
  unPublishAt?: Date;

  /** URL to the main image for the content */
  featuredImage?: string;

  /**
   * Template identifier to use for rendering
   * Allows for different layouts for different content types
   */
  template?: string;

  /**
   * Numeric position for manual sorting
   * Lower numbers appear first in ordered lists
   */
  order?: number;

  /**
   * ID of the parent content item
   * Used for hierarchical relationships (e.g., pages with subpages)
   */
  parent?: string;

  /**
   * Language code of the original content
   * ISO 639-1 code (e.g., 'en', 'es', 'fr')
   */
  originalLanguage: string;

  /**
   * Available translations for this content
   * Maps language codes to translation IDs
   * Example: { 'es': 'translation-123', 'fr': 'translation-456' }
   */
  translations?: Record<string, string>;

  /** Comments on this content */
  comments?: Array<{
    /** Unique identifier for the comment */
    id: string;

    /** User ID of the commenter */
    userId: string;

    /** Comment text */
    content: string;

    /** When the comment was created */
    createdAt: Date;

    /** When the comment was last edited */
    updatedAt?: Date;

    /**
     * Moderation status
     *
     * Possible values:
     * - approved: Visible to all users
     * - pending: Awaiting moderation
     * - spam: Marked as spam
     * - deleted: Removed but record preserved
     */
    status: 'approved' | 'pending' | 'spam' | 'deleted';

    /** Number of likes/upvotes */
    likes: number;

    /** IDs of comments that are replies to this comment */
    replies?: string[];
  }>;

  /** Search engine optimization data */
  seo: {
    /** SEO-optimized title */
    title: string;

    /** Meta description */
    description: string;

    /** SEO keywords */
    keywords: string[];

    /** Open Graph image for social sharing */
    ogImage: string;

    /** Canonical URL (for duplicate content) */
    canonical?: string;

    /** Structured data for rich search results (JSON-LD) */
    structuredData?: Record<string, any>;
  };

  /**
   * Visibility setting
   *
   * Possible values:
   * - public: Visible to all users
   * - private: Visible only to authorized users
   * - password_protected: Requires a password to view
   */
  visibility: 'public' | 'private' | 'password_protected';

  /** Password for accessing protected content */
  password?: string;

  /** Version history */
  revisions: Array<{
    /** Revision identifier */
    id: string;

    /** Content at this revision */
    content: string;

    /** User ID who created this revision */
    author: string;

    /** When this revision was created */
    createdAt: Date;

    /** Note explaining the changes in this revision */
    comment: string;
  }>;
}

/**
 * Translation - Represents a translated version of content
 * Enables multilingual support across the platform
 */
interface Translation extends BaseEntity {
  /** ID of the original content being translated */
  originalContentId: string;

  /** Type of content being translated (e.g., 'article', 'page', 'video') */
  contentType: string;

  /** Language code of this translation (ISO 639-1) */
  languageCode: string;

  /** Human-readable language name */
  languageName: string;

  /** Translated title */
  title: string;

  /** Translated content body */
  content: string;

  /** Translated excerpt/summary */
  excerpt?: string;

  /**
   * Translation status
   *
   * Possible values:
   * - draft: Translation is in progress
   * - review: Translation is complete but needs review
   * - published: Translation is approved and live
   * - outdated: Original content has changed since translation
   */
  status: 'draft' | 'review' | 'published' | 'outdated';

  /** User ID of the translator */
  translatorId: string;

  /** User ID of the reviewer who approved the translation */
  reviewerId?: string;

  /** When the translation was reviewed */
  reviewedAt?: Date;

  /**
   * Translation method used
   *
   * Possible values:
   * - human: Manually translated by a person
   * - machine: Automatically translated by AI/machine
   * - hybrid: Machine translated then human edited
   */
  translationMethod: 'human' | 'machine' | 'hybrid';

  /**
   * Quality score (0-100) if machine translated
   * Higher scores indicate higher confidence in translation quality
   */
  qualityScore?: number;

  /**
   * Additional translated fields specific to the content type
   * For example, SEO fields, custom fields, etc.
   */
  additionalFields?: Record<string, any>;

  /**
   * Version of the original content this was translated from
   * Used to detect when translations become outdated
   */
  originalContentVersion: number;

  /** Translation revision history */
  revisions?: Array<{
    /** Revision ID */
    id: string;

    /** Translated content at this revision */
    content: string;

    /** When this revision was created */
    createdAt: Date;

    /** User who created this revision */
    createdBy: string;

    /** Notes about this revision */
    notes?: string;
  }>;
}

/**
 * System Configuration
 * Global settings for the entire platform
 */
interface SystemConfig extends BaseEntity {
  /** Maintenance mode settings */
  maintenance: {
    /** Whether maintenance mode is currently active */
    enabled: boolean;

    /** Message to display to users during maintenance */
    message: string;

    /** IP addresses that can still access the system during maintenance */
    allowedIPs: string[];

    /** When maintenance mode will start (for scheduled maintenance) */
    startTime?: Date;

    /** When maintenance mode will end (for scheduled maintenance) */
    endTime?: Date;
  };

  /** Translation and localization settings */
  translation: {
    /** Whether multi-language support is enabled */
    enabled: boolean;

    /** Default language code (ISO 639-1) */
    defaultLanguage: string;

    /** List of supported languages */
    supportedLanguages: Array<{
      /** Language code (ISO 639-1) */
      code: string;

      /** Human-readable language name */
      name: string;

      /** Whether this language is currently active */
      active: boolean;

      /** Right-to-left text direction */
      rtl: boolean;

      /** Language-specific date format */
      dateFormat?: string;

      /** Language-specific number format */
      numberFormat?: string;
    }>;

    /** Automatic translation settings */
    autoTranslation: {
      /** Whether automatic translation is enabled */
      enabled: boolean;

      /**
       * Translation service provider
       *
       * Possible values:
       * - google: Google Translate API
       * - azure: Azure Translator
       * - deepl: DeepL API
       * - openai: OpenAI API
       * - custom: Custom translation service
       */
      provider: 'google' | 'azure' | 'deepl' | 'openai' | 'custom';

      /** Provider-specific API settings */
      apiSettings: Record<string, any>;

      /** Minimum confidence score to accept automatic translations (0-100) */
      minimumConfidence: number;

      /** Whether to require human review for automatic translations */
      requireReview: boolean;

      /** Content types eligible for automatic translation */
      eligibleContentTypes: string[];
    };

    /** URL path structure for multilingual content */
    urlFormat: 'prefix' | 'subdomain' | 'parameter';

    /** Whether to show language selector in UI */
    showLanguageSelector: boolean;

    /** Whether to redirect users based on browser language */
    browserLanguageRedirect: boolean;

    /** Whether to translate user-generated content */
    translateUserContent: boolean;
  };

  /** Security settings */
  security: {
    /** Number of failed login attempts before account lockout */
    maxLoginAttempts: number;

    /** Session timeout in minutes */
    sessionTimeout: number;

    /** Password requirements */
    passwordPolicy: {
      /** Minimum number of characters required */
      minLength: number;

      /** Whether special characters are required */
      requireSpecialChars: boolean;

      /** Whether numbers are required */
      requireNumbers: boolean;

      /** Whether uppercase letters are required */
      requireUppercase: boolean;

      /** Number of days after which passwords expire */
      expiryDays: number;
    };

    /** API rate limiting configuration */
    apiRateLimit: {
      /** Whether rate limiting is enabled */
      enabled: boolean;

      /** Maximum number of requests allowed in the time window */
      maxRequests: number;

      /** Time window in milliseconds */
      windowMs: number;
    };
  };

  /** Email system configuration */
  email: {
    /** Map of template identifiers to template content */
    templates: Record<string, string>;

    /** Provider-specific email settings */
    settings: Record<string, any>;

    /** Notification email settings */
    notifications: {
      /** Types of notifications that can be sent */
      types: string[];

      /** Whether notifications are enabled by default */
      defaultEnabled: boolean;
    };

    /**
     * Email delivery service provider
     *
     * Possible values:
     * - smtp: Standard SMTP server
     * - sendgrid: SendGrid email service
     * - aws-ses: Amazon Simple Email Service
     * - mailchimp: Mailchimp/Mandrill
     * - postmark: Postmark App
     */
    provider: 'smtp' | 'sendgrid' | 'aws-ses' | 'mailchimp' | 'postmark';

    /** Sender name shown in emails */
    fromName: string;

    /** Sender email address */
    fromEmail: string;

    /** Reply-to email address */
    replyTo: string;

    /** Standard text added to the footer of all emails */
    footerText: string;

    /** Whether system notifications via email are enabled */
    enableNotifications: boolean;

    /** SMTP server configuration (when provider is 'smtp') */
    smtp: {
      /** SMTP server hostname */
      host: string;

      /** SMTP server port (typically 25, 465, or 587) */
      port: number;

      /** SMTP authentication username */
      username: string;

      /** SMTP authentication password */
      password: string;

      /**
       * Connection encryption type
       *
       * Possible values:
       * - tls: Transport Layer Security (typically port 587)
       * - ssl: Secure Sockets Layer (typically port 465)
       * - none: No encryption (typically port 25, not recommended)
       */
      encryption: 'tls' | 'ssl' | 'none';

      /** Whether to allow self-signed SSL certificates */
      allowSelfSigned: boolean;

      /** Connection timeout in seconds */
      timeout: number;

      /** Maximum number of messages to send in a single connection */
      maxConnections: number;

      /** Maximum number of messages to send per second */
      rateLimitPerSecond: number;
    };
  };

  /** File storage configuration */
  storage: {
    /**
     * Storage provider
     *
     * Possible values:
     * - local: Local file system
     * - s3: Amazon S3
     * - gcs: Google Cloud Storage
     */
    provider: 'local' | 's3' | 'gcs';

    /** Provider-specific settings */
    settings: Record<string, any>;

    /** Storage limitations */
    limits: {
      /** Maximum file size in bytes */
      maxFileSize: number;

      /** Array of allowed MIME types */
      allowedTypes: string[];

      /** Total storage space allowed in bytes */
      totalSpace: number;
    };
  };

  /** Caching system configuration */
  cache: {
    /** Whether caching is enabled */
    enabled: boolean;

    /**
     * Cache provider
     *
     * Possible values:
     * - redis: Redis cache
     * - memcached: Memcached cache
     */
    provider: 'redis' | 'memcached';

    /** Default time-to-live in seconds */
    ttl: number;

    /** Provider-specific settings */
    settings: Record<string, any>;
  };

  /** Background job queue configuration */
  queue: {
    /**
     * Queue provider
     *
     * Possible values:
     * - redis: Redis-based queue
     * - rabbitmq: RabbitMQ message broker
     * - beanstalkd: Beanstalkd queue
     * - kafka: Apache Kafka
     * - azure: Azure Service Bus
     * - aws-sqs: Amazon Simple Queue Service
     * - google-pubsub: Google Cloud Pub/Sub
     * - bullmq: BullMQ (Redis-based)
     */
    provider: 'redis' | 'rabbitmq' | 'beanstalkd' | 'kafka' | 'azure' | 'aws-sqs' | 'google-pubsub' | 'bullmq';

    /** Provider-specific settings */
    settings: Record<string, any>;

    /** Job processing settings */
    jobs: {
      /** Number of jobs to process simultaneously */
      concurrency: number;

      /** Job timeout in seconds */
      timeout: number;

      /** Number of retry attempts for failed jobs */
      retries: number;
    };
  };

  /** System monitoring configuration */
  monitoring: {
    /** Whether monitoring is enabled */
    enabled: boolean;

    /**
     * Monitoring provider
     *
     * Possible values:
     * - prometheus: Prometheus monitoring system
     * - datadog: Datadog monitoring service
     */
    provider: 'prometheus' | 'datadog';

    /** Metrics to collect */
    metrics: string[];

    /** Alert configuration */
    alerting: {
      /** Whether alerting is enabled */
      enabled: boolean;

      /** Notification channels for sending alerts */
      channels: string[];

      /** Threshold values that trigger alerts */
      thresholds: Record<string, number>;
    };
  };
}

/**
 * Moderation System
 * Rules and tools for content moderation
 */
interface ModerationRule extends BaseEntity {
  /**
   * Type of content this rule applies to
   *
   * Possible values:
   * - text: Text content (comments, messages, etc.)
   * - image: Image content (photos, thumbnails, etc.)
   * - video: Video content
   */
  type: 'text' | 'image' | 'video';

  /** Human-readable name of the rule */
  name: string;

  /** Detailed description of what this rule detects */
  description: string;

  /** Whether this rule is currently active */
  enabled: boolean;

  /**
   * Action to take when rule conditions are met
   *
   * Possible values:
   * - flag: Mark for human review without blocking
   * - block: Prevent content from being published
   * - delete: Automatically remove the content
   * - notify: Send notifications without blocking
   */
  action: 'flag' | 'block' | 'delete' | 'notify';

  /** Conditions that trigger this rule */
  conditions: Array<{
    /** Content field to check (e.g., 'title', 'body', 'tags') */
    field: string;

    /**
     * Comparison operator
     *
     * Possible values:
     * - contains: Field contains the value
     * - equals: Field exactly matches the value
     * - regex: Field matches the regular expression
     * - greater: Field is numerically greater than value
     * - less: Field is numerically less than value
     */
    operator: 'contains' | 'equals' | 'regex' | 'greater' | 'less';

    /** Value to compare against */
    value: any;

    /**
     * Confidence score threshold (0-1)
     * For AI-based detection, minimum confidence to trigger
     */
    score?: number;
  }>;

  /** Notification settings when rule is triggered */
  notifications: {
    /** Email addresses to notify */
    emails: string[];

    /** Webhook URLs to call */
    webhooks: string[];
  };

  /** Additional metadata about the rule */
  metadata: {
    /** User ID who created this rule */
    createdBy: string;

    /** User ID who last modified this rule */
    lastModifiedBy: string;

    /** Rule version number */
    version: number;

    /** Additional notes about the rule */
    notes: string;
  };
}

/**
 * Reporting System
 * Handles user-submitted reports of inappropriate content or behavior
 */
interface Report extends BaseEntity {
  /**
   * Type of content being reported
   *
   * Possible values:
   * - video: Report about a video
   * - comment: Report about a comment
   * - user: Report about a user's behavior
   * - channel: Report about a channel
   */
  reportType: 'video' | 'comment' | 'user' | 'channel';

  /** ID of the content being reported */
  targetId: string;

  /** User ID of the person submitting the report */
  reporterId: string;

  /**
   * Category of the report
   * Examples: 'harassment', 'copyright', 'violence', 'spam'
   */
  category: string;

  /** Specific reason for the report */
  reason: string;

  /** Detailed description provided by the reporter */
  description: string;

  /** URLs or IDs of supporting evidence (screenshots, etc.) */
  evidence: string[];

  /**
   * Current status of the report
   *
   * Possible values:
   * - pending: Report has been submitted but not yet reviewed
   * - investigating: Report is being reviewed by moderators
   * - resolved: Report has been addressed and closed
   * - rejected: Report was determined to be invalid
   */
  status: 'pending' | 'investigating' | 'resolved' | 'rejected';

  /**
   * Priority level of the report
   *
   * Possible values:
   * - low: Non-urgent issue
   * - medium: Standard priority
   * - high: Requires prompt attention
   * - urgent: Requires immediate attention (e.g., illegal content)
   */
  priority: 'low' | 'medium' | 'high' | 'urgent';

  /** User ID of the moderator assigned to handle this report */
  assignedTo?: string;

  /** Resolution information (when status is 'resolved') */
  resolution?: {
    /**
     * Action taken
     * Examples: 'content_removed', 'warning_issued', 'account_suspended'
     */
    action: string;

    /** Notes about the resolution */
    note: string;

    /** User ID of the moderator who took the action */
    takenBy: string;

    /** When the action was taken */
    takenAt: Date;
  };

  /** History of actions taken on this report */
  history: Array<{
    /**
     * Type of action
     * Examples: 'created', 'assigned', 'status_changed', 'comment_added'
     */
    action: string;

    /** User ID of the person who performed the action */
    actor: string;

    /** When the action occurred */
    timestamp: Date;

    /** Additional information about the action */
    note: string;
  }>;
}

/**
 * Audit Log System
 * Tracks all significant actions in the system for security and compliance
 */
interface AuditLog extends BaseEntity {
  /**
   * Action performed
   * Examples: 'create', 'update', 'delete', 'login', 'export'
   */
  action: string;

  /**
   * Category of the action
   * Examples: 'user', 'content', 'security', 'admin', 'data'
   */
  category: string;

  /** User ID of the person who performed the action */
  userId: string;

  /** IP address from which the action was performed */
  userIp: string;

  /** User agent string from the browser/app */
  userAgent: string;

  /**
   * Type of resource affected
   * Examples: 'user', 'video', 'comment', 'setting'
   */
  resourceType: string;

  /** ID of the specific resource affected */
  resourceId: string;

  /** Previous values before the change (for updates) */
  oldValues?: Record<string, any>;

  /** New values after the change (for updates) */
  newValues?: Record<string, any>;

  /**
   * Whether the action completed successfully
   *
   * Possible values:
   * - success: Action completed successfully
   * - failure: Action failed to complete
   */
  status: 'success' | 'failure';

  /** Error message if status is 'failure' */
  errorMessage?: string;

  /** Additional contextual information */
  metadata: {
    /** Browser name and version */
    browser: string;

    /** Operating system name and version */
    os: string;

    /** Device type (desktop, mobile, tablet) */
    device: string;

    /** Geolocation information */
    location: {
      /** Country code (ISO) */
      country: string;

      /** City name */
      city: string;

      /** IP address (may be anonymized) */
      ip: string;
    };
  };
}

/**
 * API Management System
 * Manages API keys and access for external integrations
 */
interface ApiKey extends BaseEntity {
  /** Human-readable name for the API key */
  name: string;

  /** The API key string used for authentication */
  key: string;

  /** Secret used with the key for authentication */
  secret: string;

  /**
   * Array of permission codes granted to this key
   * Controls what actions can be performed with this key
   */
  permissions: string[];

  /**
   * Domains allowed to use this key
   * Empty array means no restrictions
   */
  allowedOrigins: string[];

  /** Rate limiting configuration */
  rateLimit: {
    /** Whether rate limiting is enabled for this key */
    enabled: boolean;

    /** Maximum number of requests allowed */
    requests: number;

    /** Time window in seconds for the request limit */
    duration: number;
  };

  /**
   * Current status of the API key
   *
   * Possible values:
   * - active: Key is valid and can be used
   * - revoked: Key has been invalidated and cannot be used
   */
  status: 'active' | 'revoked';

  /** When this key was last used */
  lastUsed?: Date;

  /** When this key expires */
  expiresAt?: Date;

  /** Additional information about this key */
  metadata: {
    /** User ID who created this key */
    createdBy: string;

    /** Intended use case for this key */
    purpose: string;

    /** Additional notes about this key */
    notes: string;
  };
}

/**
 * Email Template System
 * Manages reusable email templates for consistent communication
 */
interface EmailTemplate extends BaseEntity {
  /** Human-readable name of the template */
  name: string;

  /** Detailed description of when and how this template is used */
  description: string;

  /** Subject line of the email */
  subject: string;

  /** HTML version of the email body */
  bodyHtml: string;

  /** Plain text version of the email body */
  bodyText: string;

  /**
   * Array of variable placeholders used in the template
   * Examples: ['user_name', 'reset_link', 'verification_code']
   */
  variables: string[];

  /**
   * Category for grouping related templates
   * Examples: 'user', 'billing', 'notification', 'marketing'
   */
  category: string;

  /** Whether this is the default template for its category */
  isDefault: boolean;

  /** URL to a preview image of the rendered template */
  previewImage?: string;

  /** Additional metadata */
  metadata: {
    /** When this template was last tested */
    lastTestedAt?: Date;

    /** Template version number */
    version: number;

    /** User ID of the template author */
    author: string;
  };
}

/**
 * Export all interfaces
 * Makes them available for import in other parts of the application
 */
export type {
  BaseEntity,
  Permission,
  Role,
  User,
  Session,
  NotificationTemplate,
  Notification,
  Conversation,
  Message,
  Call,
  ChatbotSession,
  ChatbotMessage,
  ChatbotIntent,
  Video,
  Analytics,
  Content,
  Translation,
  SystemConfig,
  ModerationRule,
  Report,
  AuditLog,
  ApiKey,
  EmailTemplate
};

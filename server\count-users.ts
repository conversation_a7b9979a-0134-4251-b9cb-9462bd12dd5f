import { UserModel } from './src/models';
import { connectDB } from './src/config/database';

async function countUsers() {
  try {
    await connectDB();
    
    // Count all users
    const count = await UserModel.countDocuments({});
    console.log(`Total users in database: ${count}`);
    
    // List all usernames
    const users = await UserModel.find({}).select('username email');
    console.log('All users:');
    users.forEach((user, index) => {
      console.log(`${index + 1}. ${user.username} (${user.email})`);
    });
    
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

countUsers();

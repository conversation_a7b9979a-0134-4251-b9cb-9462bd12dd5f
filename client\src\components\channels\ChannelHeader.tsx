import React from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { CheckCircle, Share2, MoreHorizontal, Plus } from 'lucide-react';
import { formatNumber } from '@/lib/utils';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useAuth } from '@/context/AuthContext';

interface ChannelHeaderProps {
  channel: {
    id: string;
    name: string;
    displayName: string;
    description: string;
    avatar?: string;
    banner?: string;
    isVerified: boolean;
    ownerId: string;
    stats: {
      subscribers: number;
      totalViews: number;
      videoCount: number;
    };
  };
  activeTab: string;
  onTabChange: (value: string) => void;
  isSubscribed?: boolean;
  onSubscribe?: () => void;
  onShare?: () => void;
}

const ChannelHeader: React.FC<ChannelHeaderProps> = ({
  channel,
  activeTab,
  onTabChange,
  isSubscribed = false,
  onSubscribe,
  onShare,
}) => {
  const { currentUser } = useAuth();
  const isOwner = currentUser?.id === channel.ownerId;

  // Get initials for avatar fallback
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  return (
    <div className="space-y-4">
      {/* Banner */}
      <div className="relative h-32 sm:h-48 w-full bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg overflow-hidden">
        {channel.banner && (
          <img
            src={channel.banner}
            alt={`${channel.displayName} banner`}
            className="w-full h-full object-cover"
          />
        )}
      </div>

      {/* Channel info */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
        <Avatar className="h-20 w-20 border-4 border-background -mt-10 sm:-mt-14 z-10">
          <AvatarImage src={channel.avatar} alt={channel.displayName} />
          <AvatarFallback className="text-xl">{getInitials(channel.displayName)}</AvatarFallback>
        </Avatar>

        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2">
            <h1 className="text-2xl font-bold truncate">{channel.displayName}</h1>
            {channel.isVerified && (
              <CheckCircle className="h-5 w-5 text-blue-500" />
            )}
          </div>
          <div className="text-sm text-muted-foreground">
            <span className="font-medium">@{channel.name}</span> •
            <span className="ml-2">{formatNumber(channel.stats.subscribers)} subscribers</span> •
            <span className="ml-2">{formatNumber(channel.stats.videoCount)} videos</span>
          </div>
          <p className="text-sm mt-1 line-clamp-1">{channel.description}</p>
        </div>

        <div className="flex items-center gap-2 self-end sm:self-center mt-2 sm:mt-0">
          {isOwner && (
            <Button
              onClick={() => window.location.href = `/creator-studio?tab=videos&channelId=${channel.id}`}
              variant="default"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Video
            </Button>
          )}

          {!isOwner && onSubscribe && (
            <Button
              variant={isSubscribed ? "outline" : "default"}
              onClick={onSubscribe}
            >
              {isSubscribed ? 'Subscribed' : 'Subscribe'}
            </Button>
          )}

          {onShare && (
            <Button variant="outline" size="icon" onClick={onShare}>
              <Share2 className="h-4 w-4" />
            </Button>
          )}

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="icon">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {isOwner && (
                <>
                  <DropdownMenuItem asChild>
                    <a href={`/studio/channel/${channel.id}`}>Edit Channel</a>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <a href={`/creator-studio?tab=videos&channelId=${channel.id}`}>Manage Videos</a>
                  </DropdownMenuItem>
                </>
              )}
              <DropdownMenuItem onClick={() => window.open(`/channels/${channel.name}`, '_blank')}>
                Open in new tab
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => navigator.clipboard.writeText(window.location.href)}>
                Copy channel URL
              </DropdownMenuItem>
              {!isOwner && (
                <DropdownMenuItem>Report channel</DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={onTabChange} className="w-full">
        <TabsList className="w-full justify-start">
          <TabsTrigger value="videos">Videos</TabsTrigger>
          <TabsTrigger value="playlists">Playlists</TabsTrigger>
          <TabsTrigger value="about">About</TabsTrigger>
          <TabsTrigger value="community">Community</TabsTrigger>
        </TabsList>
      </Tabs>
    </div>
  );
};

export default ChannelHeader;

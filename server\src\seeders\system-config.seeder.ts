import { SystemConfigModel } from '../models';

/**
 * Seed system configuration data
 */
async function seedSystemConfig() {
  try {
    // Clear existing system config
    await SystemConfigModel.deleteMany({});
    
    // Define system config to seed
    const systemConfig = {
      maintenance: {
        enabled: false,
        message: 'The system is currently undergoing maintenance. Please try again later.',
        allowedIPs: ['127.0.0.1'],
      },
      localization: {
        defaultLanguage: 'en',
        availableLanguages: ['en', 'es', 'fr', 'de', 'hi'],
        dateFormat: 'MM/DD/YYYY',
        timeFormat: 'HH:mm',
        firstDayOfWeek: 0,
        autoTranslation: {
          enabled: true,
          provider: 'google',
          apiSettings: {
            apiKey: 'your-api-key-here',
          },
          minimumConfidence: 70,
          requireReview: true,
          eligibleContentTypes: ['video.title', 'video.description'],
        },
      },
      security: {
        maxLoginAttempts: 5,
        sessionTimeout: 60,
        passwordPolicy: {
          minLength: 8,
          requireSpecialChars: true,
          requireNumbers: true,
          requireUppercase: true,
          expiryDays: 90,
        },
        cors: {
          allowedOrigins: ['*'],
          allowedMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
          allowedHeaders: ['Content-Type', 'Authorization'],
          allowCredentials: true,
        },
        contentSecurityPolicy: {
          enabled: true,
          directives: {
            'default-src': ["'self'"],
            'script-src': ["'self'"],
            'style-src': ["'self'", "'unsafe-inline'"],
            'img-src': ["'self'", 'data:'],
            'connect-src': ["'self'"],
          },
        },
        rateLimit: {
          enabled: true,
          maxRequests: 100,
          windowMs: 60000,
          skipTrustedIPs: true,
          trustedIPs: ['127.0.0.1', '::1'],
        },
      },
      email: {
        templates: {},
        settings: {},
        notifications: {
          types: ['account', 'security', 'marketing'],
          defaultEnabled: true,
        },
        provider: 'smtp',
        smtp: {
          host: 'smtp.example.com',
          port: 587,
          username: 'your-username',
          password: 'your-password',
          encryption: 'tls',
        },
      },
      storage: {
        provider: 'local',
        settings: {
          basePath: './uploads',
        },
        limits: {
          maxFileSize: *********, // 100MB
          allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'video/mp4', 'video/webm'],
          totalSpace: ***********, // 10GB
        },
      },
      cache: {
        enabled: true,
        provider: 'redis',
        ttl: 3600,
        settings: {
          host: 'localhost',
          port: 6379,
        },
      },
      queue: {
        provider: 'redis',
        settings: {
          host: 'localhost',
          port: 6379,
        },
        jobs: {
          concurrency: 5,
          timeout: 300,
          retries: 3,
        },
      },
      monitoring: {
        enabled: true,
        provider: 'prometheus',
        metrics: ['cpu', 'memory', 'requests', 'errors'],
        alerting: {
          enabled: true,
          channels: ['email'],
          thresholds: {
            cpu: 80,
            memory: 80,
            errorRate: 5,
          },
        },
      },
      createdBy: 'system',
      updatedBy: 'system',
    };
    
    // Insert system config
    await SystemConfigModel.create(systemConfig);
    
    console.log('✅ System configuration seeded successfully');
  } catch (error) {
    console.error('❌ Error seeding system configuration:', error);
    throw error;
  }
}

export default seedSystemConfig;

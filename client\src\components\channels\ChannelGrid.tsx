import React from 'react';
import ChannelCard from './ChannelCard';
import { Skeleton } from '@/components/ui/skeleton';

interface Channel {
  id: string;
  name: string;
  displayName: string;
  description: string;
  avatar?: string;
  isVerified: boolean;
  stats: {
    subscribers: number;
    videoCount: number;
  };
  tags?: string[];
}

interface ChannelGridProps {
  channels: Channel[];
  isLoading?: boolean;
  emptyMessage?: string;
  onSubscribe?: (channelId: string) => void;
  subscribedChannels?: string[];
  compact?: boolean;
}

const ChannelGrid: React.FC<ChannelGridProps> = ({
  channels,
  isLoading = false,
  emptyMessage = 'No channels found',
  onSubscribe,
  subscribedChannels = [],
  compact = false,
}) => {
  if (isLoading) {
    return (
      <div className={`grid grid-cols-1 ${compact ? 'sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4' : 'sm:grid-cols-2 md:grid-cols-3'} gap-4`}>
        {Array.from({ length: 8 }).map((_, index) => (
          <div key={index} className="space-y-3">
            <div className="flex items-center space-x-3">
              <Skeleton className="h-12 w-12 rounded-full" />
              <div className="space-y-2">
                <Skeleton className="h-4 w-[150px]" />
                <Skeleton className="h-3 w-[100px]" />
              </div>
            </div>
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-8 w-full" />
          </div>
        ))}
      </div>
    );
  }

  if (channels.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">{emptyMessage}</p>
      </div>
    );
  }

  return (
    <div className={`grid grid-cols-1 ${compact ? 'sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4' : 'sm:grid-cols-2 md:grid-cols-3'} gap-4`}>
      {channels.map(channel => (
        <ChannelCard
          key={channel.id}
          channel={channel}
          isSubscribed={subscribedChannels.includes(channel.id)}
          onSubscribe={onSubscribe ? () => onSubscribe(channel.id) : undefined}
          compact={compact}
        />
      ))}
    </div>
  );
};

export default ChannelGrid;

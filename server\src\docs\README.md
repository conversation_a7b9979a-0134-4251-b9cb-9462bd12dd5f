# API Documentation

This directory contains files related to the API documentation for the LawEngaxe server.

## Overview

The API documentation is generated using OpenAPI/Swagger and is accessible through a web interface at `/documentation`. The documentation is secured and only accessible to users with the appropriate permissions.

## Setup

To set up the documentation system, follow these steps:

1. Install dependencies:
   ```
   npm install
   ```

2. Set up the documentation permission:
   ```
   npm run setup-docs
   ```

3. Start the server:
   ```
   npm run dev
   ```

4. Access the documentation at:
   ```
   http://localhost:3000/documentation
   ```

## Security

The documentation is secured and only accessible to users with the `docs:read` permission. By default, this permission is assigned to the `admin` and `developer` roles.

To access the documentation, you need to:

1. Log in with a user that has the `docs:read` permission
2. The system will redirect you to the documentation if you have the appropriate permission

## Customization

### Adding New Endpoints

When adding new endpoints to the API, make sure to document them properly using the OpenAPI/Swagger annotations. Here's an example:

```typescript
fastify.post('/users', {
  schema: {
    description: 'Create a new user',
    tags: ['Users'],
    summary: 'Create a new user account',
    body: Type.Object({
      username: Type.String({ minLength: 3, maxLength: 50 }),
      email: Type.String({ format: 'email' }),
      password: Type.String({ minLength: 8 }),
      firstName: Type.String(),
      lastName: Type.String(),
      displayName: Type.Optional(Type.String()),
    }),
    response: {
      201: Type.Object({
        success: Type.Boolean(),
        message: Type.String(),
        user: Type.Object({
          id: Type.String(),
          username: Type.String(),
          email: Type.String(),
          firstName: Type.String(),
          lastName: Type.String(),
          displayName: Type.String(),
        }),
      }),
      400: Type.Object({
        success: Type.Boolean(),
        error: Type.Object({
          message: Type.String(),
          code: Type.String(),
        }),
      }),
    },
    security: [{ bearerAuth: [] }],
  },
}, async (request, reply) => {
  // Handler implementation
});
```

### Customizing the UI

You can customize the Swagger UI by modifying the `swaggerPlugin.ts` file. For example, you can change the theme, add custom CSS, or modify the layout.

## Best Practices

1. **Document All Endpoints**: Make sure all endpoints are properly documented with descriptions, parameters, request bodies, and responses.

2. **Use Tags**: Organize endpoints using tags to make the documentation more navigable.

3. **Include Examples**: Provide examples for request bodies and responses to make the documentation more user-friendly.

4. **Document Security Requirements**: Clearly indicate which endpoints require authentication and what permissions are needed.

5. **Keep Documentation Updated**: Update the documentation whenever you make changes to the API.

## Troubleshooting

If you encounter issues with the documentation, check the following:

1. Make sure the user has the `docs:read` permission
2. Check the server logs for any errors
3. Verify that the Swagger plugin is properly configured
4. Ensure that the documentation routes are registered correctly

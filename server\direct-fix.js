/**
 * Direct database fix script
 * This script directly updates all videos in the database to ensure they play properly
 */

const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');

// MongoDB connection string
const MONGODB_URI = 'mongodb://localhost:27017/lawengaxe';

// Connect to MongoDB
mongoose.connect(MONGODB_URI)
  .then(() => {
    console.log('Connected to MongoDB');
    fixAllVideos();
  })
  .catch(err => {
    console.error('Error connecting to MongoDB:', err);
    process.exit(1);
  });

// Define the Video schema
const VideoSchema = new mongoose.Schema({
  id: String,
  title: String,
  description: String,
  url: String,
  thumbnailUrl: String,
  duration: Number,
  userId: String,
  channelId: String,
  visibility: String,
  languages: [{
    code: String,
    name: String,
    isDefault: Boolean,
    url: String
  }],
  source: mongoose.Schema.Types.Mixed // Use Mixed type to handle any structure
});

// Create the Video model
const Video = mongoose.model('Video', VideoSchema);

// Function to fix all videos
async function fixAllVideos() {
  try {
    console.log('Starting to fix all videos...');

    // Get all videos
    const videos = await Video.find({});
    console.log(`Found ${videos.length} videos in the database`);

    let fixedCount = 0;

    // Process each video
    for (const video of videos) {
      let needsUpdate = false;

      // Fix 1: Ensure video has a URL
      if (!video.url || video.url === '') {
        console.log(`Video ${video.id} has no URL, setting to video ID`);
        video.url = video.id;
        needsUpdate = true;
      }

      // Fix 2: Ensure video has a source
      if (!video.source) {
        console.log(`Video ${video.id} has no source, creating default source`);
        video.source = {
          type: 'embed',
          originalUrl: video.url,
          platform: 'engaxe',
          externalId: video.url
        };
        needsUpdate = true;
      }

      // Fix 3: Ensure source has all required fields
      if (video.source) {
        if (!video.source.type) {
          video.source.type = 'embed';
          needsUpdate = true;
        }
        if (!video.source.originalUrl) {
          video.source.originalUrl = video.url;
          needsUpdate = true;
        }
        if (!video.source.platform) {
          video.source.platform = 'engaxe';
          needsUpdate = true;
        }
        if (!video.source.externalId) {
          video.source.externalId = video.url;
          needsUpdate = true;
        }
      }

      // Fix 4: Ensure video has languages
      if (!video.languages || video.languages.length === 0) {
        console.log(`Video ${video.id} has no languages, adding default language`);
        video.languages = [{
          code: 'en',
          name: 'English',
          isDefault: true,
          url: video.url
        }];
        needsUpdate = true;
      }

      // Fix 5: Ensure all languages have URLs
      if (video.languages && video.languages.length > 0) {
        for (let i = 0; i < video.languages.length; i++) {
          if (!video.languages[i].url || video.languages[i].url === '') {
            console.log(`Language ${i} for video ${video.id} has no URL, setting to video URL`);
            video.languages[i].url = video.url;
            needsUpdate = true;
          }
        }
      }

      // Save the video if it was updated
      if (needsUpdate) {
        await video.save();
        fixedCount++;
        console.log(`Fixed and saved video ${video.id}`);
      }
    }

    console.log(`Fixed ${fixedCount} out of ${videos.length} videos`);

    // Disconnect from MongoDB
    mongoose.disconnect()
      .then(() => {
        console.log('Disconnected from MongoDB');
        process.exit(0);
      })
      .catch(err => {
        console.error('Error disconnecting from MongoDB:', err);
        process.exit(1);
      });
  } catch (error) {
    console.error('Error fixing videos:', error);
    process.exit(1);
  }
}

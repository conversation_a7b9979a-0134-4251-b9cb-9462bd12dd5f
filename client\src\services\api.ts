import axios from 'axios';
import { API_BASE_URL } from '../config';

// Create an axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  // Add timeout to prevent hanging requests
  timeout: 10000, // 10 seconds
});

// Add a request interceptor to include the auth token in requests
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('lawengaxe-token');
    console.log('Token from localStorage:', token ? 'Token exists' : 'No token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
      console.log('Added Authorization header');
    } else {
      console.warn('No auth token found in localStorage');
      // Don't add a default token - let the server handle unauthenticated requests
      console.log('No Authorization header added - user is not authenticated');
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Add a response interceptor to handle errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    // Log the error for debugging
    console.error('API Error:', error);

    // Add more detailed error information if available
    if (error.response) {
      console.error('Response data:', error.response.data);
      console.error('Response status:', error.response.status);
      console.error('Response headers:', error.response.headers);
    } else if (error.request) {
      console.error('No response received:', error.request);
    } else {
      console.error('Error message:', error.message);
    }

    return Promise.reject(error);
  }
);

// Auth API calls
const authAPI = {
  // Login user
  login: async (email: string, password: string) => {
    const response = await api.post('/auth/login', { email, password });
    return response.data;
  },

  // Login with Engaxe
  loginWithEngaxe: async (username: string, password: string) => {
    const response = await api.post('/auth/engaxe/login', { username, password });
    return response.data;
  },

  // Register user
  register: async (userData: {
    username: string;
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    displayName?: string;
  }) => {
    const response = await api.post('/users/register', userData);
    return response.data;
  },

  // Get current user profile
  getCurrentUser: async () => {
    const response = await api.get('/auth/me');
    return response.data;
  },

  // Refresh token
  refreshToken: async (refreshToken: string) => {
    const response = await api.post('/auth/refresh-token', { refreshToken });
    return response.data;
  },
};

// Import API services
import { channelAPI } from './channel-api';
import { videoAPI } from './video-api';
import { metadataAPI } from './metadata-api';
import { reportAPI } from './report-api';

// Export all API services
export { authAPI, channelAPI, videoAPI, metadataAPI, reportAPI };

export default api;

.messages-page {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 60px);
  background-color: #1a242f;
  color: #ecf0f1;
}

.back-button-container {
  padding: 15px;
  background-color: #1e2a38;
  border-bottom: 1px solid #2c3e50;
}

.back-button {
  display: inline-flex;
  align-items: center;
  padding: 8px 16px;
  background-color: #3498db;
  color: white;
  text-decoration: none;
  border-radius: 5px;
  font-weight: bold;
  transition: background-color 0.2s, transform 0.1s;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.back-button:hover {
  background-color: #2980b9;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.back-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.2);
}

.back-button i {
  margin-right: 8px;
}

.messages-main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.messages-sidebar {
  width: 320px;
  border-right: 1px solid #2c3e50;
  display: flex;
  flex-direction: column;
  background-color: #1e2a38;
}

.messages-header {
  padding: 15px;
  border-bottom: 1px solid #2c3e50;
}

.messages-header h2 {
  margin: 0;
  font-size: 18px;
}

.search-container {
  padding: 15px;
  border-bottom: 1px solid #2c3e50;
}

.conversations-list {
  flex-grow: 1;
  overflow-y: auto;
}

.conversation-item {
  display: flex;
  align-items: center;
  padding: 15px;
  cursor: pointer;
  border-bottom: 1px solid #2c3e50;
  transition: background-color 0.2s;
}

.conversation-item:hover {
  background-color: #2c3e50;
}

.conversation-item.active {
  background-color: #34495e;
}

.conversation-avatar {
  position: relative;
  width: 50px;
  height: 50px;
  margin-right: 15px;
  flex-shrink: 0;
}

.conversation-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.status-indicator {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid #1e2a38;
}

.status-indicator.online {
  background-color: #2ecc71;
}

.status-indicator.offline {
  background-color: #7f8c8d;
}

.conversation-info {
  flex-grow: 1;
  overflow: hidden;
}

.conversation-name {
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.conversation-subject {
  font-size: 12px;
  color: #bdc3c7;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.conversation-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-left: 10px;
}

.conversation-time {
  font-size: 12px;
  color: #7f8c8d;
}

.unread-badge {
  background-color: #e74c3c;
  color: white;
  font-size: 12px;
  font-weight: bold;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 5px;
}

.messages-content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.conversation-header {
  display: flex;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #2c3e50;
  background-color: #1e2a38;
}

.conversation-status {
  font-size: 12px;
  color: #7f8c8d;
}

.conversation-actions {
  margin-left: auto;
  display: flex;
  align-items: center;
}

.language-selector {
  position: relative;
}

.language-select {
  padding: 6px 10px;
  background-color: #2c3e50;
  color: #ecf0f1;
  border: 1px solid #34495e;
  border-radius: 5px;
  font-size: 14px;
  cursor: pointer;
  appearance: none;
  padding-right: 30px;
  transition: all 0.3s;
}

.language-select:hover {
  background-color: #34495e;
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.language-select:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.3);
}

/* Add a glow effect when translating */
.language-select[data-translating="true"] {
  border-color: #3498db;
  box-shadow: 0 0 10px rgba(52, 152, 219, 0.5);
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.5);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(52, 152, 219, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(52, 152, 219, 0);
  }
}

.language-selector::after {
  content: '▼';
  font-size: 10px;
  color: #7f8c8d;
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
}

.translation-indicator {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: rgba(52, 152, 219, 0.1);
  border-left: 3px solid #3498db;
  margin-bottom: 15px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.translation-indicator:hover {
  background-color: rgba(52, 152, 219, 0.2);
}

.translation-icon {
  font-size: 16px;
  margin-right: 8px;
}

.translation-text {
  font-size: 13px;
  color: #ecf0f1;
}

.translation-loading {
  margin-left: auto;
  font-size: 12px;
  color: #7f8c8d;
  font-style: italic;
  position: relative;
  padding-right: 20px;
}

.translation-loading:after {
  content: "";
  position: absolute;
  right: 0;
  top: 50%;
  width: 16px;
  height: 16px;
  margin-top: -8px;
  border-radius: 50%;
  border: 2px solid rgba(52, 152, 219, 0.3);
  border-top-color: #3498db;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.messages-container {
  flex-grow: 1;
  padding: 15px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  background-color: #1a242f;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%232c3e50' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E");
}

.messages-list {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 10px 0;
}

.message {
  display: flex;
  margin-bottom: 15px;
  max-width: 70%;
  position: relative;
}

.message.sent {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.message.received {
  align-self: flex-start;
}

.message-avatar {
  width: 40px;
  height: 40px;
  margin: 0 10px;
  flex-shrink: 0;
}

.message-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.message-content {
  background-color: #2c3e50;
  padding: 10px 15px;
  border-radius: 15px;
  position: relative;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.message.sent .message-content {
  background-color: #3498db;
  border-top-right-radius: 5px;
}

.message.received .message-content {
  border-top-left-radius: 5px;
}

/* Add chat bubble tails */
.message.sent .message-content:after {
  content: '';
  position: absolute;
  top: 0;
  right: -10px;
  width: 0;
  height: 0;
  border: 10px solid transparent;
  border-left-color: #3498db;
  border-right: 0;
  border-top: 0;
  margin-top: 0;
}

.message.received .message-content:after {
  content: '';
  position: absolute;
  top: 0;
  left: -10px;
  width: 0;
  height: 0;
  border: 10px solid transparent;
  border-right-color: #2c3e50;
  border-left: 0;
  border-top: 0;
  margin-top: 0;
}

.message-sender {
  font-weight: bold;
  font-size: 12px;
  margin-bottom: 5px;
  color: #e67e22;
}

.message-text {
  word-wrap: break-word;
  font-size: 14px;
  line-height: 1.4;
  position: relative;
}

.message-text[data-translated="true"] {
  padding-left: 18px;
}

.message-text[data-translated="true"]::before {
  content: "🌐";
  position: absolute;
  left: 0;
  top: 2px;
  font-size: 12px;
  opacity: 0.7;
}

.message-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 5px;
}

.message-time {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.6);
}

.message-translate-options {
  display: flex;
  align-items: center;
}

.message-translate-button {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  padding: 2px 5px;
  border-radius: 3px;
  transition: all 0.2s;
}

.message-translate-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
}

.message-translate-dropdown {
  position: absolute;
  background-color: #1e2a38;
  border: 1px solid #2c3e50;
  border-radius: 5px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  width: 180px;
  overflow: hidden;
}

.message-translate-dropdown-header {
  padding: 8px 12px;
  font-size: 12px;
  font-weight: bold;
  border-bottom: 1px solid #2c3e50;
  color: #ecf0f1;
}

.message-translate-dropdown-option {
  padding: 8px 12px;
  font-size: 13px;
  cursor: pointer;
  transition: background-color 0.2s;
  color: #ecf0f1;
}

.message-translate-dropdown-option:hover {
  background-color: #2c3e50;
}

.typing-indicator {
  align-self: flex-start;
  font-size: 12px;
  color: #7f8c8d;
  font-style: italic;
  margin-bottom: 10px;
}

.message-input-container {
  display: flex;
  padding: 15px;
  border-top: 1px solid #2c3e50;
  background-color: #1e2a38;
}

.message-input {
  flex-grow: 1;
  padding: 10px 15px;
  border: 1px solid #2c3e50;
  border-radius: 5px;
  background-color: #1a242f;
  color: #fff;
  font-size: 14px;
  margin-right: 10px;
}

.message-input:focus {
  outline: none;
  border-color: #3498db;
}

.send-button {
  padding: 10px 20px;
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s;
}

.send-button:hover {
  background-color: #2980b9;
}

.no-conversation-selected {
  flex-grow: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.no-conversation-message {
  text-align: center;
  color: #7f8c8d;
}

.no-conversation-message h3 {
  margin-bottom: 10px;
}

.loading, .error, .no-conversations, .no-messages {
  padding: 20px;
  text-align: center;
  color: #7f8c8d;
}

.error {
  color: #e74c3c;
}

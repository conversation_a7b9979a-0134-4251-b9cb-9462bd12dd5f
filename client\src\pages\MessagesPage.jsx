import React, { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import UserSearch from '../components/messaging/UserSearch';
import axios from 'axios';
import { API_BASE_URL, WS_URL, TYPING_INDICATOR_TIMEOUT } from '../config';
import '../components/messaging/UserSearch.css';
import './MessagesPage.css';

// For debugging - set to false to disable debug logs
const DEBUG = false;

const MessagesPage = () => {
  const { currentUser, token } = useAuth();
  const [conversations, setConversations] = useState([]);
  const [activeConversation, setActiveConversation] = useState(null);
  const [messages, setMessages] = useState([]);
  const [messageInput, setMessageInput] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [typing, setTyping] = useState(false);
  const [currentLanguage, setCurrentLanguage] = useState('en');
  const [translatedMessages, setTranslatedMessages] = useState({});
  const [isTranslating, setIsTranslating] = useState(false);
  const messagesEndRef = useRef(null);
  const socketRef = useRef(null);
  const typingTimeoutRef = useRef(null);

  if (DEBUG) console.log('MessagesPage: Initialized with token:', token ? 'Token exists' : 'No token', 'User:', currentUser?.username);

  // Connect to WebSocket on component mount
  useEffect(() => {
    if (token) {
      if (DEBUG) console.log('MessagesPage: Token available, connecting to WebSocket');
      connectWebSocket();
    } else {
      if (DEBUG) console.log('MessagesPage: No token available for WebSocket');
    }

    return () => {
      if (socketRef.current) {
        socketRef.current.close();
      }
    };
  }, [token]);

  // Fetch conversations on component mount
  useEffect(() => {
    if (token) {
      if (DEBUG) console.log('MessagesPage: Token available, fetching conversations');
      fetchConversations();
    } else {
      if (DEBUG) console.log('MessagesPage: No token available for fetching conversations');
    }
  }, [token]);

  // Fetch messages when active conversation changes
  useEffect(() => {
    if (activeConversation) {
      fetchMessages(activeConversation.id);
    }
  }, [activeConversation]);

  // Translate messages when language changes or when messages change
  useEffect(() => {
    if (currentLanguage !== 'en' && messages.length > 0) {
      handleTranslateConversation(currentLanguage);
    }
  }, [currentLanguage, messages.length]);

  // Scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Connect to WebSocket
  const connectWebSocket = () => {
    if (DEBUG) console.log('MessagesPage: Connecting to WebSocket at', WS_URL);
    const socket = new WebSocket(WS_URL);

    socket.onopen = () => {
      console.log('WebSocket connected');
      if (DEBUG) console.log('MessagesPage: WebSocket connected, authenticating with token');
      // Authenticate with token
      socket.send(JSON.stringify({
        type: 'auth',
        payload: {
          token,
        },
      }));
    };

    socket.onmessage = (event) => {
      const data = JSON.parse(event.data);
      console.log('WebSocket message:', data);

      switch (data.type) {
        case 'auth_success':
          console.log('Authentication successful');
          break;

        case 'auth_error':
          console.error('Authentication failed:', data.payload.message);
          break;

        case 'message_sent':
          // Add message to UI if it's for the active conversation
          if (activeConversation && data.payload.message.conversationId === activeConversation.id) {
            setMessages((prevMessages) => [...prevMessages, data.payload.message]);
          }
          break;

        case 'new_message':
          // Add message to UI if it's for the active conversation
          if (activeConversation && data.payload.message.conversationId === activeConversation.id) {
            setMessages((prevMessages) => [...prevMessages, data.payload.message]);

            // Send read receipt
            socket.send(JSON.stringify({
              type: 'read_receipt',
              payload: {
                messageId: data.payload.message.id,
              },
            }));
          }

          // Refresh conversations to update unread count
          fetchConversations();
          break;

        case 'typing_indicator':
          // Show typing indicator if it's for the active conversation
          if (activeConversation && data.payload.conversationId === activeConversation.id) {
            setTyping(data.payload.isTyping);
          }
          break;

        case 'user_status':
          // Update user status in conversations
          fetchConversations();
          break;

        case 'error':
          console.error('WebSocket error:', data.payload.message);
          break;
      }
    };

    socket.onclose = () => {
      console.log('WebSocket disconnected');
      // Try to reconnect after 5 seconds
      setTimeout(connectWebSocket, 5000);
    };

    socket.onerror = (error) => {
      console.error('WebSocket error:', error);
    };

    socketRef.current = socket;
  };

  // Fetch conversations
  const fetchConversations = async () => {
    try {
      setLoading(true);
      if (DEBUG) console.log('MessagesPage: Fetching conversations with token:', token ? 'Token exists' : 'No token');

      const response = await axios.get(`${API_BASE_URL}/conversations`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (DEBUG) console.log('MessagesPage: Conversations response:', response.data);

      if (response.data.success) {
        setConversations(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching conversations:', error);
      if (DEBUG) console.log('MessagesPage: Error details:', error.response?.data || error.message);
      setError('Failed to load conversations');
    } finally {
      setLoading(false);
    }
  };

  // Fetch messages for a conversation
  const fetchMessages = async (conversationId) => {
    try {
      setLoading(true);
      const response = await axios.get(`${API_BASE_URL}/messages/conversation/${conversationId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.data.success) {
        setMessages(response.data.data);
      }

      // Mark conversation as read
      await axios.post(`${API_BASE_URL}/conversations/${conversationId}/read`, {}, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      // Refresh conversations to update unread count
      fetchConversations();
    } catch (error) {
      console.error('Error fetching messages:', error);
      setError('Failed to load messages');
    } finally {
      setLoading(false);
    }
  };

  // Create a new conversation
  const createConversation = async (selectedUser) => {
    try {
      setLoading(true);
      setError(null);

      if (DEBUG) console.log('Creating conversation with user:', selectedUser);

      // Check if conversation already exists
      const existingConversation = conversations.find(
        (conv) =>
          (conv.creatorId === selectedUser.id && conv.userId === currentUser.id) ||
          (conv.userId === selectedUser.id && conv.creatorId === currentUser.id)
      );

      if (existingConversation) {
        if (DEBUG) console.log('Found existing conversation:', existingConversation);
        setActiveConversation(existingConversation);
        return;
      }

      // Determine if current user is creator or regular user
      const isCreator = currentUser.roles?.includes('creator');

      if (DEBUG) console.log('Current user is creator:', isCreator);

      // Prepare request data with all required fields
      const requestData = {
        creatorId: isCreator ? currentUser.id : selectedUser.id,
        userId: isCreator ? selectedUser.id : currentUser.id,
        subject: `Chat with ${selectedUser.displayName || selectedUser.username}`,
        // Add optional fields with default values
        settings: {
          notifications: true,
          readReceipts: true
        },
        metadata: {
          tags: [],
          customFields: {}
        }
      };

      if (DEBUG) console.log('Creating conversation with data:', requestData);

      const response = await axios.post(
        `${API_BASE_URL}/conversations`,
        requestData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (DEBUG) console.log('Conversation creation response:', response.data);

      if (response.data.success) {
        // Refresh conversations
        await fetchConversations();

        // Set active conversation
        setActiveConversation(response.data.data);

        // Fetch messages for the new conversation
        await fetchMessages(response.data.data.id);
      } else {
        setError(response.data.message || 'Failed to create conversation');
      }
    } catch (error) {
      console.error('Error creating conversation:', error);
      if (DEBUG) console.log('Error details:', error.response?.data || error.message);
      setError('Failed to create conversation: ' + (error.response?.data?.message || error.message));
    } finally {
      setLoading(false);
    }
  };

  // Send a message
  const sendMessage = () => {
    if (!activeConversation || !messageInput.trim()) {
      return;
    }

    // Send message via WebSocket
    socketRef.current.send(JSON.stringify({
      type: 'message',
      payload: {
        conversationId: activeConversation.id,
        content: messageInput.trim(),
        contentType: 'text',
        clientId: Date.now().toString(),
      },
    }));

    // Clear input
    setMessageInput('');
  };

  // Handle message input change
  const handleMessageInputChange = (e) => {
    setMessageInput(e.target.value);

    // Send typing indicator
    if (activeConversation && socketRef.current) {
      socketRef.current.send(JSON.stringify({
        type: 'typing',
        payload: {
          conversationId: activeConversation.id,
          isTyping: true,
        },
      }));

      // Clear previous timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }

      // Set timeout to send stop typing indicator
      typingTimeoutRef.current = setTimeout(() => {
        if (socketRef.current) {
          socketRef.current.send(JSON.stringify({
            type: 'typing',
            payload: {
              conversationId: activeConversation.id,
              isTyping: false,
            },
          }));
        }
      }, TYPING_INDICATOR_TIMEOUT);
    }
  };

  // Handle user selection from search
  const handleSelectUser = (selectedUser) => {
    if (selectedUser.conversationId) {
      // If user has an existing conversation, set it as active
      const conversation = conversations.find(
        (conv) => conv.id === selectedUser.conversationId
      );
      if (conversation) {
        setActiveConversation(conversation);
      }
    } else {
      // Create a new conversation with the selected user
      createConversation(selectedUser);
    }
  };

  // Scroll to bottom of messages
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Format time for display
  const formatTime = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Translate a single message
  const translateMessage = async (message, targetLanguage) => {
    // Get message content and ID
    const content = message.content;
    const messageId = message.id || `msg_${message.createdAt}_${message.senderId}`;

    if (targetLanguage === 'en') {
      // If target is English, just use the original content
      return content;
    }

    try {
      // In a real app, you would call your translation API here
      // For now, we'll simulate translation with a prefix
      let translatedContent;

      // Common phrases in different languages
      const commonPhrases = {
        'hello': {
          'hi': 'नमस्ते',
          'mr': 'नमस्कार',
          'gu': 'નમસ્તે',
          'ta': 'வணக்கம்',
          'te': 'హలో',
          'bn': 'হ্যালো',
          'kn': 'ಹಲೋ',
          'ml': 'ഹലോ',
          'fr': 'Bonjour',
          'es': '¡Hola!',
          'de': 'Hallo',
          'zh': '你好',
          'ja': 'こんにちは'
        },
        'how are you': {
          'hi': 'आप कैसे हैं?',
          'mr': 'तुम्ही कसे आहात?',
          'gu': 'તમે કેમ છો?',
          'ta': 'நீங்கள் எப்படி இருக்கிறீர்கள்?',
          'te': 'మీరు ఎలా ఉన్నారు?',
          'bn': 'আপনি কেমন আছেন?',
          'kn': 'ನೀವು ಹೇಗಿದ್ದೀರಿ?',
          'ml': 'സുഖമാണോ?',
          'fr': 'Comment allez-vous?',
          'es': '¿Cómo estás?',
          'de': 'Wie geht es dir?',
          'zh': '你好吗？',
          'ja': 'お元気ですか？'
        },
        'good': {
          'hi': 'अच्छा',
          'mr': 'चांगले',
          'gu': 'સારું',
          'ta': 'நல்லது',
          'te': 'మంచిది',
          'bn': 'ভালো',
          'kn': 'ಒಳ್ಳೆಯದು',
          'ml': 'നല്ലത്',
          'fr': 'Bien',
          'es': 'Bien',
          'de': 'Gut',
          'zh': '好的',
          'ja': '良い'
        },
        'i am': {
          'hi': 'मैं हूँ',
          'mr': 'मी आहे',
          'gu': 'હું છું',
          'ta': 'நான்',
          'te': 'నేను',
          'bn': 'আমি',
          'kn': 'ನಾನು',
          'ml': 'ഞാൻ',
          'fr': 'Je suis',
          'es': 'Yo soy',
          'de': 'Ich bin',
          'zh': '我是',
          'ja': '私は'
        },
        'what are you doing': {
          'hi': 'आप क्या कर रहे हैं?',
          'mr': 'तुम्ही काय करत आहात?',
          'gu': 'તમે શું કરી રહ્યા છો?',
          'ta': 'நீங்கள் என்ன செய்கிறீர்கள்?',
          'te': 'మీరు ఏమి చేస్తున్నారు?',
          'bn': 'আপনি কি করছেন?',
          'kn': 'ನೀವು ಏನು ಮಾಡುತ್ತಿದ್ದೀರಿ?',
          'ml': 'നിങ്ങൾ എന്താണ് ചെയ്യുന്നത്?',
          'fr': 'Que fais-tu?',
          'es': '¿Qué estás haciendo?',
          'de': 'Was machst du?',
          'zh': '你在做什么？',
          'ja': '何をしていますか？'
        },
        'i\'m happy': {
          'hi': 'मैं खुश हूँ',
          'mr': 'मी खुश आहे',
          'gu': 'હું ખુશ છું',
          'ta': 'நான் மகிழ்ச்சியாக இருக்கிறேன்',
          'te': 'నేను సంతోషంగా ఉన్నాను',
          'bn': 'আমি খুশি',
          'kn': 'ನಾನು ಸಂತೋಷವಾಗಿದ್ದೇನೆ',
          'ml': 'ഞാൻ സന്തോഷവാനാണ്',
          'fr': 'Je suis heureux',
          'es': 'Estoy feliz',
          'de': 'Ich bin glücklich',
          'zh': '我很开心',
          'ja': '私は幸せです'
        },
        'always smile': {
          'hi': 'हमेशा मुस्कुराते रहें',
          'mr': 'नेहमी हसत रहा',
          'gu': 'હંમેશા સ્માઈલ',
          'ta': 'எப்போதும் புன்னகை',
          'te': 'ఎప్పుడూ నవ్వుతూ ఉండండి',
          'bn': 'সবসময় হাসুন',
          'kn': 'ಯಾವಾಗಲೂ ನಗು',
          'ml': 'എപ്പോഴും പുഞ്ചിരിക്കുക',
          'fr': 'Souriez toujours',
          'es': 'Siempre sonríe',
          'de': 'Immer lächeln',
          'zh': '永远微笑',
          'ja': 'いつも笑顔で'
        }
      };

      // Check if the content contains any of the common phrases
      const lowerContent = content.toLowerCase();
      let matched = false;

      // Try to match common phrases
      for (const [phrase, translations] of Object.entries(commonPhrases)) {
        if (lowerContent.includes(phrase) && translations[targetLanguage]) {
          translatedContent = translations[targetLanguage];
          matched = true;
          break;
        }
      }

      // If no match found, use a generic translation
      if (!matched) {
        // For languages with specific prefixes
        const prefixes = {
          'hi': '[हिंदी]',
          'mr': '[मराठी]',
          'gu': '[ગુજરાતી]',
          'ta': '[தமிழ்]',
          'te': '[తెలుగు]',
          'bn': '[বাংলা]',
          'kn': '[ಕನ್ನಡ]',
          'ml': '[മലയാളം]'
        };

        const prefix = prefixes[targetLanguage] || `[${targetLanguage.toUpperCase()}]`;
        translatedContent = `${prefix} ${content}`;
      }

      // Store the translated message
      setTranslatedMessages(prev => ({
        ...prev,
        [messageId]: {
          ...prev[messageId],
          [targetLanguage]: translatedContent
        }
      }));

      return translatedContent;
    } catch (error) {
      console.error('Error translating message:', error);
      return content; // Return original content on error
    }
  };

  // Translate all messages in the conversation
  const handleTranslateConversation = async (targetLanguage) => {
    console.log('Translating conversation to:', targetLanguage);
    setCurrentLanguage(targetLanguage);

    if (targetLanguage === 'en') {
      // If target is English, reset translations
      setTranslatedMessages({});
      return;
    }

    setIsTranslating(true);

    try {
      // Translate each message
      const translations = { ...translatedMessages };

      for (const message of messages) {
        // Create a fallback ID if message doesn't have one
        const messageId = message.id || `msg_${message.createdAt}_${message.senderId}`;

        if (!translations[messageId] || !translations[messageId][targetLanguage]) {
          console.log(`Translating message ${messageId} to ${targetLanguage}`);

          // Use the same translation function we defined earlier
          const translatedContent = await translateMessage(message, targetLanguage);

          translations[messageId] = {
            ...translations[messageId],
            [targetLanguage]: translatedContent
          };
        }
      }

      console.log('Translated messages:', translations);
      setTranslatedMessages(translations);
    } catch (error) {
      console.error('Error translating conversation:', error);
    } finally {
      setIsTranslating(false);
    }
  };

  // Show translation options for a single message
  const handleTranslateMessage = (message, event) => {
    // Create a dropdown menu with language options
    const languages = [
      // Indian languages
      { code: 'hi', name: 'Hindi 🇮🇳' },
      { code: 'mr', name: 'Marathi 🇮🇳' },
      { code: 'gu', name: 'Gujarati 🇮🇳' },
      { code: 'ta', name: 'Tamil 🇮🇳' },
      { code: 'te', name: 'Telugu 🇮🇳' },
      { code: 'bn', name: 'Bengali 🇮🇳' },
      { code: 'kn', name: 'Kannada 🇮🇳' },
      { code: 'ml', name: 'Malayalam 🇮🇳' },
      // Foreign languages
      { code: 'es', name: 'Spanish 🇪🇸' },
      { code: 'fr', name: 'French 🇫🇷' },
      { code: 'de', name: 'German 🇩🇪' },
      { code: 'zh', name: 'Chinese 🇨🇳' },
      { code: 'ja', name: 'Japanese 🇯🇵' },
      { code: 'ru', name: 'Russian 🇷🇺' },
      { code: 'ar', name: 'Arabic 🇸🇦' }
    ];

    // Create a temporary dropdown
    const dropdown = document.createElement('div');
    dropdown.className = 'message-translate-dropdown';

    // Add a header
    const header = document.createElement('div');
    header.className = 'message-translate-dropdown-header';
    header.textContent = 'Translate to:';
    dropdown.appendChild(header);

    // Add language options
    languages.forEach(lang => {
      const option = document.createElement('div');
      option.className = 'message-translate-dropdown-option';
      option.textContent = lang.name;
      option.onclick = async () => {
        // Translate the message
        await translateMessage(message, lang.code);

        // Switch to the language
        setCurrentLanguage(lang.code);

        // Remove the dropdown
        document.body.removeChild(dropdown);
      };
      dropdown.appendChild(option);
    });

    // Position the dropdown near the translate button
    const rect = event.target.getBoundingClientRect();
    dropdown.style.top = `${rect.bottom + window.scrollY}px`;
    dropdown.style.left = `${rect.left + window.scrollX}px`;

    // Add the dropdown to the body
    document.body.appendChild(dropdown);

    // Remove the dropdown when clicking outside
    const handleClickOutside = (e) => {
      if (!dropdown.contains(e.target) && e.target !== event.target) {
        document.body.removeChild(dropdown);
        document.removeEventListener('click', handleClickOutside);
      }
    };

    // Add a small delay to prevent immediate closing
    setTimeout(() => {
      document.addEventListener('click', handleClickOutside);
    }, 100);
  };

  // Get message content (original or translated)
  const getMessageContent = (message) => {
    // Create a fallback ID if message doesn't have one
    const messageId = message.id || `msg_${message.createdAt}_${message.senderId}`;

    if (currentLanguage === 'en' || !translatedMessages[messageId]) {
      return message.content;
    }

    return translatedMessages[messageId][currentLanguage] || message.content;
  };

  // Get user display name
  const getUserDisplayName = (user) => {
    return user?.displayName || user?.username || `${user?.firstName || ''} ${user?.lastName || ''}`.trim();
  };

  // Get user avatar or placeholder
  const getUserAvatar = (user) => {
    return user?.avatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(getUserDisplayName(user) || 'User')}&background=random`;
  };

  // Get other participant in conversation
  const getOtherParticipant = (conversation) => {
    if (!conversation) return null;

    try {
      const isCreator = currentUser.id === conversation.creatorId;

      // Check if user and creator objects exist
      if (isCreator && !conversation.user) {
        if (DEBUG) console.log('User object missing in conversation:', conversation);
        return {
          id: conversation.userId,
          username: 'Unknown User',
          displayName: 'Unknown User',
          presence: { isOnline: false, lastActiveAt: new Date().toISOString() }
        };
      }

      if (!isCreator && !conversation.creator) {
        if (DEBUG) console.log('Creator object missing in conversation:', conversation);
        return {
          id: conversation.creatorId,
          username: 'Unknown Creator',
          displayName: 'Unknown Creator',
          presence: { isOnline: false, lastActiveAt: new Date().toISOString() }
        };
      }

      return isCreator ? conversation.user : conversation.creator;
    } catch (error) {
      console.error('Error getting other participant:', error);
      return {
        username: 'Unknown User',
        displayName: 'Unknown User',
        presence: { isOnline: false, lastActiveAt: new Date().toISOString() }
      };
    }
  };

  return (
    <div className="messages-page">
      {/* Back button */}
      <div className="back-button-container">
        <Link to="/" className="back-button">
          <i className="fas fa-arrow-left"></i> Back to Home
        </Link>
      </div>

      <div className="messages-main-content">
        <div className="messages-sidebar">
          <div className="messages-header">
            <h2>Messages</h2>
          </div>

          <div className="search-container">
            <UserSearch onSelectUser={handleSelectUser} />
          </div>

          <div className="conversations-list">
            {loading && conversations.length === 0 ? (
              <div className="loading">Loading conversations...</div>
            ) : error ? (
              <div className="error">{error}</div>
            ) : conversations.length === 0 ? (
              <div className="no-conversations">No conversations yet</div>
            ) : (
            conversations.map((conversation) => {
              const otherParticipant = getOtherParticipant(conversation);
              const unreadCount = currentUser.id === conversation.creatorId
                ? conversation.creatorUnreadCount
                : conversation.userUnreadCount;

              return (
                <div
                  key={conversation.id}
                  className={`conversation-item ${activeConversation?.id === conversation.id ? 'active' : ''}`}
                  onClick={() => setActiveConversation(conversation)}
                >
                  <div className="conversation-avatar">
                    <img src={getUserAvatar(otherParticipant)} alt={getUserDisplayName(otherParticipant)} />
                    <span className={`status-indicator ${otherParticipant?.presence?.isOnline ? 'online' : 'offline'}`}></span>
                  </div>
                  <div className="conversation-info">
                    <div className="conversation-name">{getUserDisplayName(otherParticipant)}</div>
                    <div className="conversation-subject">{conversation.subject}</div>
                  </div>
                  <div className="conversation-meta">
                    <div className="conversation-time">{formatTime(conversation.lastMessageAt)}</div>
                    {unreadCount > 0 && (
                      <div className="unread-badge">{unreadCount}</div>
                    )}
                  </div>
                </div>
              );
            })
          )}
        </div>
      </div>

      <div className="messages-content">
        {activeConversation ? (
          <>
            <div className="conversation-header">
              <div className="conversation-avatar">
                <img
                  src={getUserAvatar(getOtherParticipant(activeConversation))}
                  alt={getUserDisplayName(getOtherParticipant(activeConversation))}
                />
                <span className={`status-indicator ${getOtherParticipant(activeConversation)?.presence?.isOnline ? 'online' : 'offline'}`}></span>
              </div>
              <div className="conversation-info">
                <div className="conversation-name">{getUserDisplayName(getOtherParticipant(activeConversation))}</div>
                <div className="conversation-status">
                  {getOtherParticipant(activeConversation)?.presence?.isOnline ? 'Online' : 'Offline'}
                </div>
              </div>
              <div className="conversation-actions">
                <div className="language-selector">
                  <select
                    className="language-select"
                    onChange={(e) => handleTranslateConversation(e.target.value)}
                    value={currentLanguage || 'en'}
                    data-translating={isTranslating.toString()}
                  >
                    <option value="en">🇺🇸 English</option>
                    <optgroup label="Indian Languages">
                      <option value="hi">🇮🇳 Hindi</option>
                      <option value="mr">🇮🇳 Marathi</option>
                      <option value="gu">🇮🇳 Gujarati</option>
                      <option value="ta">🇮🇳 Tamil</option>
                      <option value="te">🇮🇳 Telugu</option>
                      <option value="bn">🇮🇳 Bengali</option>
                      <option value="kn">🇮🇳 Kannada</option>
                      <option value="ml">🇮🇳 Malayalam</option>
                    </optgroup>
                    <optgroup label="Foreign Languages">
                      <option value="es">🇪🇸 Spanish</option>
                      <option value="fr">🇫🇷 French</option>
                      <option value="de">🇩🇪 German</option>
                      <option value="zh">🇨🇳 Chinese</option>
                      <option value="ja">🇯🇵 Japanese</option>
                      <option value="ru">🇷🇺 Russian</option>
                      <option value="ar">🇸🇦 Arabic</option>
                    </optgroup>
                  </select>
                </div>
              </div>
            </div>

            <div className="messages-container">
              {/* Translation indicator */}
              {currentLanguage !== 'en' && (
                <div className="translation-indicator">
                  <span className="translation-icon">🌐</span>
                  <span className="translation-text">
                    Messages are being displayed in {
                      {
                        // Indian languages
                        'hi': 'Hindi 🇮🇳',
                        'mr': 'Marathi 🇮🇳',
                        'gu': 'Gujarati 🇮🇳',
                        'ta': 'Tamil 🇮🇳',
                        'te': 'Telugu 🇮🇳',
                        'bn': 'Bengali 🇮🇳',
                        'kn': 'Kannada 🇮🇳',
                        'ml': 'Malayalam 🇮🇳',
                        // Foreign languages
                        'es': 'Spanish 🇪🇸',
                        'fr': 'French 🇫🇷',
                        'de': 'German 🇩🇪',
                        'zh': 'Chinese 🇨🇳',
                        'ja': 'Japanese 🇯🇵',
                        'ru': 'Russian 🇷🇺',
                        'ar': 'Arabic 🇸🇦'
                      }[currentLanguage] || currentLanguage
                    }
                  </span>
                  {isTranslating && <span className="translation-loading">Translating...</span>}
                </div>
              )}

              {loading && messages.length === 0 ? (
                <div className="loading">Loading messages...</div>
              ) : error ? (
                <div className="error">{error}</div>
              ) : messages.length === 0 ? (
                <div className="no-messages">No messages yet</div>
              ) : (
                <>
                  <div className="messages-list">
                    {messages.map((message) => (
                      <div
                        key={message.id}
                        className={`message ${message.senderId === currentUser.id ? 'sent' : 'received'}`}
                      >
                        {message.senderId !== currentUser.id && (
                          <div className="message-avatar">
                            <img
                              src={getUserAvatar(message.sender)}
                              alt={getUserDisplayName(message.sender)}
                            />
                          </div>
                        )}
                        <div className="message-content">
                          {message.senderId !== currentUser.id && (
                            <div className="message-sender">{getUserDisplayName(message.sender)}</div>
                          )}
                          <div
                            className="message-text"
                            data-translated={currentLanguage !== 'en' && translatedMessages[message.id || `msg_${message.createdAt}_${message.senderId}`]?.[currentLanguage] ? "true" : "false"}
                          >
                            {getMessageContent(message)}
                          </div>
                          <div className="message-footer">
                            <div className="message-time">{formatTime(message.createdAt)}</div>
                            {currentLanguage === 'en' && (
                              <div className="message-translate-options">
                                <div className="message-translate-button" onClick={(e) => handleTranslateMessage(message, e)}>
                                  🌐 Translate
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                        {message.senderId === currentUser.id && (
                          <div className="message-avatar">
                            <img
                              src={getUserAvatar(message.sender)}
                              alt={getUserDisplayName(message.sender)}
                            />
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                  {typing && (
                    <div className="typing-indicator">
                      {getUserDisplayName(getOtherParticipant(activeConversation))} is typing...
                    </div>
                  )}
                  <div ref={messagesEndRef} />
                </>
              )}
            </div>

            <div className="message-input-container">
              <input
                type="text"
                className="message-input"
                placeholder="Type a message..."
                value={messageInput}
                onChange={handleMessageInputChange}
                onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
              />
              <button className="send-button" onClick={sendMessage}>
                Send
              </button>
            </div>
          </>
        ) : (
          <div className="no-conversation-selected">
            <div className="no-conversation-message">
              <h3>Select a conversation or start a new one</h3>
              <p>Use the search bar to find users and start chatting</p>
            </div>
          </div>
        )}
      </div>
      </div>
    </div>
  );
};

export default MessagesPage;

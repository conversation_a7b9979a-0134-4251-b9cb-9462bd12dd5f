/**
 * Test script to add a video with multiple languages
 * 
 * Run this script with Node.js:
 * node test-add-video-with-languages.js
 */

const fetch = require('node-fetch');

// Configuration
const API_URL = 'http://localhost:3003/api';
const AUTH_TOKEN = 'YOUR_AUTH_TOKEN'; // Replace with your actual auth token

// Video data with multiple languages
const videoData = {
  title: 'Test Video with Multiple Languages',
  description: 'This is a test video with multiple languages',
  channelId: 'YOUR_CHANNEL_ID', // Replace with your actual channel ID
  category: 'Education',
  videoId: 'XLcMq2', // Use a valid Engaxe ID
  languages: [
    {
      code: 'en',
      name: 'English',
      flag: '🇺🇸',
      isDefault: true,
      url: 'XLcMq2' // Use a valid Engaxe ID
    },
    {
      code: 'hi',
      name: 'Hindi',
      flag: '🇮🇳',
      isDefault: false,
      url: 'suZKhW' // Use a different valid Engaxe ID
    }
  ]
};

// Function to add a video
async function addVideo() {
  try {
    console.log('Adding video with multiple languages...');
    console.log('Video data:', JSON.stringify(videoData, null, 2));
    
    const response = await fetch(`${API_URL}/videos/engaxe`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${AUTH_TOKEN}`
      },
      body: JSON.stringify(videoData)
    });
    
    const data = await response.json();
    
    if (response.ok) {
      console.log('Video added successfully!');
      console.log('Response:', JSON.stringify(data, null, 2));
    } else {
      console.error('Failed to add video:', data);
    }
  } catch (error) {
    console.error('Error adding video:', error);
  }
}

// Run the function
addVideo();

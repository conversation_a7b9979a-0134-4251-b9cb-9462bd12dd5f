# Adding a Video with Multiple Languages

This guide will walk you through the process of adding a video with multiple languages to test the language dropdown functionality.

## Prerequisites

1. Make sure the server is running
2. Make sure you have a channel created

## Steps to Add a Video with Multiple Languages

1. Navigate to the Creator Studio
2. Click on "Add Video"
3. Fill in the video details:
   - **Title**: "Test Video with Multiple Languages"
   - **Description**: "This is a test video with multiple languages"
   - **Channel**: Select your channel
   - **Category**: Select any category
   - **Video ID**: Use a valid Engaxe ID (e.g., "XLcMq2")

4. Add the first language:
   - **Language**: English
   - **URL**: Use a valid Engaxe ID (e.g., "XLcMq2")
   - **Set as default**: Yes

5. Click "Add Language" to add a second language:
   - **Language**: Hindi
   - **URL**: Use a different valid Engaxe ID (e.g., "suZKhW")
   - **Set as default**: No

6. Click "Save Video"

## Verifying the Languages

1. Navigate to the Home page
2. Find your newly added video
3. Verify that it shows "2 languages" on the video card
4. Click on the video to open it
5. Check the browser console for language-related logs:
   - Look for "Languages in dropdown:" followed by the JSON representation of the languages array
   - Look for "Found X languages in video object:" followed by details of each language
6. Click on the language dropdown button
7. Verify that both languages (English and Hindi) appear in the dropdown
8. Click on the Hindi language option
9. Verify that the video source changes to the Hindi language URL
10. Click on the language dropdown button again
11. Verify that Hindi now has the dot indicator

## Troubleshooting

If the languages are not appearing in the dropdown:

1. Check the browser console for any errors
2. Verify that the languages array is being properly populated in the video object
3. Check the server logs to see if the languages are being properly saved to the database
4. Try refreshing the page to ensure the latest data is loaded

## Expected Results

- The video card should show "2 languages"
- The language dropdown should show both English and Hindi options
- Clicking on a language should change the video source to the corresponding URL
- The selected language should be indicated in the dropdown
